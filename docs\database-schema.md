# Database Schema Documentation

## Overview

MOUVIK uses a PostgreSQL database managed by Supabase. The schema is designed to support music creation, social features, and content management.

## Core Tables

### Users

The `users` table is managed by Supa<PERSON> Auth and extended with additional profile information.

```sql
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT, -- Kept for reference, but auth.users.email is the source of truth
    username TEXT UNIQUE NOT NULL,
    full_name TEXT,
    display_name TEXT,
    avatar_url TEXT,
    header_url TEXT,
    bio TEXT,
    website TEXT,
    -- location TEXT, -- Deprecated in favor of city/country
    location_city TEXT,
    location_country TEXT,
    social_links JSONB, -- Stores array of { platform: string, url: string }
    genres TEXT[],
    influences TEXT[],
    tags TEXT[],
    is_artist BOOLEAN DEFAULT false,
    record_label TEXT,
    equipment TEXT,
    spoken_languages TEXT[],
    instruments_played JSONB, -- Stores array of { name: string, experience_years: number }
    is_profile_public BOOLEAN DEFAULT true,
    show_stats_publicly BOOLEAN DEFAULT true,
    allow_collaboration_requests BOOLEAN DEFAULT true,
    receive_email_notifications BOOLEAN DEFAULT true,
    role_primary TEXT DEFAULT 'listener', -- e.g., 'musician', 'producer', 'songwriter', 'listener'
    roles_secondary TEXT[],
    -- years_active INT, -- Deprecated, experience captured in instruments_played
    subscription_tier TEXT DEFAULT 'free', -- e.g., 'free', 'pro', 'studio'
    user_role TEXT DEFAULT 'user', -- e.g., 'user', 'moderator', 'admin'
    status_badges TEXT[],
    main_instruments TEXT[],
    monetization_goals TEXT DEFAULT 'hobby', -- e.g., 'hobby', 'streaming', 'sync', 'teaching'
    open_to_collab BOOLEAN DEFAULT false,
    primary_daw TEXT, -- e.g., 'Ableton', 'FL', 'Logic', 'Reaper', 'Other'
    other_daws TEXT[],
    operating_systems TEXT[], -- New field for OS
    ai_usage_level TEXT DEFAULT 'none', -- e.g., 'none', 'light', 'moderate', 'heavy'
    ai_usage_percent NUMERIC, -- e.g., 0-100
    ai_tools TEXT[],
    coins_balance INT DEFAULT 0,
    ia_credits INT DEFAULT 0,
    marketing_opt_in BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Example RLS policies (review and adapt based on public visibility needs for new fields)
-- The existing policies for select/update by auth.uid() = id are a good base.
-- For public profiles, specific columns would be readable if is_profile_public = true.
-- This often involves creating a view (e.g., public_profiles_view) that exposes only permitted fields.

/* Example of a more granular RLS for public viewing (if not using a view):
CREATE POLICY "Public can view public profiles"
ON public.profiles
FOR SELECT
USING (is_profile_public = true);
*/

-- The existing policies are fine for owner-based access:
CREATE POLICY "Users can view their own profile"
ON public.profiles
FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
ON public.profiles
FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- It's good practice to enable RLS on the table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
```

### Bands

The `bands` table stores information about music bands.

```sql
CREATE TABLE public.bands (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    cover_url TEXT,
    creator_id UUID REFERENCES auth.users(id) NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

Exemple d'INSERT :
```sql
INSERT INTO public.bands (name, description, cover_url, creator_id) VALUES ('Nom du groupe', 'Description', 'https://...', 'user-uuid');
```

### Albums

The `albums` table stores collections of songs.

```sql
CREATE TABLE public.albums (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    genre TEXT[], -- Changed to array for multi-genre support, consistent with songs
    moods TEXT[], -- Added for categorization
    instrumentation TEXT[], -- Added for categorization
    album_type TEXT,
    release_date DATE,
    cover_url TEXT,
    status TEXT DEFAULT 'draft',
    is_explicit BOOLEAN DEFAULT false,
    label TEXT,
    upc TEXT,
    copyright TEXT,
    recording_year TEXT,
    language TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

Exemple d'INSERT :
```sql
INSERT INTO public.albums (user_id, title, description) VALUES ('user-uuid', 'Titre de l\'album', 'Description');
```

#### Policies RLS
```sql
CREATE POLICY "Public can view published albums"
ON public.albums
FOR SELECT
USING (status = 'published');

CREATE POLICY "Users can view their own albums"
ON public.albums
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can create albums"
ON public.albums
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own albums"
ON public.albums
FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own albums"
ON public.albums
FOR DELETE
USING (auth.uid() = user_id);
```

### Songs

The `songs` table stores information about individual music tracks. (Reflecting `db/schema.sql` and recent additions)

```sql
CREATE TABLE IF NOT EXISTS songs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  artist_name VARCHAR(255), -- Added based on form
  featured_artists TEXT[], -- Added based on form
  description TEXT,
  genre TEXT[], -- Supports multiple genres
  moods TEXT[], -- Supports multiple moods
  instrumentation TEXT[], -- Supports multiple instruments
  -- Note: For more extensive or structured tagging, consider using the generic 'resource_tags' table.
  album_id UUID REFERENCES albums(id) ON DELETE SET NULL,
  duration INTEGER, -- Consider NUMERIC if more precision needed
  cover_url TEXT, -- Note: Form uses image_path, might need consolidation
  image_path TEXT, -- Added based on form
  audio_url TEXT,
  waveform_url TEXT, -- Consider generating this
  lyrics TEXT,
  chords TEXT, -- Added based on potential future use
  bpm INTEGER,
  key VARCHAR(10),
  time_signature VARCHAR(10), -- Added based on form
  capo INTEGER, -- Added based on form
  tuning_frequency NUMERIC, -- Added based on form
  composer_name VARCHAR(255), -- Added based on form
  record_label VARCHAR(255), -- Added based on form
  distributor VARCHAR(255), -- Added based on form
  isrc VARCHAR(50), -- Added based on form
  upc VARCHAR(50), -- Added based on form
  status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  is_explicit BOOLEAN DEFAULT false, -- Added based on form
  is_ai_generated BOOLEAN DEFAULT FALSE, -- Keep this?
  plays INTEGER DEFAULT 0,
  release_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  -- Visibility flags
  is_description_public BOOLEAN DEFAULT false,
  is_lyrics_public BOOLEAN DEFAULT false,
  is_chords_public BOOLEAN DEFAULT false,
  is_bpm_public BOOLEAN DEFAULT false,
  is_key_public BOOLEAN DEFAULT false,
  is_album_public BOOLEAN DEFAULT false,
  is_composer_public BOOLEAN DEFAULT false,
  is_release_date_public BOOLEAN DEFAULT false,
  is_record_label_public BOOLEAN DEFAULT false,
  is_distributor_public BOOLEAN DEFAULT false,
  is_isrc_public BOOLEAN DEFAULT false,
  is_upc_public BOOLEAN DEFAULT false,
  are_stems_available_public BOOLEAN DEFAULT false, -- Replaces stems_available
  is_download_allowed_public BOOLEAN DEFAULT false, -- Replaces allow_downloads
  are_comments_allowed_public BOOLEAN DEFAULT false -- Replaces allow_comments
);

-- Note: Removed the old INSERT example as it's outdated.
```

#### Policies RLS (Example - Review and adapt as needed)
```sql
CREATE POLICY "Public can view published songs"
ON public.songs
FOR SELECT
USING (status = 'published');

CREATE POLICY "Users can view their own songs"
ON public.songs
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can create songs"
ON public.songs
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own songs"
ON public.songs
FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own songs"
ON public.songs
FOR DELETE
USING (auth.uid() = user_id);
```

### Album Songs

The `album_songs` table manages the many-to-many relationship between albums and songs.

```sql
CREATE TABLE public.album_songs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    album_id UUID REFERENCES public.albums(id) NOT NULL,
    song_id UUID REFERENCES public.songs(id) NOT NULL,
    track_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(album_id, song_id)
);

## Social Features Tables

### Follows

The `follows` table tracks user follow relationships.

```sql
CREATE TABLE public.follows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    follower_id UUID REFERENCES auth.users(id) NOT NULL,
    following_id UUID REFERENCES auth.users(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(follower_id, following_id),
    CONSTRAINT no_self_follow CHECK (follower_id != following_id)
);

### Likes

The `likes` table tracks user likes on various content types.

```sql
CREATE TABLE public.likes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    resource_id UUID NOT NULL,
    resource_type TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id, resource_id, resource_type)
);

### Comments

The `comments` table stores user comments on various content types.

```sql
CREATE TABLE public.comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    resource_id UUID NOT NULL,
    resource_type TEXT NOT NULL,
    content TEXT NOT NULL,
    parent_id UUID REFERENCES public.comments(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

## Analytics Tables

### Plays

The `plays` table tracks song play events.

```sql
CREATE TABLE public.plays (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    song_id UUID REFERENCES public.songs(id) NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

### Views

The `views` table tracks content view events.

```sql
CREATE TABLE public.views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_id UUID NOT NULL,
    resource_type TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

## Tagging System

### Tags

The `tags` table stores available tags.

```sql
CREATE TABLE public.tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

### Resource Tags

The `resource_tags` table associates tags with various content types.

```sql
CREATE TABLE public.resource_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tag_id UUID REFERENCES public.tags(id) NOT NULL,
    resource_id UUID NOT NULL,
    resource_type TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(tag_id, resource_id, resource_type)
);

## Entity Relationship Diagram

```mermaid
erDiagram
    USERS ||--o{ SONGS : creates
    USERS ||--o{ ALBUMS : creates
    USERS ||--o{ COMMENTS : writes
    USERS ||--o{ LIKES : gives
    USERS ||--o{ FOLLOWS : follows
    USERS ||--o{ PLAYS : plays
    USERS ||--o{ VIEWS : views
    
    SONGS }o--|| ALBUMS : belongs_to
    ALBUMS ||--o{ ALBUM_SONGS : contains
    SONGS ||--o{ ALBUM_SONGS : included_in
    
    SONGS ||--o{ COMMENTS : has
    ALBUMS ||--o{ COMMENTS : has
    
    SONGS ||--o{ LIKES : receives
    ALBUMS ||--o{ LIKES : receives
    
    SONGS ||--o{ PLAYS : played
    
    SONGS ||--o{ RESOURCE_TAGS : tagged_with
    ALBUMS ||--o{ RESOURCE_TAGS : tagged_with
    TAGS ||--o{ RESOURCE_TAGS : used_in
