"use client";

import { useEffect, useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation'; 
import { SupabaseClient } from '@supabase/supabase-js'; 
import { useForm, UseFormReturn, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from "@/hooks/use-toast"; 

import { Button } from "@/components/ui/button"; 
import { MultiSelect } from '@/components/ui/multi-select';
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { ScrollArea } from '@/components/ui/scroll-area'; 
import { Cog, Loader2 as LoadingSpinner, Music2, UploadCloud } from 'lucide-react'; 
// import SongVault from '@/components/song-vault'; // Uncomment if SongVault is used
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from '@/components/ui/switch'; 
import { DatePicker } from "@/components/ui/date-picker"; 
import Image from 'next/image';
import { AiConfigMenu } from "@/components/ia/ai-config-menu";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { AiQuickActions } from "@/components/ia/ai-quick-actions"; 
import AudioWaveformPreview from '@/components/audio-waveform-preview';
import { RichLyricsEditor } from "@/components/ui/rich-lyrics-editor";

// Constants
export const musicalKeys = [
  "C", "C#", "Db", "D", "D#", "Eb", "E", "F", "F#", "Gb", "G", "G#", "Ab", "A", "A#", "Bb", "B",
  "Am", "A#m", "Bbm", "Bm", "Cm", "C#m", "Dbm", "Dm", "D#m", "Ebm", "Em", "Fm", "F#m", "Gbm", "Gm", "G#m", "Abm"
];
export const timeSignatures = ["2/4", "3/4", "4/4", "5/4", "6/8", "7/8", "9/8", "12/8", "Autre"];
export const NO_ALBUM_SELECTED_VALUE = "__NO_ALBUM__";
export const LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY = "ai_selected_provider_mouvik";
export const LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY = "ollama_selected_model_mouvik";
export const LOCAL_STORAGE_OPENAI_SELECTED_MODEL_KEY = "openai_selected_model_mouvik";
export const LOCAL_STORAGE_OPENROUTER_SELECTED_MODEL_KEY = "openrouter_selected_model_mouvik";
export const LOCAL_STORAGE_ANTHROPIC_SELECTED_MODEL_KEY = "anthropic_selected_model_mouvik";

// Custom Types for AI features
export interface AiConfig {
  provider: 'ollama' | 'openai' | 'openrouter' | 'anthropic' | string;
  model: string;
  temperature: number;
}

export interface AiHistoryItem {
  role: 'user' | 'assistant';
  content: string;
}

// Zod Schema
export const songFormSchema = z.object({
  title: z.string().min(1, { message: "Le titre est requis." }),
  artist_name: z.string().optional(),
  featured_artists: z.array(z.string()).optional(),
  genre: z.array(z.string()).optional(), 
  moods: z.array(z.string()).optional(),
  instrumentation: z.array(z.string()).optional(),
  key: z.string().optional(),
  bpm: z.number().min(0).nullable().optional(),
  time_signature: z.string().optional(),
  capo: z.number().min(0).nullable().optional(),
  tuning_frequency: z.number().min(0).nullable().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  audio_url: z.string().optional().nullable(),
  cover_url: z.string().optional().nullable(),
  album_id: z.string().nullable().optional(),
  composer_name: z.string().optional(),
  release_date: z.date().optional().nullable(),
  record_label: z.string().optional(),
  distributor: z.string().optional(),
  isrc: z.string().optional(),
  upc: z.string().optional(),
  is_explicit: z.boolean().default(false).optional(),
  stems_available: z.boolean().default(false).optional(),
  allow_downloads: z.boolean().default(false).optional(),
  allow_comments: z.boolean().default(true).optional(),
  lyrics: z.string().optional(),
  bloc_note: z.string().optional(),
  right_column_notepad: z.string().optional(),
  status: z.enum(['draft', 'published']).optional(), 
});
export type SongFormValues = z.infer<typeof songFormSchema>;

// Interfaces
export interface Album {
  id: string;
  title: string;
}

interface SongFormProps {
  mode: 'create' | 'edit';
  initialValues?: Partial<SongFormValues>;
  onFormSubmit: (data: SongFormValues, status?: 'draft' | 'published') => Promise<void>;
  isSubmitting: boolean;
  albums: Album[];
  isLoadingAlbums: boolean;
  supabaseClient: SupabaseClient;
}

// Component
export function SongForm({
  mode,
  initialValues,
  onFormSubmit,
  isSubmitting,
  albums,
  isLoadingAlbums,
  supabaseClient,
}: SongFormProps) {
  const router = useRouter();

  const form = useForm<SongFormValues>({
    resolver: zodResolver(songFormSchema),
    defaultValues: mode === 'create' ? {
      title: "", artist_name: "", featured_artists: [], genre: [], moods: [], instrumentation: [], key: "",
      bpm: undefined, time_signature: "", capo: undefined, tuning_frequency: 440, description: "", tags: [],
      audio_url: null, cover_url: null, album_id: null, composer_name: "", release_date: null,
      record_label: "", distributor: "", isrc: "", upc: "", is_explicit: false, stems_available: false,
      allow_downloads: false, allow_comments: true, lyrics: "", bloc_note: "", right_column_notepad: "", status: 'draft'
    } : { ...initialValues, release_date: initialValues?.release_date ? new Date(initialValues.release_date) : null }, 
    mode: 'onChange',
  });

  const [uploadingAudio, setUploadingAudio] = useState(false);
  const [uploadedAudioUrl, setUploadedAudioUrl] = useState<string | null>(initialValues?.audio_url || null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(initialValues?.cover_url || null);
  
  const audioFileInputRef = useRef<HTMLInputElement>(null);
  const imageFileInputRef = useRef<HTMLInputElement>(null);

  const [lyricsContent, setLyricsContent] = useState<string>(initialValues?.lyrics || "");
  const [aiConfig, setAiConfig] = useState<AiConfig>({
    provider: 'ollama', model: '', temperature: 0.7,
  });
  const [aiLoading, setAiLoading] = useState(false);
  const [aiLastResult, setAiLastResult] = useState<string | undefined>(undefined);
  const [aiError, setAiError] = useState<string | undefined>(undefined);
  const [aiHistory, setAiHistory] = useState<AiHistoryItem[]>([]);
  const [aiMaxOutputTokens, setAiMaxOutputTokens] = useState<number>(256);
  const [aiGeneralPrompt, setAiGeneralPrompt] = useState<string>("You are a helpful assistant for songwriting.");
  const [showAiConfigMenu, setShowAiConfigMenu] = useState(false);

  const handleAiCorrect = () => {
    // TODO: Implement AI text correction logic
    toast({ title: "Correction IA", description: "Fonctionnalité de correction à implémenter." });
  };

  const handleAiTranslate = (lang: string) => {
    // TODO: Implement AI text translation logic
    toast({ title: "Traduction IA", description: `Traduction vers ${lang} à implémenter.` });
  };

  const handleAiConfigChange = (newPartialConfig: Partial<AiConfig>) => {
    setAiConfig(prevConfig => ({ ...prevConfig, ...newPartialConfig }));
  };

  const stripHtml = (html: string): string => {
    if (typeof document !== "undefined") {
      const doc = new DOMParser().parseFromString(html, 'text/html');
      return doc.body.textContent || "";
    }
    return html.replace(/<[^>]+>/g, '')
  };

  useEffect(() => {
    if (mode === 'edit' && initialValues) {
      const valuesToReset = {
        ...initialValues,
        release_date: initialValues.release_date ? new Date(initialValues.release_date) : null,
      };
      form.reset(valuesToReset);
      setUploadedAudioUrl(initialValues.audio_url || null);
      setUploadedImageUrl(initialValues.cover_url || null);
      setLyricsContent(initialValues.lyrics || "");
    }
  }, [mode, initialValues, form]);

  useEffect(() => {
    const savedProvider = typeof window !== 'undefined' ? localStorage.getItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY) || 'ollama' : 'ollama';
    let savedModel = '';
    if (typeof window !== 'undefined'){
      if (savedProvider === 'ollama') savedModel = localStorage.getItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY) || '';
      else if (savedProvider === 'openai') savedModel = localStorage.getItem(LOCAL_STORAGE_OPENAI_SELECTED_MODEL_KEY) || '';
      else if (savedProvider === 'openrouter') savedModel = localStorage.getItem(LOCAL_STORAGE_OPENROUTER_SELECTED_MODEL_KEY) || '';
      else if (savedProvider === 'anthropic') savedModel = localStorage.getItem(LOCAL_STORAGE_ANTHROPIC_SELECTED_MODEL_KEY) || '';
    }
    setAiConfig((prev: AiConfig) => ({ ...prev, provider: savedProvider as AiConfig['provider'], model: savedModel }));
  }, []);

  const handleLyricsChange = (newContent: string) => {
    setLyricsContent(newContent);
    form.setValue('lyrics', newContent, { shouldValidate: true, shouldDirty: true });
  };

  const handleFileUpload = async (file: File, type: 'audio' | 'image') => {
    if (!file) return;
    const fileExt = file.name.split('.').pop();
    const fileName = `${Math.random()}.${fileExt}`;
    const bucketName = type === 'audio' ? 'audio' : 'covers';
    const filePath = `${fileName}`;

    if (type === 'audio') setUploadingAudio(true);
    if (type === 'image') setUploadingImage(true);

    try {
      const { error: uploadError } = await supabaseClient.storage.from(bucketName).upload(filePath, file);
      if (uploadError) throw uploadError;
      const { data: publicURLData } = supabaseClient.storage.from(bucketName).getPublicUrl(filePath);
      if (!publicURLData || !publicURLData.publicUrl) throw new Error('Could not get public URL');

      if (type === 'audio') {
        form.setValue('audio_url', publicURLData.publicUrl, { shouldValidate: true, shouldDirty: true });
        setUploadedAudioUrl(publicURLData.publicUrl);
        toast({ title: "Fichier audio téléversé" });
      } else if (type === 'image') {
        form.setValue('cover_url', publicURLData.publicUrl, { shouldValidate: true, shouldDirty: true });
        setUploadedImageUrl(publicURLData.publicUrl);
        toast({ title: "Image de couverture téléversée" });
      }
    } catch (error: any) {
      toast({ title: `Erreur upload ${type}`, description: error.message, variant: "destructive" });
    } finally {
      if (type === 'audio') setUploadingAudio(false);
      if (type === 'image') setUploadingImage(false);
    }
  };

  const onSubmitWithStatus = (status?: 'draft' | 'published') => {
    if (status) {
        form.setValue('status', status, { shouldValidate: true, shouldDirty: true });
    }
    form.handleSubmit((data) => onFormSubmit(data, status ?? data.status))();
  };
  
  const handleAiGenerate = async (prompt?: string) => { 
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    const currentLyricsText = stripHtml(lyricsContent);
    const fullPrompt = `${aiGeneralPrompt}\n\n${prompt || 'Continue writing lyrics based on this text'}:\n\n${currentLyricsText}`;
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      const result = `AI Generated text for prompt: '${prompt || 'continue'}'. Current text was: ${currentLyricsText.substring(0,50)}...`;
      
      setAiLastResult(result);
      const newLyrics = lyricsContent + (lyricsContent ? '\n' : '') + result;
      setLyricsContent(newLyrics);
      form.setValue('lyrics', newLyrics, { shouldValidate: true, shouldDirty: true });
      setAiHistory((prev: AiHistoryItem[]) => [...prev, {role: 'user', content: fullPrompt}, {role: 'assistant', content: result}]);
      toast({ title: "IA: Texte généré!"});
    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de génération", description: e.message, variant: "destructive"});
    }
    finally { setAiLoading(false); }
  }

  return (
    <form className="h-full flex flex-col" onSubmit={form.handleSubmit(data => onFormSubmit(data))}>
        {/* Header Section - Copied from previous attempt */}
        {/* --- Nouveau HEADER HERO FULL WIDTH --- */}
        <div className="w-full bg-background/90 py-6 px-0 md:px-4 flex flex-col md:flex-row md:items-center gap-8 border-b mb-8 max-w-none">
          <div className="flex flex-col items-center md:items-start gap-4 w-full md:w-1/4">
            {/* Image de couverture */}
            <div className="w-full flex flex-col items-center">
              <FormField
                control={form.control}
                name="cover_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm">Image de couverture</FormLabel>
                    <FormControl>
                      <div className="flex flex-col items-center gap-2">
                        <Input
                          type="file"
                          accept="image/*"
                          ref={imageFileInputRef}
                          onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'image')}
                          className="hidden"
                          id="image-upload"
                        />
                        <Button type="button" variant="outline" size="sm" onClick={() => imageFileInputRef.current?.click()} disabled={uploadingImage || isSubmitting}>
                          {uploadingImage ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                          {uploadedImageUrl ? 'Changer' : 'Choisir une image'}
                        </Button>
                        {uploadedImageUrl && uploadedImageUrl.startsWith('http') && (
                          <Image src={uploadedImageUrl} alt="Preview" width={180} height={180} className="rounded-md object-cover mt-2" />
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex-1 flex flex-col items-center md:items-start gap-4 w-full">
            {/* Titre du morceau */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="sr-only">Titre du morceau</FormLabel>
                  <FormControl>
                    <Input className="text-3xl md:text-4xl font-bold w-full bg-transparent border-none shadow-none px-0 mb-2" placeholder="Titre du morceau *" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Affiche l'artiste principal sous le titre */}
            {(form.watch('artist_name') ?? '').trim() !== '' && (
              <div className="flex items-center mt-1 mb-2">
                <span className="px-3 py-1 rounded-full bg-secondary text-base font-medium text-foreground/80 shadow-sm">
                  {form.watch('artist_name')}
                </span>
              </div>
            )}
            {/* Audio/Waveform */}
            <FormField
              control={form.control}
              name="audio_url"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="text-sm">Fichier audio</FormLabel>
                  <FormControl>
                    <div className="flex flex-col gap-2">
                      <Input
                        type="file"
                        accept="audio/*"
                        ref={audioFileInputRef}
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'audio')}
                        className="hidden"
                        id="audio-upload"
                      />
                      <Button type="button" variant="outline" size="sm" onClick={() => audioFileInputRef.current?.click()} disabled={uploadingAudio || isSubmitting}>
                        {uploadingAudio ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                        {uploadedAudioUrl ? 'Changer' : 'Choisir un fichier audio'}
                      </Button>
                      {uploadedAudioUrl && uploadedAudioUrl.startsWith('http') && (
                        <AudioWaveformPreview audioUrl={uploadedAudioUrl} />
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Boutons action */}
            <div className="flex gap-2 mt-4">
              <Button variant="outline" onClick={() => router.back()} disabled={isSubmitting || uploadingAudio || uploadingImage}>Annuler</Button>
              {mode === 'create' ? (
                <>
                  <Button type="button" onClick={() => onSubmitWithStatus('draft')} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="secondary">
                    {(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}
                    Enregistrer en Brouillon
                  </Button>
                  <Button type="button" onClick={() => onSubmitWithStatus('published')} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg">
                    {(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}
                    Publier
                  </Button>
                </>
              ) : (
                <Button type="button" onClick={() => onSubmitWithStatus(form.getValues('status'))} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg">
                  {(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}
                  Sauvegarder
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* --- FIN HEADER HERO --- */}

        {/* Main Content Area, Full Width */}
        <ScrollArea className="flex-grow pb-6">
          <div className="w-full px-0 md:px-2 space-y-6">
            <Tabs defaultValue="general" className="w-full">
              <TabsList className="grid w-full grid-cols-4 md:grid-cols-4 mb-4">
                <TabsTrigger value="general">Général</TabsTrigger>
                <TabsTrigger value="media">Media</TabsTrigger>
                <TabsTrigger value="lyrics-ia">Paroles / Copo IA</TabsTrigger>
                <TabsTrigger value="distribution">Distribution</TabsTrigger>
              </TabsList>

              {/* General Tab */}
              <TabsContent value="general" className="w-full">
                <div className="space-y-4 w-full">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Titre du morceau *</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: Mon Super Hit" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="artist_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Artiste principal *</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: Nom de l'artiste" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="featured_artists"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Artistes en featuring</FormLabel>
                          <FormControl>
                            <Input placeholder="Ajouter des artistes en featuring (séparés par des virgules)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={form.control}
                        name="genre"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Genre</FormLabel>
                            <FormControl>
                              <MultiSelect
                                options={[
                                  { value: "pop", label: "Pop" },
                                  { value: "rock", label: "Rock" },
                                  { value: "rap", label: "Rap" },
                                  { value: "rnb", label: "R&B" },
                                  { value: "electro", label: "Electro" },
                                  { value: "chanson", label: "Chanson" },
                                  { value: "jazz", label: "Jazz" },
                                  { value: "afro", label: "Afro" },
                                  { value: "zouk", label: "Zouk" },
                                  { value: "disco", label: "Disco" },
                                  { value: "reggae", label: "Reggae" },
                                  { value: "metal", label: "Metal" },
                                  { value: "blues", label: "Blues" },
                                  { value: "world", label: "World" },
                                  { value: "funk", label: "Funk" },
                                  { value: "soul", label: "Soul" },
                                  { value: "trap", label: "Trap" },
                                  { value: "drill", label: "Drill" },
                                  { value: "classique", label: "Classique" },
                                  { value: "folk", label: "Folk" },
                                  { value: "country", label: "Country" },
                                  { value: "latino", label: "Latino" },
                                  { value: "house", label: "House" },
                                  { value: "techno", label: "Techno" },
                                  { value: "trance", label: "Trance" },
                                  { value: "kpop", label: "K-Pop" },
                                  { value: "reggaeton", label: "Reggaeton" },
                                  { value: "gospel", label: "Gospel" },
                                  { value: "salsa", label: "Salsa" },
                                  { value: "bossa", label: "Bossa Nova" },
                                  { value: "autre", label: "Autre" },
                                ]}
                                selected={field.value || []}
                                onChange={field.onChange}
                                placeholder="Ajouter un ou plusieurs genres"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <FormField
                        control={form.control}
                        name="moods"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Ambiances / Moods</FormLabel>
                            <FormControl>
                              <MultiSelect
                                options={[
                                  { value: "joyeux", label: "Joyeux" },
                                  { value: "triste", label: "Triste" },
                                  { value: "motivant", label: "Motivant" },
                                  { value: "nostalgique", label: "Nostalgique" },
                                  { value: "énergique", label: "Énergique" },
                                  { value: "calme", label: "Calme" },
                                  { value: "romantique", label: "Romantique" },
                                  { value: "sombre", label: "Sombre" },
                                  { value: "épique", label: "Épique" },
                                  { value: "relax", label: "Relax" },
                                  { value: "mélancolique", label: "Mélancolique" },
                                  { value: "festif", label: "Festif" },
                                  { value: "planant", label: "Planant" },
                                  { value: "mystérieux", label: "Mystérieux" },
                                  { value: "cinématique", label: "Cinématique" },
                                  { value: "groovy", label: "Groovy" },
                                  { value: "hype", label: "Hype" },
                                  { value: "sensuel", label: "Sensuel" },
                                  { value: "dramatique", label: "Dramatique" },
                                  { value: "autre", label: "Autre" },
                                ]}
                                selected={field.value || []}
                                onChange={field.onChange}
                                placeholder="Ajouter des ambiances (moods)"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                     <FormField
                        control={form.control}
                        name="tags"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tags</FormLabel>
                            <FormControl>
                              <MultiSelect
                                options={[
                                  { value: "summer", label: "Summer" },
                                  { value: "love", label: "Love" },
                                  { value: "chill", label: "Chill" },
                                  { value: "party", label: "Party" },
                                  { value: "workout", label: "Workout" },
                                  { value: "viral", label: "Viral" },
                                  { value: "tiktok", label: "TikTok" },
                                  { value: "club", label: "Club" },
                                  { value: "oldschool", label: "Old School" },
                                  { value: "sad", label: "Sad" },
                                  { value: "happy", label: "Happy" },
                                  { value: "motivation", label: "Motivation" },
                                  { value: "travel", label: "Travel" },
                                  { value: "nostalgia", label: "Nostalgia" },
                                  { value: "melody", label: "Melody" },
                                  { value: "anthem", label: "Anthem" },
                                  { value: "radio", label: "Radio" },
                                  { value: "instrumental", label: "Instrumental" },
                                  { value: "dance", label: "Dance" },
                                  { value: "street", label: "Street" },
                                  { value: "underground", label: "Underground" },
                                  { value: "mainstream", label: "Mainstream" },
                                  { value: "autre", label: "Autre" },
                                ]}
                                selected={field.value || []}
                                onChange={field.onChange}
                                placeholder="Ajouter des tags"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormField
                          control={form.control}
                          name="key"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Tonalité</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger><SelectValue placeholder="Sélectionner une tonalité" /></SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {musicalKeys.map(k => <SelectItem key={k} value={k}>{k}</SelectItem>)}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="bpm"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>BPM</FormLabel>
                              <FormControl>
                                <Input type="number" placeholder="120" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))} value={field.value ?? ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="time_signature"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Signature Rythmique</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger><SelectValue placeholder="Sélectionner une signature" /></SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {timeSignatures.map(ts => <SelectItem key={ts} value={ts}>{ts}</SelectItem>)}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="capo"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Capo</FormLabel>
                              <FormControl>
                                <Input type="number" placeholder="0" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))} value={field.value ?? ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="tuning_frequency"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Fréquence d'accordage (Hz)</FormLabel>
                              <FormControl>
                                <Input type="number" placeholder="440" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))} value={field.value ?? ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="composer_name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Nom du compositeur</FormLabel>
                              <FormControl>
                                <Input placeholder="Ex: Jean Dupont" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
              </TabsContent>

              {/* Media Tab */}
              <TabsContent value="media" className="w-full">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
                  <Card>
                    <CardHeader>
                      <CardTitle>Fichier Audio</CardTitle>
                      <CardDescription>Téléversez le fichier audio principal de votre morceau (MP3, WAV, etc.).</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="audio_url"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Fichier audio</FormLabel>
                            <FormControl>
                              <div>
                                <Input
                                  type="file"
                                  accept="audio/*"
                                  ref={audioFileInputRef}
                                  onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'audio')}
                                  className="hidden"
                                  id="audio-upload"
                                />
                                <Button type="button" variant="outline" onClick={() => audioFileInputRef.current?.click()} disabled={uploadingAudio || isSubmitting}>
                                  {uploadingAudio ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                                  Choisir un fichier audio
                                </Button>
                              </div>
                            </FormControl>
                            {uploadingAudio && <p className='text-sm text-muted-foreground'>Téléversement en cours...</p>}
                            {uploadedAudioUrl && (
                              <div className="mt-4">
                                <p className="text-sm font-medium">Fichier actuel:</p>
                                {/* <a href={uploadedAudioUrl} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-500 hover:underline">{uploadedAudioUrl.split('/').pop()}</a> */}
                                <AudioWaveformPreview audioUrl={uploadedAudioUrl} />
                              </div>
                            )}
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle>Image de Couverture</CardTitle>
                      <CardDescription>Ajoutez une image pour représenter votre morceau (JPEG, PNG, GIF).</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="cover_url"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Image de couverture</FormLabel>
                            <FormControl>
                              <div>
                                <Input
                                  type="file"
                                  accept="image/*"
                                  ref={imageFileInputRef}
                                  onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'image')}
                                  className="hidden"
                                  id="image-upload"
                                />
                                <Button type="button" variant="outline" onClick={() => imageFileInputRef.current?.click()} disabled={uploadingImage || isSubmitting}>
                                  {uploadingImage ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                                  Choisir une image
                                </Button>
                              </div>
                            </FormControl>
                            {uploadingImage && <p className='text-sm text-muted-foreground'>Téléversement en cours...</p>}
                            {uploadedImageUrl && (
                              <div className="mt-4">
                                <p className="text-sm font-medium">Image actuelle:</p>
                                <Image src={uploadedImageUrl} alt="Preview" width={150} height={150} className="rounded-md object-cover" />
                              </div>
                            )}
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Onglet Paroles / Copo IA */}
              <TabsContent value="lyrics-ia" className="w-full p-0">
                <div className="w-full p-0">
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle>Paroles & Assistant IA</CardTitle>
                      <CardDescription>Écrivez ou générez les paroles de votre morceau avec l'aide de l'IA.</CardDescription>
                    </div>
                    <Button type="button" variant="ghost" size="icon" onClick={() => setShowAiConfigMenu(true)} title="Configurer l'IA">
                      <Cog className="w-5 h-5" />
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="lyrics"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Paroles</FormLabel>
                          <FormControl>
                            <RichLyricsEditor
                              value={lyricsContent}
                              onChange={handleLyricsChange}
                              placeholder="Commencez à écrire les paroles ici..."
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <AiQuickActions 
                      onGenerate={handleAiGenerate}
                      loading={aiLoading} 
                      lastResult={aiLastResult}
                      error={aiError}
                      iaHistory={aiHistory}
                      aiConfig={aiConfig}
                      setAiConfig={handleAiConfigChange}
                      onCorrect={handleAiCorrect}
                      onTranslate={handleAiTranslate}
                      generalPrompt={aiGeneralPrompt} 
                      onEditGeneralPrompt={setAiGeneralPrompt}
                    />
                    <AiConfigMenu
                      isPopoverOpen={showAiConfigMenu}
                      setIsPopoverOpen={setShowAiConfigMenu}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Distribution Tab */}
              <TabsContent value="distribution" className="w-full">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                  <div>
                    <div className="font-bold text-lg mb-2">Distribution & Publication</div>
                    <div className="text-muted-foreground mb-4">Informations relatives à la distribution et aux droits.</div>
                    <FormField
                      control={form.control}
                      name="album_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Album</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value || NO_ALBUM_SELECTED_VALUE}>
                            <FormControl>
                              <SelectTrigger disabled={isLoadingAlbums}>
                                <SelectValue placeholder={isLoadingAlbums ? "Chargement des albums..." : "Sélectionner un album (optionnel)"} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value={NO_ALBUM_SELECTED_VALUE}>Aucun album</SelectItem>
                              {albums.map((album) => (
                                <SelectItem key={album.id} value={album.id}>
                                  {album.title}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="release_date"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Date de sortie</FormLabel>
                          <DatePicker date={field.value ?? undefined} onSelect={(date: Date | undefined) => field.onChange(date)} />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="record_label"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Label</FormLabel>
                          <FormControl>
                            <Input placeholder="Mon Label Indépendant" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="distributor"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Distributeur</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: DistroKid, TuneCore" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="isrc"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ISRC</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: US-S1Z-99-00001" {...field} />
                          </FormControl>
                          <FormDescription>Code ISRC pour l'identification de l'enregistrement.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="upc"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>UPC/EAN</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: 123456789012" {...field} />
                          </FormControl>
                          <FormDescription>Code-barres du produit.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </TabsContent>

              {/* Permissions Tab */}
              <TabsContent value="permissions">
                <Card>
                  <CardHeader>
                    <CardTitle>Permissions & Options</CardTitle>
                    <CardDescription>Gérez les options de votre morceau.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="is_explicit"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Contenu explicite</FormLabel>
                            <FormDescription>Indique si le morceau contient des paroles explicites.</FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="stems_available"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Pistes séparées (Stems) disponibles</FormLabel>
                            <FormDescription>Indique si les pistes instrumentales séparées sont disponibles.</FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="allow_downloads"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Autoriser les téléchargements</FormLabel>
                            <FormDescription>Permet aux utilisateurs de télécharger le morceau.</FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="allow_comments"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Autoriser les commentaires</FormLabel>
                            <FormDescription>Permet aux utilisateurs de commenter le morceau.</FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
                 <Card className="mt-6">
                  <CardHeader>
                    <CardTitle>Bloc-notes & Notes Personnelles</CardTitle>
                    <CardDescription>Espace pour vos notes privées sur ce morceau.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="bloc_note"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bloc-notes principal</FormLabel>
                          <FormControl>
                            <ScrollArea className="h-72 w-full rounded-md border p-2">
                              <Textarea
                                placeholder="Notes sur la composition, l'enregistrement, idées..."
                                className="min-h-[250px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-1"
                                {...field}
                              />
                            </ScrollArea>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="right_column_notepad"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bloc-notes (colonne de droite)</FormLabel>
                          <FormControl>
                            <ScrollArea className="h-72 w-full rounded-md border p-2">
                              <Textarea
                                placeholder="Notes additionnelles, visibles dans la colonne de droite de la page du morceau."
                                className="min-h-[250px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-1"
                                {...field}
                              />
                            </ScrollArea>
                          </FormControl>
                          <FormDescription>
                            Ce bloc-notes sera affiché dans la colonne de droite sur la page de détail du morceau (si le thème le supporte).
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent> 
            </Tabs>
          </div>
        </ScrollArea>
      </form>
  );
}

export default SongForm;
