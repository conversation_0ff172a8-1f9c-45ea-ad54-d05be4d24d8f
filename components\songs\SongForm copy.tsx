"use client";

import { useEffect, useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation'; 
import { SupabaseClient } from '@supabase/supabase-js'; 
// Import Control from react-hook-form
import { useForm, UseFormReturn, FormProvider, Control } from 'react-hook-form'; 
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from "@/hooks/use-toast"; 

import { Button } from "@/components/ui/button"; 
import { MultiSelect } from '@/components/ui/multi-select';
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { ScrollArea } from '@/components/ui/scroll-area'; 
import { Cog, Loader2 as LoadingSpinner, Music2, UploadCloud, History, ChevronDown, ChevronUp, Eye, EyeOff } from 'lucide-react'; // Keep icons
// import SongVault from '@/components/song-vault'; // Uncomment if SongVault is used
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from '@/components/ui/switch'; 
import { DatePicker } from "@/components/ui/date-picker"; 
import Image from 'next/image';
import { AiConfigMenu } from "@/components/ia/ai-config-menu";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
// AiQuickActions is now only used within AiAssistantPanel
import AudioWaveformPreview from '@/components/audio-waveform-preview';
import { LyricsEditorWithAI } from './LyricsEditorWithAI';
import { AiAssistantPanel } from './AiAssistantPanel'; 
import { VisibilityToggle } from "@/components/ui/visibility-toggle"; // Import VisibilityToggle
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import type Quill from 'quill';
import Link from 'next/link'; // Import Link for preview button

// Constants
export const musicalKeys = [
  "C", "C#", "Db", "D", "D#", "Eb", "E", "F", "F#", "Gb", "G", "G#", "Ab", "A", "A#", "Bb", "B",
  "Am", "A#m", "Bbm", "Bm", "Cm", "C#m", "Dbm", "Dm", "D#m", "Ebm", "Em", "Fm", "F#m", "Gbm", "Gm", "G#m", "Abm"
];
export const timeSignatures = ["2/4", "3/4", "4/4", "5/4", "6/8", "7/8", "9/8", "12/8", "Autre"];
export const NO_ALBUM_SELECTED_VALUE = "__NO_ALBUM__";
export const LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY = "ai_selected_provider_mouvik";
export const LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY = "ollama_selected_model_mouvik";
export const LOCAL_STORAGE_OPENAI_SELECTED_MODEL_KEY = "openai_selected_model_mouvik";
export const LOCAL_STORAGE_OPENROUTER_SELECTED_MODEL_KEY = "openrouter_selected_model_mouvik";
export const LOCAL_STORAGE_ANTHROPIC_SELECTED_MODEL_KEY = "anthropic_selected_model_mouvik";

// Expanded Options for MultiSelects - Restoring definitions
const genreOptions = [
  { value: "pop", label: "Pop" }, { value: "rock", label: "Rock" }, { value: "rap", label: "Rap/Hip-Hop" },
  { value: "rnb", label: "R&B/Soul" }, { value: "electro", label: "Electro/Dance" }, { value: "chanson", label: "Chanson Française" },
  { value: "jazz", label: "Jazz" }, { value: "blues", label: "Blues" }, { value: "reggae", label: "Reggae/Dancehall" },
  { value: "metal", label: "Metal" }, { value: "punk", label: "Punk" }, { value: "funk", label: "Funk/Disco" },
  { value: "classique", label: "Classique" }, { value: "folk", label: "Folk/Acoustique" }, { value: "country", label: "Country" },
  { value: "latino", label: "Latino (Salsa, Bachata...)" }, { value: "world", label: "World Music" }, { value: "afro", label: "Afro (Beat, Pop...)" },
  { value: "ambient", label: "Ambient/Experimental" }, { value: "soundtrack", label: "Musique de Film/Soundtrack" }, { value: "autre", label: "Autre" },
];
const moodOptions = [
  { value: "joyeux", label: "Joyeux/Optimiste" }, { value: "triste", label: "Triste/Mélancolique" }, { value: "énergique", label: "Énergique/Puissant" },
  { value: "calme", label: "Calme/Relaxant" }, { value: "romantique", label: "Romantique/Amoureux" }, { value: "sombre", label: "Sombre/Introspectif" },
  { value: "épique", label: "Épique/Grandios" }, { value: "planant", label: "Planant/Aérien" }, { value: "mystérieux", label: "Mystérieux/Intrigant" },
  { value: "festif", label: "Festif/Dansant" }, { value: "agressif", label: "Agressif/Rageur" }, { value: "sensuel", label: "Sensuel/Séduisant" },
  { value: "inspirant", label: "Inspirant/Motivant" }, { value: "nostalgique", label: "Nostalgique" }, { value: "reveur", label: "Rêveur/Onirique" },
  { value: "autre", label: "Autre" },
];
const tagOptions = [
  { value: "summer", label: "Summer/Été" }, { value: "love", label: "Love/Amour" }, { value: "chill", label: "Chill/Détente" },
  { value: "party", label: "Party/Fête" }, { value: "workout", label: "Workout/Sport" }, { value: "roadtrip", label: "Road Trip" },
  { value: "focus", label: "Focus/Concentration" }, { value: "sleep", label: "Sleep/Sommeil" }, { value: "vintage", label: "Vintage/Rétro" },
  { value: "futuriste", label: "Futuriste" }, { value: "urbain", label: "Urbain/Street" }, { value: "nature", label: "Nature" },
  { value: "politique", label: "Politique/Engagé" }, { value: "humour", label: "Humour" }, { value: "instrumental", label: "Instrumental" },
  { value: "autre", label: "Autre" },
];
const instrumentationOptions = [
  { value: "piano", label: "Piano" }, { value: "guitare-acoustique", label: "Guitare Acoustique" }, { value: "guitare-electrique", label: "Guitare Electrique" },
  { value: "basse", label: "Basse" }, { value: "batterie", label: "Batterie" }, { value: "voix", label: "Voix Lead" },
  { value: "choeurs", label: "Chœurs/Backing Vocals" }, { value: "synthetiseur", label: "Synthétiseur" }, { value: "boite-a-rythmes", label: "Boîte à rythmes/Beat" },
  { value: "cordes", label: "Cordes (Violon, Violoncelle...)" }, { value: "cuivres", label: "Cuivres (Trompette, Sax...)" }, { value: "vents", label: "Vents (Flûte, Clarinette...)" },
  { value: "percussions", label: "Percussions (Congas, Bongos...)" }, { value: "orchestre", label: "Orchestre" }, { value: "samples", label: "Samples/Loops" },
  { value: "autre", label: "Autre" },
];


// Custom Types for AI features
export interface AiConfig {
  provider: 'ollama' | 'openai' | 'openrouter' | 'anthropic' | string;
  model: string;
  temperature: number;
}

export interface AiHistoryItem {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: Date; // Optional timestamp
}

// Zod Schema - Updated with visibility flags
export const songFormSchema = z.object({
  title: z.string().min(1, { message: "Le titre est requis." }),
  artist_name: z.string().min(1, { message: "Le nom de l'artiste principal est requis." }), 
  featured_artists: z.array(z.string()).optional(),
  genre: z.array(z.string()).optional(), 
  moods: z.array(z.string()).optional(),
  instrumentation: z.array(z.string()).optional(),
  key: z.string().optional(),
  bpm: z.number().min(0).nullable().optional(),
  time_signature: z.string().optional(),
  capo: z.number().min(0).nullable().optional(),
  tuning_frequency: z.number().min(0).nullable().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  audio_url: z.string().optional().nullable(), 
  image_path: z.string().optional().nullable(), 
  album_id: z.string().nullable().optional(),
  composer_name: z.string().optional(),
  release_date: z.date().optional().nullable(),
  record_label: z.string().optional(),
  distributor: z.string().optional(),
  isrc: z.string().optional(),
  upc: z.string().optional(),
  is_explicit: z.boolean().default(false).optional(), // Content rating, likely always public if used
  lyrics: z.string().optional(),
  bloc_note: z.string().optional(),
  right_column_notepad: z.string().optional(),
  status: z.enum(['draft', 'published']).optional(),
  // Visibility flags
  is_description_public: z.boolean().default(false).optional(),
  is_lyrics_public: z.boolean().default(false).optional(),
  is_chords_public: z.boolean().default(false).optional(), // Assuming chords might be part of lyrics or separate
  is_bpm_public: z.boolean().default(false).optional(),
  is_key_public: z.boolean().default(false).optional(),
  is_album_public: z.boolean().default(false).optional(),
  is_composer_public: z.boolean().default(false).optional(),
  is_release_date_public: z.boolean().default(false).optional(),
  is_record_label_public: z.boolean().default(false).optional(),
  is_distributor_public: z.boolean().default(false).optional(),
  is_isrc_public: z.boolean().default(false).optional(),
  is_upc_public: z.boolean().default(false).optional(),
  are_stems_available_public: z.boolean().default(false).optional(), // New visibility flag
  is_download_allowed_public: z.boolean().default(false).optional(), // New visibility flag
  are_comments_allowed_public: z.boolean().default(false).optional(), // New visibility flag (default false for public view)
});
export type SongFormValues = z.infer<typeof songFormSchema>;

// Interfaces
export interface Album {
  id: string;
  title: string;
}

interface SongFormProps {
  mode: 'create' | 'edit';
  initialValues?: Partial<SongFormValues>;
  onFormSubmit: (data: SongFormValues, status?: 'draft' | 'published') => Promise<void>;
  isSubmitting: boolean;
  albums: Album[];
  isLoadingAlbums: boolean;
  supabaseClient: SupabaseClient;
  songId?: string; // Pass songId for preview link if in edit mode
}

// Component
export function SongForm({
  mode,
  initialValues,
  onFormSubmit,
  isSubmitting,
  albums,
  isLoadingAlbums,
  supabaseClient,
  songId, // Receive songId
}: SongFormProps) {
  const router = useRouter();

  const form = useForm<SongFormValues>({
    resolver: zodResolver(songFormSchema),
    defaultValues: mode === 'create' ? {
      title: "", artist_name: "", featured_artists: [], genre: [], moods: [], instrumentation: [], key: "",
      bpm: undefined, time_signature: "", capo: undefined, tuning_frequency: 440, description: "", tags: [],
      audio_url: null, image_path: null, album_id: null, composer_name: "", release_date: null,
      record_label: "", distributor: "", isrc: "", upc: "", is_explicit: false, 
      lyrics: "", bloc_note: "", right_column_notepad: "", status: 'draft',
      // Default visibility flags
      is_description_public: false, is_lyrics_public: false, is_chords_public: false, is_bpm_public: false, is_key_public: false,
      is_album_public: false, is_composer_public: false, is_release_date_public: false, is_record_label_public: false,
      is_distributor_public: false, is_isrc_public: false, is_upc_public: false, are_stems_available_public: false,
      is_download_allowed_public: false, are_comments_allowed_public: false
    } : { 
      ...initialValues, 
      release_date: initialValues?.release_date ? new Date(initialValues.release_date) : null,
      // Ensure existing visibility flags are loaded, defaulting to false if not present
      is_description_public: initialValues?.is_description_public ?? false,
      is_lyrics_public: initialValues?.is_lyrics_public ?? false,
      is_chords_public: initialValues?.is_chords_public ?? false,
      is_bpm_public: initialValues?.is_bpm_public ?? false,
      is_key_public: initialValues?.is_key_public ?? false,
      is_album_public: initialValues?.is_album_public ?? false,
      is_composer_public: initialValues?.is_composer_public ?? false,
      is_release_date_public: initialValues?.is_release_date_public ?? false,
      is_record_label_public: initialValues?.is_record_label_public ?? false,
      is_distributor_public: initialValues?.is_distributor_public ?? false,
      is_isrc_public: initialValues?.is_isrc_public ?? false,
      is_upc_public: initialValues?.is_upc_public ?? false,
      are_stems_available_public: initialValues?.are_stems_available_public ?? false,
      is_download_allowed_public: initialValues?.is_download_allowed_public ?? false,
      are_comments_allowed_public: initialValues?.are_comments_allowed_public ?? false,
    }, 
    mode: 'onChange',
  });

  // ... (rest of state variables remain the same) ...
  const [uploadingAudio, setUploadingAudio] = useState(false);
  const [uploadedAudioUrl, setUploadedAudioUrl] = useState<string | null>(initialValues?.audio_url || null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(initialValues?.image_path || null); 
  
  const audioFileInputRef = useRef<HTMLInputElement>(null);
  const imageFileInputRef = useRef<HTMLInputElement>(null);
  const quillRef = useRef<any>(null); 

  const [lyricsContent, setLyricsContent] = useState<string>(initialValues?.lyrics || "");
  const [aiConfig, setAiConfig] = useState<AiConfig>({
    provider: 'ollama', model: '', temperature: 0.7,
  });
  const [aiLoading, setAiLoading] = useState(false);
  const [aiLastResult, setAiLastResult] = useState<string | undefined>(undefined);
  const [aiError, setAiError] = useState<string | undefined>(undefined);
  const [aiHistory, setAiHistory] = useState<AiHistoryItem[]>([]);
  const [aiMaxOutputTokens, setAiMaxOutputTokens] = useState<number>(256);
  const [aiGeneralPrompt, setAiGeneralPrompt] = useState<string>("You are a helpful assistant for songwriting.");
  const [showAiConfigMenu, setShowAiConfigMenu] = useState(false);
  const [isAiPanelCollapsed, setIsAiPanelCollapsed] = useState(false);
  const [showAiHistory, setShowAiHistory] = useState(false); 

  // Utility to strip HTML tags
  const stripHtml = (html: string): string => {
    if (typeof document !== "undefined") {
      const doc = new DOMParser().parseFromString(html, 'text/html');
      return doc.body.textContent || "";
    }
    return html.replace(/<[^>]+>/g, ''); 
  };

  // Handler for editor content changes
  const handleLyricsChange = (newContent: string) => {
    setLyricsContent(newContent);
    form.setValue('lyrics', newContent, { shouldValidate: true, shouldDirty: true });
  };

  // --- AI Action Handlers (Only those NOT related to direct lyrics manipulation remain here) ---

  // Helper to add interaction to history
  const addAiHistory = (userPrompt: string, assistantResponse: string) => {
    setAiHistory((prev: AiHistoryItem[]) => [
      ...prev, 
      { role: 'user', content: userPrompt, timestamp: new Date() }, 
      { role: 'assistant', content: assistantResponse, timestamp: new Date() }
    ]);
  };

  // AI Action: General Song Suggestions (Remains here)
  const handleAiGeneralSuggestions = async () => { /* ... implementation ... */ };
  
  // AI Action: Melody Suggestion (Remains here)
  const handleAiMelodySuggestion = async () => { /* ... implementation ... */ };
  
  // AI Action: Recording Advice
  const handleAiRecordingAdvice = async () => { /* ... implementation ... */ };

  // AI Action: Instrumentation Suggestion
  const handleAiInstrumentationSuggestion = async () => { /* ... implementation ... */ };

  // AI Action: Creative FX Ideas
  const handleAiCreativeFx = async () => { /* ... implementation ... */ };

  // AI Action: Arrangement Advice
  const handleAiArrangementAdvice = async () => { /* ... implementation ... */ };

  // AI Action: Edit General Prompt (Remains here)
  const handleAiEditGeneralPrompt = useCallback((newPrompt: string) => { /* ... implementation ... */ }, []);

  // AI Action: Set Config
  const handleAiConfigChange = (newPartialConfig: Partial<AiConfig>) => { /* ... implementation ... */ };

  // File Upload Handler
  const handleFileUpload = async (file: File, type: 'audio' | 'image') => { /* ... implementation ... */ };

  // Form Submission Handler
  const onSubmitWithStatus = (status?: 'draft' | 'published') => { /* ... implementation ... */ };
  
  // Effects (Remain here)
  useEffect(() => { /* ... implementation ... */ }, [mode, initialValues, form]);
  useEffect(() => { /* ... implementation ... */ }, []);


  return (
    <FormProvider {...form}>
      <form className="h-full flex flex-col" onSubmit={form.handleSubmit(data => onFormSubmit(data))}>
        {/* Header Section */}
        <div className="w-full bg-background/90 py-6 px-0 md:px-4 flex flex-col md:flex-row md:items-center gap-8 border-b mb-8 max-w-none">
          {/* Image Upload */}
          <div className="flex flex-col items-center md:items-start gap-4 w-full md:w-1/4">
            <div className="w-full flex flex-col items-center">
              <FormField
                control={form.control}
                name="image_path"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm">Image de couverture</FormLabel>
                    {/* FormControl now only wraps the Input */}
                    <FormControl>
  <Input
    type="file"
    accept="image/*"
    ref={imageFileInputRef}
    onChange={(e) => {
      field.onChange(e.target.files?.[0] ? e.target.value : '');
      if (e.target.files?.[0]) {
        handleFileUpload(e.target.files[0], 'image');
      }
    }}
    className="hidden"
    id="image-upload"
  />
</FormControl>
{/* Les boutons et la preview restent en dehors de FormControl */}
<div className="flex flex-col items-center gap-2">
  <Button type="button" variant="outline" size="sm" onClick={() => imageFileInputRef.current?.click()} disabled={uploadingImage || isSubmitting}>
    {uploadingImage ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
    {uploadedImageUrl ? 'Changer' : 'Choisir une image'}
  </Button>
  {uploadedImageUrl && uploadedImageUrl.startsWith('http') && (
    <Image src={uploadedImageUrl} alt="Preview" width={180} height={180} className="rounded-md object-cover mt-2" />
  )}
</div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex-1 flex flex-col items-center md:items-start gap-4 w-full">
            {/* Title */}
            <FormField control={form.control} name="title" render={({ field }) => ( <FormItem className="w-full"> <FormLabel className="sr-only">Titre du morceau</FormLabel> <FormControl> <Input className="text-3xl md:text-4xl font-bold w-full bg-transparent border-none shadow-none px-0 mb-2" placeholder="Titre du morceau *" {...field} /> </FormControl> <FormMessage /> </FormItem> )} />
            {/* Artist Name Display */}
            {(form.watch('artist_name') ?? '').trim() !== '' && ( <div className="flex items-center mt-1 mb-2"> <span className="px-3 py-1 rounded-full bg-secondary text-base font-medium text-foreground/80 shadow-sm"> {form.watch('artist_name')} </span> </div> )}
            {/* Audio Upload/Preview */}
            <FormField
              control={form.control}
              name="audio_url"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="text-sm">Fichier audio</FormLabel>
                  {/* FormControl now only wraps the Input */}
                  <FormControl>
  <Input
    type="file"
    accept="audio/*"
    ref={audioFileInputRef}
    onChange={(e) => {
      field.onChange(e.target.files?.[0] ? e.target.value : '');
      if (e.target.files?.[0]) {
        handleFileUpload(e.target.files[0], 'audio');
      }
    }}
    className="hidden"
    id="audio-upload"
  />
</FormControl>
{/* Les boutons et la preview restent en dehors de FormControl */}
<div className="flex flex-col gap-2">
  <Button type="button" variant="outline" size="sm" onClick={() => audioFileInputRef.current?.click()} disabled={uploadingAudio || isSubmitting}>
    {uploadingAudio ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
    {uploadedAudioUrl ? 'Changer' : 'Choisir un fichier audio'}
  </Button>
  {uploadedAudioUrl && uploadedAudioUrl.startsWith('http') && (
    <AudioWaveformPreview audioUrl={uploadedAudioUrl} />
  )}
</div>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Action Buttons & Preview Link */}
            <div className="flex flex-wrap items-center gap-2 mt-4">
              <Button variant="outline" onClick={() => router.back()} disabled={isSubmitting || uploadingAudio || uploadingImage}>Annuler</Button>
              {mode === 'create' ? (
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <Button type="button" onClick={() => onSubmitWithStatus('draft')} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="secondary"> {(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />} Enregistrer en Brouillon </Button>
                  <Button type="button" onClick={() => onSubmitWithStatus('published')} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg"> {(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />} Publier </Button>
                </div>
              ) : (
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <Button type="button" onClick={() => onSubmitWithStatus(form.getValues('status'))} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg"> {(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />} Sauvegarder </Button>
                  {/* Preview Link */}
                  {songId && (
                    <Button asChild variant="link" className="text-sm p-0 h-auto">
                      <Link href={`/songs/${songId}`} target="_blank" rel="noopener noreferrer">
                        Voir la page publique <Eye className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <ScrollArea className="flex-grow pb-6">
          <div className="w-full px-4 md:px-6 lg:px-8 space-y-6"> 
            <Tabs defaultValue="general" className="w-full">
              <TabsList className="grid w-full grid-cols-1 sm:grid-cols-3 mb-4 bg-muted p-1 rounded-md"> 
                <TabsTrigger value="general">Général</TabsTrigger>
                <TabsTrigger value="lyrics-ia">Paroles / IA</TabsTrigger>
                <TabsTrigger value="publication">Publication & Options</TabsTrigger>
              </TabsList>

              {/* General Tab - Reorganized */}
              <TabsContent value="general" className="w-full">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Column 1: Basic Info, Description, Notepads */}
                  <div className="lg:col-span-2 space-y-6">
                    <Card>
                      <CardHeader><CardTitle>Informations Principales</CardTitle></CardHeader>
                      <CardContent className="space-y-4">
                        <FormField control={form.control} name="title" render={({ field }) => (<FormItem><FormLabel>Titre du morceau *</FormLabel><FormControl><Input placeholder="Ex: Mon Super Hit" {...field} /></FormControl><FormMessage /></FormItem>)} />
                        <FormField control={form.control} name="artist_name" render={({ field }) => (<FormItem><FormLabel>Artiste principal *</FormLabel><FormControl><Input placeholder="Ex: Nom de l'artiste" {...field} /></FormControl><FormMessage /></FormItem>)} />
                        <FormField control={form.control} name="featured_artists" render={({ field }) => (<FormItem><FormLabel>Artistes en featuring</FormLabel><FormControl><Input placeholder="Ajouter des artistes en featuring (séparés par des virgules)" {...field} /></FormControl><FormMessage /></FormItem>)} />
                        {/* Composer Name with Integrated Visibility Toggle */}
                        <FormField
                          control={form.control}
                          name="composer_name"
                           render={({ field }) => (
                             <FormItem>
                               <div className="flex items-center justify-between mb-1">
                                 <FormLabel>Nom du compositeur</FormLabel>
                                 <VisibilityToggle control={form.control} name="is_composer_public" />
                               </div>
                               <FormControl>
                                 <Input placeholder="Ex: Jean Dupont" {...field} />
                               </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>
                    <Card>
                       <CardHeader><CardTitle>Description & Catégorisation</CardTitle></CardHeader>
                      <CardContent className="space-y-4">
                         {/* MultiSelect: Removed FormControl wrapper */}
                         <FormField control={form.control} name="genre" render={({ field }) => (
  <FormItem>
    <FormLabel>Genre(s)</FormLabel>
    <MultiSelect options={genreOptions} selected={field.value || []} onChange={field.onChange} placeholder="Ajouter un ou plusieurs genres" />
    <FormMessage />
  </FormItem>
)} />
                         {/* MultiSelect: Removed FormControl wrapper */}
                         <FormField control={form.control} name="moods" render={({ field }) => (
  <FormItem>
    <FormLabel>Ambiance(s) / Mood(s)</FormLabel>
    <MultiSelect options={moodOptions} selected={field.value || []} onChange={field.onChange} placeholder="Ajouter des ambiances (moods)" />
    <FormMessage />
  </FormItem>
)} />
                         {/* MultiSelect: Removed FormControl wrapper */}
                         <FormField control={form.control} name="tags" render={({ field }) => (
  <FormItem>
    <FormLabel>Tags</FormLabel>
    <MultiSelect options={tagOptions} selected={field.value || []} onChange={field.onChange} placeholder="Ajouter des tags" />
    <FormMessage />
  </FormItem>
)} />
                         {/* Description with Integrated Visibility Toggle */}
                         <FormField
                           control={form.control}
                           name="description"
                           render={({ field }) => (
                             <FormItem>
                               <div className="flex items-center justify-between mb-1">
                                 <FormLabel>Description</FormLabel>
                                 <VisibilityToggle control={form.control} name="is_description_public" />
                               </div>
                               <FormControl>
                                 <Textarea placeholder="Décrivez votre morceau, son histoire, ses inspirations..." className="min-h-[100px]" {...field} />
                               </FormControl>
                               <FormMessage />
                             </FormItem>
                           )}
                         />
                      </CardContent>
                    </Card>
                    {/* Moved Notepads Here */}
                    <Card>
                      <CardHeader><CardTitle>Bloc-notes</CardTitle><CardDescription>Espace pour vos notes privées sur ce morceau.</CardDescription></CardHeader>
                      <CardContent className="space-y-4">
                        {/* Notepad: Removed FormControl wrapper */}
                        <FormField control={form.control} name="bloc_note" render={({ field }) => (
  <FormItem>
    <FormLabel>Bloc-notes principal</FormLabel>
    <ScrollArea className="h-40 w-full rounded-md border p-2">
      <Textarea placeholder="Notes sur la composition, l'enregistrement, idées..." className="min-h-[120px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-1" {...field} />
    </ScrollArea>
    <FormMessage />
  </FormItem>
)} />
                        {/* Notepad: Removed FormControl wrapper */}
                        <FormField control={form.control} name="right_column_notepad" render={({ field }) => (
  <FormItem>
    <FormLabel>Bloc-notes (colonne de droite)</FormLabel>
    <ScrollArea className="h-40 w-full rounded-md border p-2">
      <Textarea placeholder="Notes additionnelles, visibles dans la colonne de droite de la page du morceau." className="min-h-[120px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-1" {...field} />
    </ScrollArea>
    <FormDescription>Ce bloc-notes sera affiché dans la colonne de droite sur la page de détail du morceau (si le thème le supporte).</FormDescription>
    <FormMessage />
  </FormItem>
)} />
                      </CardContent>
                    </Card>
                  </div>
                   {/* Column 2: Musical Details & Instrumentation */}
                   <div className="lg:col-span-1 space-y-6">
                     <Card>
                       <CardHeader><CardTitle>Détails Musicaux</CardTitle></CardHeader>
                       <CardContent className="space-y-4">
                         {/* Key with Integrated Visibility Toggle */}
                         <FormField
                           control={form.control}
                           name="key"
                           render={({ field }) => (
                             <FormItem>
                               <div className="flex items-center justify-between mb-1">
                                 <FormLabel>Tonalité</FormLabel>
                                  <VisibilityToggle control={form.control} name="is_key_public" />
                                </div>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  {/* Removed FormControl wrapper from SelectTrigger */}
                                  <SelectTrigger><SelectValue placeholder="Sélectionner une tonalité" /></SelectTrigger>
                                  <SelectContent className="bg-background border shadow-md">{musicalKeys.map(k => <SelectItem key={k} value={k}>{k}</SelectItem>)}</SelectContent>
                                </Select>
                                <FormMessage />
                             </FormItem>
                           )}
                         />
                         {/* BPM with Integrated Visibility Toggle */}
                         <FormField
                           control={form.control}
                           name="bpm"
                           render={({ field }) => (
                             <FormItem>
                               <div className="flex items-center justify-between mb-1">
                                 <FormLabel>BPM</FormLabel>
                                 <VisibilityToggle control={form.control} name="is_bpm_public" />
                               </div>
                               <FormControl>
                                 <Input type="number" placeholder="120" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))} value={field.value ?? ''} />
                               </FormControl>
                               <FormMessage />
                             </FormItem>
                          )}
                          />
                          {/* Time Signature - Assuming always public if provided */}
                          <FormField control={form.control} name="time_signature" render={({ field }) => (<FormItem><FormLabel>Signature Rythmique</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value}>
                                  {/* Removed FormControl wrapper from SelectTrigger */}
                                  <SelectTrigger><SelectValue placeholder="Sélectionner une signature" /></SelectTrigger>
                                  <SelectContent className="bg-background border shadow-md">{timeSignatures.map(ts => <SelectItem key={ts} value={ts}>{ts}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem>)} />
                          {/* Capo - Assuming always public if provided */}
                          <FormField control={form.control} name="capo" render={({ field }) => (<FormItem><FormLabel>Capo</FormLabel><FormControl><Input type="number" placeholder="0" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                         {/* Tuning Frequency - Assuming always public if provided */}
                         <FormField control={form.control} name="tuning_frequency" render={({ field }) => (<FormItem><FormLabel>Fréquence d'accordage (Hz)</FormLabel><FormControl><Input type="number" placeholder="440" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                       </CardContent>
                     </Card>
                     <Card>
                        <CardHeader><CardTitle>Instrumentation</CardTitle></CardHeader>
                        <CardContent>
                          {/* MultiSelect: Removed FormControl wrapper */}
                          <FormField control={form.control} name="instrumentation" render={({ field }) => (
  <FormItem>
    <FormLabel>Instrumentation</FormLabel>
    {/* PAS de FormControl ici : MultiSelect est directement enfant */}
    <MultiSelect options={instrumentationOptions} selected={field.value || []} onChange={field.onChange} placeholder="Ajouter des instruments" />
    <FormMessage />
  </FormItem>
)} />
                        </CardContent>
                      </Card>
                   </div>
                </div>
              </TabsContent>

              {/* Lyrics / AI Tab */}
              <TabsContent value="lyrics-ia" className="w-full p-0">
                <div className={`grid grid-cols-1 ${!isAiPanelCollapsed ? 'lg:grid-cols-3 gap-6' : ''}`}>
                  <div className={!isAiPanelCollapsed ? 'lg:col-span-2' : 'lg:col-span-3'}>
                    <Card className="w-full">
                      {/* Lyrics Visibility Toggle in Header */}
                      <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <div>
                          <CardTitle>Paroles</CardTitle>
                          <CardDescription>Écrivez ou générez les paroles de votre morceau.</CardDescription>
                        </div>
                         {/* Use VisibilityToggle directly here, passing control and name */}
                         <VisibilityToggle control={form.control} name="is_lyrics_public" label="Public" />
                      </CardHeader>
                      <CardContent className="space-y-4 pt-4"> 
                        <LyricsEditorWithAI
                          lyricsContent={lyricsContent}
                          handleLyricsChange={handleLyricsChange} 
                          quillRef={quillRef} 
                          formControl={form.control} 
                          aiConfig={aiConfig}
                          aiGeneralPrompt={aiGeneralPrompt}
                          addAiHistory={addAiHistory}
                          aiHistory={aiHistory} 
                          showAiHistory={showAiHistory} 
                          setShowAiHistory={setShowAiHistory} 
                        />
                      </CardContent>
                    </Card>
                  </div>
                  {/* AI Assistant Panel */}
                  {!isAiPanelCollapsed && ( <AiAssistantPanel isAiPanelCollapsed={isAiPanelCollapsed} setIsAiPanelCollapsed={setIsAiPanelCollapsed} showAiConfigMenu={showAiConfigMenu} setShowAiConfigMenu={setShowAiConfigMenu} aiLoading={aiLoading} aiLastResult={aiLastResult} aiError={aiError} aiHistory={aiHistory} aiConfig={aiConfig} setAiConfig={handleAiConfigChange} generalPrompt={aiGeneralPrompt} onEditGeneralPrompt={handleAiEditGeneralPrompt} onGeneralSuggestions={handleAiGeneralSuggestions} onMelodySuggestion={handleAiMelodySuggestion} onRecordingAdvice={handleAiRecordingAdvice} onInstrumentationSuggestion={handleAiInstrumentationSuggestion} onCreativeFx={handleAiCreativeFx} onArrangementAdvice={handleAiArrangementAdvice} /> )}
                </div> 
                 {/* Button to re-open AI panel */}
                 {isAiPanelCollapsed && ( <div className="absolute top-0 right-0 mt-2 mr-2 z-20"> <Button variant="outline" size="icon" onClick={() => setIsAiPanelCollapsed(false)} className="rounded-full shadow-lg bg-background hover:bg-muted h-8 w-8" title="Ouvrir l'assistant IA"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><line x1="15" y1="3" x2="15" y2="21"/></svg> <span className="sr-only">Ouvrir Assistant IA</span> </Button> </div> )}
              </TabsContent>

              {/* Publication & Options Tab */}
              <TabsContent value="publication"> 
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Distribution Info Card */}
                  <Card>
                     <CardHeader><CardTitle>Distribution & Publication</CardTitle><CardDescription>Informations relatives à la distribution et aux droits.</CardDescription></CardHeader>
                     <CardContent className="space-y-4">
                       {/* Album with Visibility Toggle */}
                       <FormField control={form.control} name="album_id" render={({ field }) => (<FormItem><div className="flex items-center justify-between mb-1"><FormLabel>Album</FormLabel><VisibilityToggle control={form.control} name="is_album_public" /></div><Select onValueChange={field.onChange} value={field.value || NO_ALBUM_SELECTED_VALUE}>
                                  {/* Removed FormControl wrapper from SelectTrigger */}
                                  <SelectTrigger disabled={isLoadingAlbums}><SelectValue placeholder={isLoadingAlbums ? "Chargement des albums..." : "Sélectionner un album (optionnel)"} /></SelectTrigger>
                                  <SelectContent className="bg-background border shadow-md"><SelectItem value={NO_ALBUM_SELECTED_VALUE}>Aucun album</SelectItem>{albums.map((album) => (<SelectItem key={album.id} value={album.id}>{album.title}</SelectItem>))}</SelectContent></Select><FormMessage /></FormItem>)} />
                       {/* Release Date with Visibility Toggle */}
                       <FormField control={form.control} name="release_date" render={({ field }) => (<FormItem className="flex flex-col"><div className="flex items-center justify-between mb-1"><FormLabel>Date de sortie</FormLabel><VisibilityToggle control={form.control} name="is_release_date_public" /></div><FormControl><DatePicker date={field.value ?? undefined} onSelect={(date: Date | undefined) => field.onChange(date)} /></FormControl><FormMessage /></FormItem>)} />
                       {/* Record Label with Visibility Toggle */}
                       <FormField control={form.control} name="record_label" render={({ field }) => (<FormItem><div className="flex items-center justify-between mb-1"><FormLabel>Label</FormLabel><VisibilityToggle control={form.control} name="is_record_label_public" /></div><FormControl><Input placeholder="Mon Label Indépendant" {...field} /></FormControl><FormMessage /></FormItem>)} />
                       {/* Distributor with Visibility Toggle */}
                       <FormField control={form.control} name="distributor" render={({ field }) => (<FormItem><div className="flex items-center justify-between mb-1"><FormLabel>Distributeur</FormLabel><VisibilityToggle control={form.control} name="is_distributor_public" /></div><FormControl><Input placeholder="Ex: DistroKid, TuneCore" {...field} /></FormControl><FormMessage /></FormItem>)} />
                       {/* ISRC with Visibility Toggle */}
                       <FormField control={form.control} name="isrc" render={({ field }) => (<FormItem><div className="flex items-center justify-between mb-1"><FormLabel>ISRC</FormLabel><VisibilityToggle control={form.control} name="is_isrc_public" /></div><FormControl><Input placeholder="Ex: US-S1Z-99-00001" {...field} /></FormControl><FormDescription>Code ISRC pour l'identification de l'enregistrement.</FormDescription><FormMessage /></FormItem>)} />
                       {/* UPC with Visibility Toggle */}
                       <FormField control={form.control} name="upc" render={({ field }) => (<FormItem><div className="flex items-center justify-between mb-1"><FormLabel>UPC/EAN</FormLabel><VisibilityToggle control={form.control} name="is_upc_public" /></div><FormControl><Input placeholder="Ex: 123456789012" {...field} /></FormControl><FormDescription>Code-barres du produit.</FormDescription><FormMessage /></FormItem>)} />
                     </CardContent>
                  </Card>
                  {/* Publication Options Card */}
                  <Card>
                    <CardHeader><CardTitle>Options de Publication</CardTitle><CardDescription>Gérez les options de votre morceau.</CardDescription></CardHeader>
                    <CardContent className="space-y-4">
                      {/* Explicit Content - Assuming always public if set */}
                      <FormField control={form.control} name="is_explicit" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Contenu Explicite</FormLabel> <FormDescription>Indique si le morceau contient des paroles ou thèmes explicites.</FormDescription> </div> <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl> </FormItem> )}/>
                       {/* Stems Available with Visibility Toggle */}
                       <FormField control={form.control} name="are_stems_available_public" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Stems Disponibles</FormLabel> <FormDescription>Pistes instrumentales séparées disponibles ?</FormDescription> </div> <VisibilityToggle control={form.control} name="are_stems_available_public" /> </FormItem> )}/>
                       {/* Allow Downloads with Visibility Toggle */}
                       <FormField control={form.control} name="is_download_allowed_public" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Autoriser Téléchargements</FormLabel> <FormDescription>Permettre aux utilisateurs de télécharger le fichier audio.</FormDescription> </div> <VisibilityToggle control={form.control} name="is_download_allowed_public" /> </FormItem> )}/>
                       {/* Allow Comments with Visibility Toggle */}
                       <FormField control={form.control} name="are_comments_allowed_public" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Autoriser Commentaires</FormLabel> <FormDescription>Permettre aux utilisateurs de commenter ce morceau.</FormDescription> </div> <VisibilityToggle control={form.control} name="are_comments_allowed_public" /> </FormItem> )}/>
                    </CardContent>
                  </Card>
                </div>
            </TabsContent> 
            </Tabs>
          </div>
        </ScrollArea>
      </form>
    </FormProvider>
  );
}

export default SongForm;
