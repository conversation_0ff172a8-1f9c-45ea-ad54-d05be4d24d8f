import Link from "next/link"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { Plus, Users, Music, Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

export default async function BandsPage() {
  const supabase = createSupabaseServerClient()

  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Fetch user's bands (where they are a member)
  const { data: userBands } = await supabase
    .from("band_members")
    .select(`
      bands (
        id,
        name,
        description,
        avatar_url,
        cover_url,
        created_at,
        genre,
        location
      )
    `)
    .eq("user_id", session?.user.id)

  // Fetch band invitations
  const { data: invitations } = await supabase
    .from("band_invitations")
    .select(`
      id,
      bands (
        id,
        name,
        avatar_url
      ),
      role,
      created_at
    `)
    .eq("user_id", session?.user.id)
    .eq("status", "pending")

  // Fetch recommended bands
  const { data: recommendedBands } = await supabase.from("bands").select("*").limit(4)

  // Strong type guard for band object structure
  interface Band {
    id: string;
    name: string;
    description?: string;
    avatar_url?: string;
    cover_url?: string;
    created_at?: string;
    genre?: string;
    location?: string;
  }

  // Fix: avoid referencing band.id and band.name directly in the type guard logic; use a helper function
  function isBand(obj: any): obj is Band {
    return (
      obj &&
      typeof obj === 'object' &&
      'id' in obj && typeof obj.id === 'string' &&
      'name' in obj && typeof obj.name === 'string'
    );
  }

  const myBands: Band[] = Array.isArray(userBands)
    ? userBands
        .map((item) => item.bands)
        .flatMap((band) => {
          if (Array.isArray(band)) {
            return band.filter(isBand);
          } else if (isBand(band)) {
            return [band];
          } else {
            return [];
          }
        })
    : [];

  // Types for invitation bands (for type safety in invitations rendering)
  interface Invitation {
    id: string;
    bands: Band;
    role: string;
    created_at: string;
  }

  // Helper to extract a valid Band from invitation.bands, which may be array or object
  function extractBand(bands: unknown): Band | null {
    if (Array.isArray(bands)) {
      const band = bands.find(isBand);
      return band || null;
    } else if (isBand(bands)) {
      return bands;
    }
    return null;
  }

  return (
    <div className="min-h-screen w-full bg-transparent">
      <div className="frosted p-6 rounded-xl w-full">
        <div className="flex flex-col gap-6 w-full">
          <div className="flex items-center justify-between w-full mb-6">
            <h1 className="text-3xl font-bold tracking-tight">Groupes</h1>
            <Button
              asChild
              size="lg"
              className="bg-primary text-white rounded-xl font-semibold shadow-md hover:bg-primary/90 transition-colors w-full max-w-xs"
            >
              <Link href="/bands/create">
                <Plus className="mr-2 h-5 w-5" />
                Créer un groupe
              </Link>
            </Button>
          </div>

          <Tabs defaultValue="my-bands" className="w-full">
            <TabsList>
              <TabsTrigger value="my-bands">Mes groupes</TabsTrigger>
              <TabsTrigger value="invitations">
                Invitations {invitations?.length ? `(${invitations.length})` : ""}
              </TabsTrigger>
              <TabsTrigger value="discover">Découvrir</TabsTrigger>
            </TabsList>

            <TabsContent value="my-bands" className="mt-6 w-full">
              {myBands.length > 0 ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 w-full">
                  {myBands.map((band) => (
                    <Link href={`/bands/${band.id}`} key={band.id}>
                      <Card className="overflow-hidden h-full hover:shadow-md transition-shadow w-full">
                        <div className="h-32 overflow-hidden">
                          <img
                            src={band.cover_url || "/placeholder.svg?height=200&width=400&query=music band"}
                            alt={band.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <CardHeader className="pb-2">
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarImage src={band.avatar_url || "/placeholder.svg?height=40&width=40&query=band logo"} />
                              <AvatarFallback>{band.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <CardTitle>{band.name}</CardTitle>
                              <CardDescription>
                                {band.genre} • {band.location}
                              </CardDescription>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <p className="text-sm line-clamp-2">{band.description}</p>
                        </CardContent>
                        <CardFooter className="flex justify-between">
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center">
                              <Users className="mr-1 h-4 w-4" />
                              <span>5 membres</span>
                            </div>
                            <div className="flex items-center">
                              <Music className="mr-1 h-4 w-4" />
                              <span>12 morceaux</span>
                            </div>
                          </div>
                        </CardFooter>
                      </Card>
                    </Link>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Users className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">Vous n'avez pas encore de groupe</h3>
                  <p className="mt-2 text-muted-foreground">
                    Créez un groupe ou rejoignez-en un pour commencer à collaborer
                  </p>
                  <Button className="mt-4" asChild>
                    <Link href="/bands/create">
                      <Plus className="mr-2 h-4 w-4" />
                      Créer un groupe
                    </Link>
                  </Button>
                </div>
              )}
            </TabsContent>

            <TabsContent value="invitations" className="mt-6 w-full">
              {invitations?.length ? (
                <div className="space-y-4 w-full">
                  {invitations.map((invitation) => {
                    const band = extractBand(invitation.bands);
                    return (
                      <Card key={invitation.id} className="w-full">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar>
                                <AvatarImage
                                  src={band && band.avatar_url ? band.avatar_url : "/placeholder.svg?height=40&width=40&query=band logo"}
                                />
                                <AvatarFallback>{band && band.name ? band.name.charAt(0) : '?'}</AvatarFallback>
                              </Avatar>
                              <div>
                                <CardTitle>{band && band.name ? band.name : ''}</CardTitle>
                                <CardDescription>Vous êtes invité en tant que {invitation.role}</CardDescription>
                              </div>
                            </div>
                            <Badge variant="outline" className="bg-primary/10">
                              {new Date(invitation.created_at).toLocaleDateString()}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardFooter className="flex gap-2 pt-2">
                          <Button size="sm" className="flex-1">
                            Accepter
                          </Button>
                          <Button size="sm" variant="outline" className="flex-1">
                            Refuser
                          </Button>
                        </CardFooter>
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">Aucune invitation en attente</h3>
                  <p className="mt-2 text-muted-foreground">
                    Vous n'avez pas d'invitation à rejoindre un groupe pour le moment
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="discover" className="mt-6">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {(recommendedBands || []).map((band) => (
                  <Card key={band.id} className="overflow-hidden hover:shadow-md transition-shadow">
                    <div className="h-32 overflow-hidden">
                      <img
                        src={band.cover_url || "/placeholder.svg?height=200&width=400&query=music band"}
                        alt={band.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={band.avatar_url || "/placeholder.svg?height=40&width=40&query=band logo"} />
                          <AvatarFallback>{band.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle>{band.name}</CardTitle>
                          <CardDescription>
                            {band.genre} • {band.location}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm line-clamp-2">{band.description}</p>
                    </CardContent>
                    <CardFooter>
                      <Button size="sm">
                        Voir le profil
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
