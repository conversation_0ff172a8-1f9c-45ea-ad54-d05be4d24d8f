import Link from "next/link"
import { ChevronRight, UserPlus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface BandMembersProps {
  members: any[]
  bandId: string
  isAdmin: boolean
}

export function BandMembers({ members, bandId, isAdmin }: BandMembersProps) {
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "owner":
        return "default"
      case "admin":
        return "destructive"
      case "member":
        return "secondary"
      case "collaborator":
        return "outline"
      default:
        return "outline"
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>Membres</CardTitle>
          <CardDescription>Membres actifs du groupe</CardDescription>
        </div>
        {members.length > 5 && (
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/bands/${bandId}/members`}>
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {members.slice(0, 5).map((member) => (
            <div key={member.user_id} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage
                    src={member.profiles?.avatar_url || "/placeholder.svg?height=40&width=40&query=person"}
                  />
                  <AvatarFallback>{member.profiles?.name?.charAt(0) || "U"}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{member.profiles?.name}</p>
                  <p className="text-xs text-muted-foreground">
                    Depuis {new Date(member.joined_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <Badge variant={getRoleBadgeVariant(member.role)}>
                {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
              </Badge>
            </div>
          ))}
        </div>

        {isAdmin && (
          <Button variant="outline" className="w-full mt-4">
            <UserPlus className="mr-2 h-4 w-4" />
            Inviter un membre
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
