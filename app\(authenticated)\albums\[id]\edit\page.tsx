"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { getSupabaseClient } from "@/lib/supabase/client"
import { Disc, Upload, Save, X, Tag, Info, Plus, Music, FlipVerticalIcon as DragVertical, Trash2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { ImageUploader } from "@/components/ui/image-uploader"
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { MultiSelect } from "@/components/ui/multi-select"
// Using options from song-options for consistency
import { genreOptions, moodOptions, instrumentationOptions, albumTypeOptions } from '@/lib/constants/song-options';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"; 
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from "@/components/ui/dialog"; // For modal

export default function EditAlbumPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    genres: [] as string[],
    moods: [] as string[], 
    instrumentation: [] as string[], 
    album_type: "", // Added album_type
    label: "",
    upc: "",
    coverUrl: "",
    releaseDate: "",
    status: "draft",
    isPublic: true, // Assuming default, not in DB schema for albums from create page
    notifyFollowers: true, // Assuming default
    addToDiscovery: true, // Assuming default
  })

  const [tracks, setTracks] = useState<Array<{ id: string; title: string; duration: number; songId?: string }>>([])
  const [currentTag, setCurrentTag] = useState("")
  const [tags, setTags] = useState<string[]>([])
  
  const [availableSongs, setAvailableSongs] = useState<any[]>([])
  const [isAddSongModalOpen, setIsAddSongModalOpen] = useState(false)


  const loadAvailableSongs = async () => {
    try {
      const supabase = getSupabaseClient()
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) return;

      const { data, error } = await supabase
        .from("songs")
        .select("id, title, cover_url, duration, status")
        .eq("user_id", session.user.id)
        .eq("status", "published") // Only published songs can be added
        .order("title", { ascending: true })

      if (error) throw error
      setAvailableSongs(data || [])
    } catch (error) {
      console.error("Erreur lors du chargement des morceaux disponibles:", error)
      toast({ title: "Erreur", description: "Impossible de charger vos morceaux disponibles.", variant: "destructive" });
    }
  }

  useEffect(() => {
    const fetchAlbumData = async () => {
      setIsLoadingData(true);
      try {
        const supabase = getSupabaseClient()
        const { data: album, error } = await supabase.from("albums").select("*").eq("id", params.id).single()

        if (error) throw error;
        if (!album) {
          toast({ title: "Erreur", description: "Album non trouvé", variant: "destructive" });
          router.push("/albums");
          return;
        }

        // Fetch songs linked via album_songs join table for correct order and track data
        const { data: albumSongsData, error: albumSongsError } = await supabase
          .from("album_songs")
          .select("track_number, songs(*)") // Fetch all song data via relationship
          .eq("album_id", params.id)
          .order("track_number", { ascending: true });

        if (albumSongsError) throw albumSongsError;
        
        const fetchedTracks = Array.isArray(albumSongsData) ? albumSongsData.map(as => ({
          // @ts-ignore
          id: `track-${as.songs.id}`, 
          // @ts-ignore
          songId: as.songs.id,
          // @ts-ignore
          title: as.songs.title,
          // @ts-ignore
          duration: as.songs.duration || 0,
          // track_number: as.track_number // if needed for display or sorting
        })) : [];
        
        setTracks(fetchedTracks);


        const { data: resourceTags } = await supabase
          .from("resource_tags")
          .select("tags(id, name)")
          .eq("resource_type", "album")
          .eq("resource_id", params.id)

        const albumTags = resourceTags?.map((rt) => (rt.tags as any).name) || []

        setFormData({
          title: album.title || '',
          description: album.description || '',
          genres: Array.isArray(album.genre) ? album.genre : (typeof album.genre === 'string' ? album.genre.split(',').filter(Boolean) : []),
          moods: Array.isArray(album.moods) ? album.moods : (typeof album.moods === 'string' ? album.moods.split(',').filter(Boolean) : []),
          instrumentation: Array.isArray(album.instrumentation) ? album.instrumentation : (typeof album.instrumentation === 'string' ? album.instrumentation.split(',').filter(Boolean) : []),
          album_type: album.album_type || "", // Added album_type
          label: album.label || '',
          upc: album.upc || '',
          coverUrl: album.cover_url || '',
          releaseDate: album.release_date ? new Date(album.release_date).toISOString().split('T')[0] : '',
          status: album.status || 'draft',
          isPublic: album.is_public !== undefined ? album.is_public : true, // Assuming is_public exists on album
          notifyFollowers: album.notify_followers !== undefined ? album.notify_followers : true, // Assuming field exists
          addToDiscovery: album.add_to_discovery !== undefined ? album.add_to_discovery : true, // Assuming field exists
        });
        setTags(albumTags);

      } catch (error: any) {
        toast({ title: "Erreur", description: error.message || "Une erreur s'est produite lors du chargement de l'album", variant: "destructive" });
      } finally {
        setIsLoadingData(false);
      }
    }
    fetchAlbumData();
    loadAvailableSongs(); // Load available songs when component mounts
  }, [params.id, router, toast]);

  const handleAddSelectedSongsToAlbum = (selectedSongIds: string[]) => {
    const songsToAdd = availableSongs.filter(s => selectedSongIds.includes(s.id) && !tracks.some(t => t.songId === s.id));
    
    const newTracksForAlbum = songsToAdd.map(s => ({
      id: `track-${s.id}`, // Ensure unique ID for dnd list
      songId: s.id,
      title: s.title,
      duration: s.duration || 0,
    }));

    setTracks(prev => [...prev, ...newTracksForAlbum]);
    setIsAddSongModalOpen(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleCoverUpload = (url: string) => {
    setFormData((prev) => ({ ...prev, coverUrl: url }))
  }

  const addTag = () => {
    if (currentTag && !tags.includes(currentTag)) {
      setTags((prev) => [...prev, currentTag])
      setCurrentTag("")
    }
  }

  const removeTag = (tag: string) => {
    setTags((prev) => prev.filter((t) => t !== tag))
  }

  const addTrack = () => { // For adding new, unsaved tracks to UI
    const newTrack = { id: `new-track-${Date.now()}`, title: `Nouveau morceau ${tracks.length + 1}`, duration: 0, songId: undefined };
    setTracks((prev) => [...prev, newTrack]);
  }

  const updateTrack = (id: string, field: string, value: any) => {
    setTracks((prev) => prev.map((track) => (track.id === id ? { ...track, [field]: value } : track)))
  }

  const removeTrack = (id: string) => {
    setTracks((prev) => prev.filter((track) => track.id !== id))
  }

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    const items = Array.from(tracks);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setTracks(items);
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }

  const calculateTotalDuration = () => {
    return tracks.reduce((total, track) => total + (track.duration || 0), 0);
  }

  const handleSave = async (newStatus?: "draft" | "published") => {
    setIsLoading(true);
    const finalStatus = newStatus || formData.status;

    if (!formData.title) {
      toast({ title: "Erreur", description: "Le titre est obligatoire", variant: "destructive" });
      setIsLoading(false); return;
    }
    // Add other validations as needed, e.g., for publishing

    const supabase = getSupabaseClient();
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      toast({ title: "Erreur d'authentification", variant: "destructive" });
      setIsLoading(false); return;
    }

    try {
      const { error: albumUpdateError } = await supabase
        .from("albums")
        .update({
          title: formData.title,
          description: formData.description,
          genre: formData.genres.length > 0 ? formData.genres : null, // Save as array
          moods: formData.moods.length > 0 ? formData.moods : null, // Save as array
          instrumentation: formData.instrumentation.length > 0 ? formData.instrumentation : null, // Save as array
          album_type: formData.album_type || null, // Save album_type
          label: formData.label,
          upc: formData.upc,
          cover_url: formData.coverUrl,
          status: finalStatus,
          release_date: formData.releaseDate || null,
          updated_at: new Date().toISOString(),
          total_duration: calculateTotalDuration(), // Add calculated total_duration
          // is_public: formData.isPublic, // Add these if they exist in your DB schema for albums
          // notify_followers: formData.notifyFollowers,
          // add_to_discovery: formData.addToDiscovery,
        })
        .eq("id", params.id);
      if (albumUpdateError) throw albumUpdateError;

      // Tag management (delete old, insert new)
      await supabase.from("resource_tags").delete().eq("resource_type", "album").eq("resource_id", params.id);
      if (tags.length > 0) {
        let tagErrors: string[] = [];
        for (const tagName of tags) {
          try {
            const { data: existingTag } = await supabase.from("tags").select("id").eq("name", tagName).single();
            let tagId = existingTag?.id;
            if (!existingTag) {
              const { data: newTag, error: tagError } = await supabase.from("tags").insert({ name: tagName }).select().single();
              if (tagError) throw tagError;
              tagId = newTag?.id;
            }
            if (tagId) {
              const { error: rtError } = await supabase.from("resource_tags").insert({ tag_id: tagId, resource_type: "album", resource_id: params.id });
              if (rtError) throw rtError;
            }
          } catch (e: any) { tagErrors.push(e.message); }
        }
        if (tagErrors.length > 0) toast({ title: "Erreurs tags", description: tagErrors.join(", "), duration: 7000 });
      }

      // Track management
      let trackProcessingErrors: string[] = [];
      const newTracksToCreateInSongsTable = tracks.filter(t => !t.songId);
      const existingTracksInUIToUpdateOrRelink = tracks.filter(t => t.songId);
      
      const createdAndUpdatedSongIdsForAlbumSongs: { songId: string; title: string }[] = [];

      // Create new songs
      for (const track of newTracksToCreateInSongsTable) {
        const { data: newSongData, error: newSongError } = await supabase.from("songs").insert({
          user_id: session.user.id, title: track.title, duration: track.duration, album_id: params.id, status: finalStatus, // cover_url: formData.coverUrl, // Optional: inherit cover
        }).select().single();
        if (newSongError) trackProcessingErrors.push(`Erreur création "${track.title}": ${newSongError.message}`);
        else if (newSongData) createdAndUpdatedSongIdsForAlbumSongs.push({ songId: newSongData.id, title: newSongData.title });
      }

      // Update existing songs metadata (if intended)
      for (const track of existingTracksInUIToUpdateOrRelink) {
        const { error: updateSongError } = await supabase.from("songs").update({ title: track.title, duration: track.duration }).eq("id", track.songId!);
        if (updateSongError) trackProcessingErrors.push(`Erreur MàJ "${track.title}": ${updateSongError.message}`);
        else createdAndUpdatedSongIdsForAlbumSongs.push({ songId: track.songId!, title: track.title }); // Add to list for album_songs
      }
      
      // Manage album_songs join table
      await supabase.from("album_songs").delete().eq("album_id", params.id);
      if (createdAndUpdatedSongIdsForAlbumSongs.length > 0) {
        const albumSongsToInsert = tracks // Use the order from the UI 'tracks' state
          .map((uiTrack, index) => {
            const songData = createdAndUpdatedSongIdsForAlbumSongs.find(s => s.songId === uiTrack.songId || s.title === uiTrack.title); // Match by ID or title for newly created
            return songData ? { album_id: params.id, song_id: songData.songId, track_number: index + 1 } : null;
          })
          .filter(item => item !== null);

        if (albumSongsToInsert.length > 0) {
          // @ts-ignore
          const { error: insertAlbumSongsError } = await supabase.from("album_songs").insert(albumSongsToInsert);
          if (insertAlbumSongsError) trackProcessingErrors.push(`Erreur liaison morceaux: ${insertAlbumSongsError.message}`);
        }
      }

      if (trackProcessingErrors.length > 0) {
        toast({ title: "Erreurs partielles (morceaux)", description: trackProcessingErrors.join("; "), variant: "destructive", duration: 10000 });
      }
      
      await supabase.from("activities").insert({ 
        user_id: session.user.id, 
        type: "album_updated", // Changed from activity_type
        target_type: "album",   // Changed from resource_type
        target_id: params.id,     // Changed from resource_id
        content: `a mis à jour l'album: ${formData.title}` 
      });
      toast({ title: "Album mis à jour", description: "Les modifications ont été enregistrées." });
      router.push(`/albums/${params.id}`); // Or /albums if preferred after edit

    } catch (error: any) {
      toast({ title: "Erreur de sauvegarde", description: error.message, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }

  const handleDelete = async () => { /* ... existing handleDelete ... */ }

  if (isLoadingData) { /* ... existing loading state ... */ }
  const validTracks = Array.isArray(tracks) ? tracks.filter(track => track && typeof track === 'object' && 'id' in track && 'title' in track) : [];

  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Modifier l'album</h1>
          <p className="text-muted-foreground">Modifiez les détails de votre album et organisez vos morceaux</p>
        </div>
        <div className="flex items-center gap-2">
          <AlertDialog>
            <AlertDialogTrigger asChild><Button variant="destructive"><Trash2 className="mr-2 h-4 w-4" />Supprimer</Button></AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader><AlertDialogTitle>Êtes-vous sûr de vouloir supprimer cet album ?</AlertDialogTitle><AlertDialogDescription>Cette action est irréversible. L'album sera définitivement supprimé.</AlertDialogDescription></AlertDialogHeader>
              <AlertDialogFooter><AlertDialogCancel>Annuler</AlertDialogCancel><AlertDialogAction onClick={handleDelete} disabled={isDeleting} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">{isDeleting ? "Suppression..." : "Supprimer"}</AlertDialogAction></AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          <Button variant="outline" onClick={() => router.back()}>Annuler</Button>
          <Button variant="outline" onClick={() => handleSave("draft")} disabled={isLoading}><Save className="mr-2 h-4 w-4" />Enregistrer comme brouillon</Button>
          <Button onClick={() => handleSave("published")} disabled={isLoading}>Publier</Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader><CardTitle className="flex items-center"><Info className="mr-2 h-5 w-5" />Informations de l'album</CardTitle><CardDescription>Modifiez les informations de base de votre album</CardDescription></CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2"><Label htmlFor="title">Titre de l'album *</Label><Input id="title" name="title" value={formData.title} onChange={handleInputChange} placeholder="Entrez le titre de votre album" required /></div>
              <div className="space-y-2"><Label htmlFor="description">Description</Label><Textarea id="description" name="description" value={formData.description} onChange={handleInputChange} placeholder="Décrivez votre album" rows={4} /></div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="genres">Genres</Label>
                  <MultiSelect options={genreOptions} selected={formData.genres} onChange={(selected) => setFormData((prev) => ({ ...prev, genres: selected }))} placeholder="Sélectionnez des genres" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="moods">Ambiances / Moods</Label>
                  <MultiSelect options={moodOptions} selected={formData.moods} onChange={(selected) => setFormData((prev) => ({ ...prev, moods: selected }))} placeholder="Sélectionnez des ambiances" />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="instrumentation">Instrumentation</Label>
                  <MultiSelect options={instrumentationOptions} selected={formData.instrumentation} onChange={(selected) => setFormData((prev) => ({ ...prev, instrumentation: selected }))} placeholder="Sélectionnez des instruments" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="album_type">Type d'album</Label>
                  <Select value={formData.album_type} onValueChange={(value) => setFormData(prev => ({ ...prev, album_type: value }))}>
                    <SelectTrigger id="album_type">
                      <SelectValue placeholder="Sélectionnez un type" />
                    </SelectTrigger>
                    <SelectContent>
                      {albumTypeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="releaseDate">Date de sortie</Label>
                  <Input id="releaseDate" name="releaseDate" type="date" value={formData.releaseDate} onChange={handleInputChange} />
                </div>
                <div className="space-y-2"><Label htmlFor="label">Label</Label><Input id="label" name="label" value={formData.label} onChange={handleInputChange} placeholder="Nom du label (optionnel)" /></div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2"><Label htmlFor="upc">Code UPC</Label><Input id="upc" name="upc" value={formData.upc} onChange={handleInputChange} placeholder="Code UPC (optionnel)" /></div>
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">{tags.map((tag) => ( <Badge key={tag} variant="secondary" className="flex items-center gap-1">{tag}<X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} /></Badge> ))}</div>
                <div className="flex gap-2">
                  <Input value={currentTag} onChange={(e) => setCurrentTag(e.target.value)} placeholder="Ajouter un tag" onKeyDown={(e) => { if (e.key === "Enter") { e.preventDefault(); addTag(); }}} />
                  <Button type="button" variant="outline" onClick={addTag}><Tag className="h-4 w-4" /></Button>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader><CardTitle className="flex items-center"><Upload className="mr-2 h-5 w-5" />Pochette de l'album</CardTitle><CardDescription>Modifiez l'image de couverture de votre album</CardDescription></CardHeader>
            <CardContent>
              <ImageUploader onImageUploaded={handleCoverUpload} existingImageUrl={formData.coverUrl} aspectRatio="square" maxWidth={1000} maxHeight={1000} bucketName="album-covers" />
              <p className="text-xs text-muted-foreground mt-2">Format recommandé : image carrée de haute qualité (minimum 1000x1000 pixels)</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div><CardTitle className="flex items-center"><Music className="mr-2 h-5 w-5" />Liste des morceaux</CardTitle><CardDescription>Organisez les morceaux de votre album</CardDescription></div>
              <div className="flex gap-2">
                <Button onClick={() => setIsAddSongModalOpen(true)} size="sm" variant="outline"><Plus className="mr-2 h-4 w-4" />Ajouter existant</Button>
                <Button onClick={addTrack} size="sm"><Plus className="mr-2 h-4 w-4" />Créer nouveau</Button>
              </div>
            </CardHeader>
            <CardContent>{validTracks.length === 0 ? <div className="text-center py-8 border border-dashed rounded-md"><Disc className="h-8 w-8 mx-auto text-muted-foreground mb-2" /><p className="text-muted-foreground">Aucun morceau ajouté</p><Button onClick={() => setIsAddSongModalOpen(true)} variant="outline" className="mt-4"><Plus className="mr-2 h-4 w-4" />Ajouter un morceau existant</Button></div> : <DragDropContext onDragEnd={handleDragEnd}><Droppable droppableId="tracks">{(provided) => (<div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2"><div className="grid grid-cols-12 text-xs font-medium text-muted-foreground px-2 py-1"><div className="col-span-1">#</div><div className="col-span-7">Titre</div><div className="col-span-3">Durée</div><div className="col-span-1"></div></div>{validTracks.map((track, index) => (<Draggable key={track.id} draggableId={track.id} index={index}>{(provided) => (<div ref={provided.innerRef} {...provided.draggableProps} className="grid grid-cols-12 items-center border rounded-md p-2"><div className="col-span-1 flex items-center"><div {...provided.dragHandleProps} className="cursor-grab"><DragVertical className="h-4 w-4 text-muted-foreground" /></div></div><div className="col-span-7"><Input value={track.title} onChange={(e) => updateTrack(track.id, "title", e.target.value)} placeholder="Titre du morceau" className="border-0 p-0 h-8 focus-visible:ring-0" /></div><div className="col-span-3 flex items-center gap-2"><Input type="number" value={Math.floor(track.duration / 60)} onChange={(e) => updateTrack(track.id, "duration", Number.parseInt(e.target.value) * 60 + (track.duration % 60))} placeholder="min" className="w-16 text-center" min={0} /><span>:</span><Input type="number" value={track.duration % 60} onChange={(e) => updateTrack(track.id, "duration", Math.floor(track.duration / 60) * 60 + Number.parseInt(e.target.value))} placeholder="sec" className="w-16 text-center" min={0} max={59} /></div><div className="col-span-1 flex justify-end"><Button variant="ghost" size="icon" onClick={() => removeTrack(track.id)} className="h-8 w-8"><X className="h-4 w-4" /></Button></div></div>)}</Draggable>))}{provided.placeholder}</div>)}</Droppable></DragDropContext>}{validTracks.length > 0 && <div className="flex justify-between items-center mt-4 pt-4 border-t"><div className="text-sm"><span className="font-medium">{validTracks.length}</span> morceau{validTracks.length > 1 ? "x" : ""}</div><div className="text-sm">Durée totale: <span className="font-medium">{formatDuration(calculateTotalDuration())}</span></div></div>}</CardContent>
          </Card>

          {/* Modal to Add Existing Songs */}
          <Dialog open={isAddSongModalOpen} onOpenChange={setIsAddSongModalOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Ajouter des morceaux existants à l'album</DialogTitle>
                <DialogDescription>Sélectionnez les morceaux publiés de votre bibliothèque à inclure dans cet album.</DialogDescription>
              </DialogHeader>
              <div className="max-h-[400px] overflow-y-auto py-4 space-y-2">
                {availableSongs.filter(s => !tracks.some(t => t.songId === s.id)).length > 0 ? 
                  availableSongs
                    .filter(s => !tracks.some(t => t.songId === s.id)) // Filter out songs already in the album tracks UI
                    .map(song => (
                      <div key={song.id} className="flex items-center justify-between p-2 rounded-md hover:bg-accent/50">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                            {song.cover_url ? <img src={song.cover_url} alt={song.title} className="h-10 w-10 rounded-md object-cover" /> : <Music className="h-5 w-5 text-primary" />}
                          </div>
                          <div>
                            <div className="font-medium">{song.title}</div>
                            <div className="text-xs text-muted-foreground">{song.duration ? formatDuration(song.duration) : ""}</div>
                          </div>
                        </div>
                        <Button variant="outline" size="sm" onClick={() => handleAddSelectedSongsToAlbum([song.id])}>Ajouter</Button>
                      </div>
                    )) 
                  : <p className="text-muted-foreground text-center">Aucun autre morceau disponible ou tous vos morceaux sont déjà ajoutés.</p>
                }
              </div>
              {/* Optional: Add a footer with a button to add multiple selected songs if implementing multi-select in modal */}
            </DialogContent>
          </Dialog>

          <Card>
            <CardHeader><CardTitle>Options de publication</CardTitle><CardDescription>Définissez comment votre album sera partagé</CardDescription></CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4"><h3 className="text-sm font-medium">Visibilité</h3><div className="space-y-2"><div className="flex items-center justify-between space-x-2"><Label htmlFor="isPublic" className="flex flex-col gap-1"><span>Public</span><span className="font-normal text-xs text-muted-foreground">Visible par tous les utilisateurs</span></Label><Switch id="isPublic" checked={formData.isPublic} onCheckedChange={(checked) => handleSwitchChange("isPublic", checked)} /></div></div></div>
              <Separator />
              <div className="space-y-4"><h3 className="text-sm font-medium">Options de promotion</h3><div className="space-y-2"><div className="flex items-center justify-between space-x-2"><Label htmlFor="notifyFollowers" className="flex flex-col gap-1"><span>Notifier mes abonnés</span><span className="font-normal text-xs text-muted-foreground">Envoyer une notification à vos abonnés</span></Label><Switch id="notifyFollowers" checked={formData.notifyFollowers} onCheckedChange={(checked) => handleSwitchChange("notifyFollowers", checked)} /></div><div className="flex items-center justify-between space-x-2"><Label htmlFor="addToDiscovery" className="flex flex-col gap-1"><span>Soumettre à "Découvertes"</span><span className="font-normal text-xs text-muted-foreground">Pour considération éditoriale</span></Label><Switch id="addToDiscovery" checked={formData.addToDiscovery} onCheckedChange={(checked) => handleSwitchChange("addToDiscovery", checked)} /></div></div></div>
            </CardContent>
          </Card>
        </div>
        <div className="space-y-6">
          <Card>
            <CardHeader><CardTitle>Prévisualisation</CardTitle><CardDescription>Aperçu de votre album</CardDescription></CardHeader>
            <CardContent className="flex flex-col items-center">
              <div className="w-full max-w-[240px] aspect-square rounded-md overflow-hidden bg-muted mb-4">{formData.coverUrl ? <img src={formData.coverUrl} alt="Pochette" className="w-full h-full object-cover" /> : <div className="w-full h-full flex items-center justify-center bg-primary/10"><Disc className="h-16 w-16 text-primary/40" /></div>}</div>
              <h3 className="text-xl font-bold">{formData.title || "Titre de l'album"}</h3>
              <p className="text-muted-foreground text-sm text-center">Genres: {formData.genres.length > 0 ? formData.genres.map((g) => genreOptions.find((opt) => opt.value === g)?.label || g).join(", ") : "N/A"}</p>
              <p className="text-muted-foreground text-sm text-center">Moods: {formData.moods.length > 0 ? formData.moods.map((m) => moodOptions.find((opt) => opt.value === m)?.label || m).join(", ") : "N/A"}</p>
              <p className="text-muted-foreground text-xs text-center">Instruments: {formData.instrumentation.length > 0 ? formData.instrumentation.map((i) => instrumentationOptions.find((opt) => opt.value === i)?.label || i).join(", ") : "N/A"}</p>
              <div className="flex flex-wrap gap-1 mt-4 justify-center">{tags.map((tag) => ( <Badge key={tag} variant="secondary">{tag}</Badge> ))}</div>
              {validTracks.length > 0 && <div className="w-full mt-6 space-y-2"><h4 className="text-sm font-medium">Morceaux</h4><div className="space-y-1">{validTracks.map((track, index) => (<div key={track.id} className="flex justify-between text-sm py-1 border-b border-muted"><div className="flex items-center gap-2"><span className="text-muted-foreground">{index + 1}.</span><span>{track.title}</span></div><span className="text-muted-foreground">{formatDuration(track.duration)}</span></div>))}</div></div>}
            </CardContent>
          </Card>
          <Card>
            <CardHeader><CardTitle>Statistiques</CardTitle></CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between"><span className="text-sm">Écoutes totales</span><span className="font-medium">0</span></div>
              <div className="flex items-center justify-between"><span className="text-sm">Likes</span><span className="font-medium">0</span></div>
              <div className="flex items-center justify-between"><span className="text-sm">Commentaires</span><span className="font-medium">0</span></div>
              <div className="flex items-center justify-between"><span className="text-sm">Ajouts aux playlists</span><span className="font-medium">0</span></div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
