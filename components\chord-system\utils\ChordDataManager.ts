/**
 * 🎼 CHORD DATA MANAGER - Gestionnaire de Données d'Accords
 * 
 * Gestionnaire centralisé pour le chargement et la mise en cache
 * des données d'accords depuis les fichiers JSON
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

import type { 
  ChordJsonDefinition, 
  InstrumentType,
  ChordDataValidator,
  ErrorHandler 
} from '../types/chord-system';

// ============================================================================
// CONFIGURATION
// ============================================================================

/** Configuration des chemins de fichiers par instrument */
const INSTRUMENT_FILE_PATHS: Record<InstrumentType, string[]> = {
  guitar: [
    'lib/chords/guitar_complete_extended.json',
    'lib/chords/guitar_standard_tuning.json',
    'lib/chords/guitar.json'
  ],
  piano: [
    'lib/chords/piano_complete.json',
    'lib/chords/piano.json'
  ],
  ukulele: [
    'lib/chords/ukulele_gcea_complete.json',
    'lib/chords/ukulele_standard_gcea_tuning.json',
    'lib/chords/ukulele.json'
  ],
  mandolin: [
    'lib/chords/mandolin_gdae_tuning.json',
    'lib/chords/mandolin_complete.json',
    'lib/chords/mandolin.json'
  ],
  banjo: [
    'lib/chords/banjo_5string_complete.json',
    'lib/chords/banjo_open_g.json',
    'lib/chords/banjo.json'
  ],
  bass: [
    'lib/chords/bass_4string_complete.json',
    'lib/chords/bass_standard.json',
    'lib/chords/bass.json'
  ]
};

/** Accordages spécifiques par instrument */
const TUNING_FILE_VARIANTS: Record<string, string[]> = {
  'guitar-drop_d': ['guitar_drop_d.json', 'guitar_drop_d_tuning.json'],
  'guitar-open_g': ['guitar_open_g.json', 'guitar_open_g_tuning.json'],
  'guitar-dadgad': ['guitar_dadgad.json', 'guitar_dadgad_tuning.json'],
  'ukulele-gcea': ['ukulele_gcea_complete.json', 'ukulele_standard_gcea_tuning.json'],
  'mandolin-gdae': ['mandolin_gdae_tuning.json', 'mandolin_gdae_complete.json'],
  'banjo-open_g': ['banjo_5string_complete.json', 'banjo_open_g.json']
};

/** Données de fallback pour chaque instrument */
const FALLBACK_DATA: Record<InstrumentType, Partial<ChordJsonDefinition>> = {
  guitar: {
    instrument: 'guitar',
    tuning: ['E', 'A', 'D', 'G', 'B', 'E'],
    strings: 6,
    keys: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
    suffixes: ['major', 'minor', '7'],
    chords: {
      'C': [{
        suffix: 'major',
        name: 'Cmajor',
        positions: [{
          frets: ['x', 3, 2, 0, 1, 0],
          fingers: [0, 3, 2, 0, 1, 0],
          baseFret: 1,
          barres: [],
          difficulty: 'beginner'
        }]
      }]
    }
  },
  piano: {
    instrument: 'piano',
    tuning: [],
    strings: 0,
    keys: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
    suffixes: ['major', 'minor', '7'],
    chords: {
      'C': [{
        suffix: 'major',
        name: 'Cmajor',
        positions: [{
          frets: [],
          midi: [60, 64, 67],
          difficulty: 'beginner'
        }]
      }]
    }
  },
  ukulele: {
    instrument: 'ukulele',
    tuning: ['G', 'C', 'E', 'A'],
    strings: 4,
    keys: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
    suffixes: ['major', 'minor', '7'],
    chords: {
      'C': [{
        suffix: 'major',
        name: 'Cmajor',
        positions: [{
          frets: [0, 0, 0, 3],
          fingers: [0, 0, 0, 1],
          baseFret: 1,
          barres: [],
          difficulty: 'beginner'
        }]
      }]
    }
  },
  mandolin: {
    instrument: 'mandolin',
    tuning: ['G', 'D', 'A', 'E'],
    strings: 4,
    keys: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
    suffixes: ['major', 'minor', '7'],
    chords: {
      'C': [{
        suffix: 'major',
        name: 'Cmajor',
        positions: [{
          frets: [3, 2, 0, 0],
          fingers: [3, 2, 0, 0],
          baseFret: 1,
          barres: [],
          difficulty: 'beginner'
        }]
      }]
    }
  },
  banjo: {
    instrument: 'banjo',
    tuning: ['D', 'G', 'B', 'D'],
    strings: 5,
    keys: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
    suffixes: ['major', 'minor', '7'],
    chords: {
      'C': [{
        suffix: 'major',
        name: 'Cmajor',
        positions: [{
          frets: [0, 2, 0, 1, 0],
          fingers: [0, 2, 0, 1, 0],
          baseFret: 1,
          barres: [],
          difficulty: 'beginner'
        }]
      }]
    }
  },
  bass: {
    instrument: 'bass',
    tuning: ['E', 'A', 'D', 'G'],
    strings: 4,
    keys: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
    suffixes: ['major', 'minor', '7'],
    chords: {
      'C': [{
        suffix: 'major',
        name: 'Cmajor',
        positions: [{
          frets: ['x', 3, 2, 0],
          fingers: [0, 3, 2, 0],
          baseFret: 1,
          barres: [],
          difficulty: 'beginner'
        }]
      }]
    }
  }
};

// ============================================================================
// CLASSE PRINCIPALE
// ============================================================================

export class ChordDataManager {
  private static cache = new Map<string, ChordJsonDefinition>();
  private static loadingPromises = new Map<string, Promise<ChordJsonDefinition>>();
  private static errorHandler: ErrorHandler = (error, context) => {
    console.error(`ChordDataManager Error${context ? ` (${context})` : ''}:`, error);
  };

  // ============================================================================
  // MÉTHODES PUBLIQUES
  // ============================================================================

  /**
   * Charge les données d'un instrument avec accordage spécifique
   */
  static async loadInstrument(
    instrument: InstrumentType, 
    tuning: string = 'standard'
  ): Promise<ChordJsonDefinition> {
    const cacheKey = `${instrument}-${tuning}`;
    
    // Vérifier le cache
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }
    
    // Vérifier si un chargement est en cours
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)!;
    }
    
    // Démarrer le chargement
    const loadingPromise = this.performLoad(instrument, tuning);
    this.loadingPromises.set(cacheKey, loadingPromise);
    
    try {
      const result = await loadingPromise;
      this.cache.set(cacheKey, result);
      return result;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * Précharge les instruments populaires
   */
  static async preloadPopularInstruments(): Promise<void> {
    const popular: InstrumentType[] = ['guitar', 'piano', 'ukulele'];
    
    const promises = popular.map(instrument => 
      this.loadInstrument(instrument).catch(error => {
        this.errorHandler(error, `preload ${instrument}`);
        return this.getFallbackData(instrument);
      })
    );
    
    await Promise.allSettled(promises);
  }

  /**
   * Obtient les variantes de fichiers pour un instrument/accordage
   */
  static getFileVariants(instrument: InstrumentType, tuning: string): string[] {
    const variants: string[] = [];
    
    // Variantes spécifiques à l'accordage
    const tuningKey = `${instrument}-${tuning}`;
    if (TUNING_FILE_VARIANTS[tuningKey]) {
      variants.push(...TUNING_FILE_VARIANTS[tuningKey].map(file => `lib/chords/${file}`));
    }
    
    // Variantes génériques de l'instrument
    if (INSTRUMENT_FILE_PATHS[instrument]) {
      variants.push(...INSTRUMENT_FILE_PATHS[instrument]);
    }
    
    return variants;
  }

  /**
   * Vide le cache
   */
  static clearCache(): void {
    this.cache.clear();
    this.loadingPromises.clear();
  }

  /**
   * Obtient les statistiques du cache
   */
  static getCacheStats(): {
    size: number;
    keys: string[];
    memoryUsage: number;
  } {
    const keys = Array.from(this.cache.keys());
    const memoryUsage = JSON.stringify(Array.from(this.cache.values())).length;
    
    return {
      size: this.cache.size,
      keys,
      memoryUsage
    };
  }

  /**
   * Définit un gestionnaire d'erreur personnalisé
   */
  static setErrorHandler(handler: ErrorHandler): void {
    this.errorHandler = handler;
  }

  /**
   * Valide les données d'un instrument
   */
  static validateInstrumentData(data: unknown): data is ChordJsonDefinition {
    if (!data || typeof data !== 'object') return false;
    
    const definition = data as any;
    
    return !!(
      typeof definition.instrument === 'string' &&
      Array.isArray(definition.tuning) &&
      typeof definition.strings === 'number' &&
      Array.isArray(definition.keys) &&
      Array.isArray(definition.suffixes) &&
      definition.chords &&
      typeof definition.chords === 'object'
    );
  }

  // ============================================================================
  // MÉTHODES PRIVÉES
  // ============================================================================

  /**
   * Effectue le chargement réel des données
   */
  private static async performLoad(
    instrument: InstrumentType, 
    tuning: string
  ): Promise<ChordJsonDefinition> {
    const variants = this.getFileVariants(instrument, tuning);
    
    // Essayer chaque variante de fichier
    for (const filePath of variants) {
      try {
        const data = await this.loadChordFile(filePath);
        
        if (this.validateInstrumentData(data)) {
          return data as ChordJsonDefinition;
        } else {
          this.errorHandler(
            new Error('Données invalides'), 
            `validation ${filePath}`
          );
        }
      } catch (error) {
        this.errorHandler(error as Error, `load ${filePath}`);
        // Continuer avec le fichier suivant
      }
    }
    
    // Si aucun fichier n'a pu être chargé, utiliser les données de fallback
    this.errorHandler(
      new Error(`Aucun fichier trouvé pour ${instrument}-${tuning}`),
      'fallback'
    );
    
    return this.getFallbackData(instrument);
  }

  /**
   * Charge un fichier JSON spécifique
   */
  private static async loadChordFile(filePath: string): Promise<unknown> {
    // Environnement navigateur
    if (typeof window !== 'undefined') {
      const response = await fetch(`/${filePath}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    }
    
    // Environnement Node.js (pour les tests)
    if (typeof require !== 'undefined') {
      try {
        return require(`../../../${filePath}`);
      } catch (error) {
        // Essayer avec un chemin relatif différent
        return require(`../../../../${filePath}`);
      }
    }
    
    throw new Error('Environnement non supporté pour le chargement de fichiers');
  }

  /**
   * Obtient les données de fallback pour un instrument
   */
  private static getFallbackData(instrument: InstrumentType): ChordJsonDefinition {
    const fallback = FALLBACK_DATA[instrument];
    
    return {
      instrument: fallback.instrument!,
      tuning: fallback.tuning!,
      strings: fallback.strings!,
      keys: fallback.keys!,
      suffixes: fallback.suffixes!,
      chords: fallback.chords!,
      fretRange: [0, 24] // Valeur par défaut
    };
  }

  /**
   * Nettoie le cache selon une stratégie LRU
   */
  private static cleanupCache(maxSize: number = 20): void {
    if (this.cache.size <= maxSize) return;
    
    // Supprimer les entrées les plus anciennes
    const entries = Array.from(this.cache.entries());
    const toRemove = entries.slice(0, entries.length - maxSize);
    
    toRemove.forEach(([key]) => {
      this.cache.delete(key);
    });
  }
}

// ============================================================================
// FONCTIONS UTILITAIRES EXPORTÉES
// ============================================================================

/**
 * Charge un instrument de manière asynchrone avec gestion d'erreur
 */
export async function loadInstrumentSafely(
  instrument: InstrumentType,
  tuning?: string
): Promise<{ data: ChordJsonDefinition | null; error: string | null }> {
  try {
    const data = await ChordDataManager.loadInstrument(instrument, tuning);
    return { data, error: null };
  } catch (error) {
    return { 
      data: null, 
      error: error instanceof Error ? error.message : 'Erreur inconnue' 
    };
  }
}

/**
 * Vérifie si un instrument est disponible
 */
export async function isInstrumentAvailable(
  instrument: InstrumentType,
  tuning?: string
): Promise<boolean> {
  const { data } = await loadInstrumentSafely(instrument, tuning);
  return data !== null;
}

/**
 * Obtient la liste des instruments disponibles
 */
export function getAvailableInstruments(): InstrumentType[] {
  return Object.keys(INSTRUMENT_FILE_PATHS) as InstrumentType[];
}

/**
 * Obtient la liste des accordages disponibles pour un instrument
 */
export function getAvailableTunings(instrument: InstrumentType): string[] {
  const tunings = ['standard']; // Accordage par défaut
  
  // Ajouter les accordages spécifiques
  Object.keys(TUNING_FILE_VARIANTS).forEach(key => {
    if (key.startsWith(`${instrument}-`)) {
      const tuning = key.replace(`${instrument}-`, '');
      if (!tunings.includes(tuning)) {
        tunings.push(tuning);
      }
    }
  });
  
  return tunings;
}
