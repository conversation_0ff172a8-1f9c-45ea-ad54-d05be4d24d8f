"use client";

import React, { useEffect, useState, useCallback, useRef, useImperativeHandle } from 'react'; // Added React and useImperativeHandle
import { useRouter } from 'next/navigation'; 
import { SupabaseClient } from '@supabase/supabase-js'; 
import { useForm, FormProvider as RHFFormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from "@/hooks/use-toast"; 

import { Button } from "@/components/ui/button"; 
import { MultiSelect } from '@/components/ui/multi-select';
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { ScrollArea } from '@/components/ui/scroll-area'; 
import { Cog, Loader2 as LoadingSpinner, Music2, UploadCloud, History, ChevronDown, ChevronUp, Languages, Users as UsersIcon, Brain, Layers, PanelRightClose } from 'lucide-react'; 
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from '@/components/ui/switch'; 
import { DatePicker } from "@/components/ui/date-picker"; 
import Image from 'next/image';
import { AiConfigMenu } from "@/components/ia/ai-config-menu";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { AiQuickActions } from "@/components/ia/ai-quick-actions";
import AudioWaveformPreview from '@/components/audio-waveform-preview';
import { RichLyricsEditor } from "@/components/ui/rich-lyrics-editor";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"; 
import type Quill from 'quill'; 
import { genreOptions, moodOptions, tagOptions, instrumentationOptions, languageOptions } from '@/lib/constants/song-options';
import { MetadataFields, MetadataFieldConfig } from '@/components/shared/MetadataFields';
import type { Song } from '@/types';
import SongVault, { SongVaultActions } from '@/components/song-vault'; 
import SongProgressTracker, { SongProgressData } from './SongProgressTracker'; // Import progress tracker

// Constants
export const musicalKeys = [
  "C", "C#", "Db", "D", "D#", "Eb", "E", "F", "F#", "Gb", "G", "G#", "Ab", "A", "A#", "Bb", "B",
  "Am", "A#m", "Bbm", "Bm", "Cm", "C#m", "Dbm", "Dm", "D#m", "Ebm", "Em", "Fm", "F#m", "Gbm", "Gm", "G#m", "Abm"
];
export const timeSignatures = ["2/4", "3/4", "4/4", "5/4", "6/8", "7/8", "9/8", "12/8", "Autre"];
export const NO_ALBUM_SELECTED_VALUE = "__NO_ALBUM__";
export const LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY = "ai_selected_provider_mouvik";
export const LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY = "ollama_selected_model_mouvik";
export const LOCAL_STORAGE_OPENAI_SELECTED_MODEL_KEY = "openai_selected_model_mouvik";
export const LOCAL_STORAGE_OPENROUTER_SELECTED_MODEL_KEY = "openrouter_selected_model_mouvik";
export const LOCAL_STORAGE_ANTHROPIC_SELECTED_MODEL_KEY = "anthropic_selected_model_mouvik";

export interface AiConfig {
  provider: 'ollama' | 'openai' | 'openrouter' | 'anthropic' | string;
  model: string;
  temperature: number;
}

export interface AiHistoryItem {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: Date; 
}

export const songFormSchema = z.object({
  title: z.string().min(1, { message: "Le titre est requis." }),
  artist: z.string().min(1, { message: "Le nom de l'artiste est requis." }),
  featured_artists: z.string().optional(), // Re-added
  genre: z.string().optional(),
  subgenre: z.string().optional(),
  mood: z.string().optional(),
  theme: z.string().optional(),
  instrumentation: z.array(z.string()).optional(), 
  key: z.string().optional(), 
  bpm: z.number().min(0).nullable().optional(),
  duration_ms: z.number().min(0).nullable().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(), 
  audio_url: z.string().optional().nullable(),
  cover_art_url: z.string().optional().nullable(),
  album_id: z.string().nullable().optional(),
  composer_name: z.string().optional(), // Re-added
  writers: z.string().optional(), // Re-added
  producers: z.string().optional(), // Re-added
  record_label: z.string().optional(), // Re-added
  isrc: z.string().optional(), // Re-added
  upc: z.string().optional(), // Re-added
  release_date: z.date().optional().nullable(),
  lyrics: z.string().optional(),
  credits: z.any().optional(), // For JSONB - remains
  editor_data: z.any().optional(), // For JSONB
  is_public: z.boolean().default(false).optional(),
  visibility: z.string().optional(), 
  is_archived: z.boolean().default(false).optional(),
  is_explicit: z.boolean().default(false).optional(), 
});
export type SongFormValues = z.infer<typeof songFormSchema>;

export interface Album {
  id: string;
  title: string;
}

interface SongFormProps {
  mode: 'create' | 'edit';
  initialValues?: Partial<SongFormValues & { id?: string; user_id?: string; tempo?: string }>; 
  onFormSubmit: (data: SongFormValues, status?: 'draft' | 'published') => Promise<void>;
  isSubmitting: boolean;
  albums: Album[];
  isLoadingAlbums: boolean;
  supabaseClient: SupabaseClient;
}

export interface SongFormHandle {
  getVaultActions: () => SongVaultActions | null;
}

export const SongForm = React.forwardRef<SongFormHandle, SongFormProps>(({
  mode,
  initialValues,
  onFormSubmit,
  isSubmitting,
  albums,
  isLoadingAlbums,
  supabaseClient,
}, ref) => {
  const router = useRouter();

  const form = useForm<SongFormValues>({
    resolver: zodResolver(songFormSchema),
    defaultValues: mode === 'create' ? {
      title: "",
      artist: "",
      featured_artists: "", // Re-added
      genre: "",
      subgenre: "",
      mood: "",
      theme: "",
      instrumentation: [],
      key: "",
      bpm: undefined, 
      duration_ms: null,
      description: "",
      tags: [],
      audio_url: null,
      cover_art_url: null,
      album_id: null,
      composer_name: "", // Re-added
      writers: "", // Re-added
      producers: "", // Re-added
      record_label: "", // Re-added
      isrc: "", // Re-added
      upc: "", // Re-added
      release_date: null,
      lyrics: "",
      credits: '', // Default to empty string for textarea
      editor_data: '', // Default to empty string for textarea
      is_public: false,
      visibility: 'private',
      is_archived: false,
      is_explicit: false,
    } : {
      ...initialValues,
      // Map re-added fields from initialValues or initialValues.credits later in useEffect
      featured_artists: initialValues?.featured_artists || "",
      composer_name: initialValues?.composer_name || "",
      writers: initialValues?.writers || "",
      producers: initialValues?.producers || "",
      record_label: initialValues?.record_label || "",
      isrc: initialValues?.isrc || "",
      upc: initialValues?.upc || "",
      artist: initialValues?.artist || "",
      duration_ms: initialValues?.duration_ms || null,
      genre: initialValues?.genre || "",
      subgenre: initialValues?.subgenre || "",
      mood: initialValues?.mood || "",
      theme: initialValues?.theme || "",
      instrumentation: Array.isArray(initialValues?.instrumentation) ? initialValues.instrumentation : [],
      tags: Array.isArray(initialValues?.tags) ? initialValues.tags : [],
      cover_art_url: initialValues?.cover_art_url || null,
      credits: initialValues?.credits || {}, // Or null
      editor_data: initialValues?.editor_data || {}, // Or null
      is_public: !!initialValues?.is_public,
      visibility: initialValues?.visibility || 'private',
      is_archived: !!initialValues?.is_archived,
      is_explicit: !!initialValues?.is_explicit,
      release_date: initialValues?.release_date ? new Date(initialValues.release_date) : null,
      // Fields removed from schema are not mapped here
    },
    mode: 'onChange',
  });

  const [uploadingAudio, setUploadingAudio] = useState(false);
  const [uploadedAudioUrl, setUploadedAudioUrl] = useState<string | null>(initialValues?.audio_url || null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadedCoverUrl, setUploadedCoverUrl] = useState<string | null>(initialValues?.cover_art_url || null); 
  
  const audioFileInputRef = useRef<HTMLInputElement>(null);
  const imageFileInputRef = useRef<HTMLInputElement>(null);
  const quillRef = useRef<any>(null); 

  const [lyricsContent, setLyricsContent] = useState<string>(initialValues?.lyrics || "");
  const [currentSelectionRange, setCurrentSelectionRange] = useState<any>(null); 
  const [aiConfig, setAiConfig] = useState<AiConfig>({
    provider: 'ollama', model: '', temperature: 0.7,
  });
  const [aiLoading, setAiLoading] = useState(false);
  const [aiLastResult, setAiLastResult] = useState<string | undefined>(undefined);
  const [aiError, setAiError] = useState<string | undefined>(undefined);
  const [aiHistory, setAiHistory] = useState<AiHistoryItem[]>([]);
  const [aiMaxOutputTokens, setAiMaxOutputTokens] = useState<number>(256);
  const [aiGeneralPrompt, setAiGeneralPrompt] = useState<string>("You are a helpful assistant for songwriting.");
  const [showAiConfigMenu, setShowAiConfigMenu] = useState(false);
  const [isAiPanelCollapsed, setIsAiPanelCollapsed] = useState(false);
  const [showAiHistory, setShowAiHistory] = useState(false); 
  const [isVaultPanelCollapsed, setIsVaultPanelCollapsed] = useState(true); 
  const songVaultActionsRef = useRef<SongVaultActions | null>(null); 

  useImperativeHandle(ref, () => ({
    getVaultActions: () => {
      return songVaultActionsRef.current;
    }
  }));

  const handleWaveformReady = useCallback((duration: number) => {
    const currentDurationInForm = form.getValues('duration_ms');
    // Assuming duration from waveform is in seconds, convert to milliseconds
    const newDurationMs = Math.round(duration * 1000);

    if (newDurationMs > 0) {
      if (currentDurationInForm === null || currentDurationInForm !== newDurationMs) {
        form.setValue('duration_ms', newDurationMs, { shouldValidate: true, shouldDirty: true });
        console.log("Audio duration (ms) set in form:", newDurationMs);
      }
    } else if (newDurationMs === 0 && currentDurationInForm !== null) {
      // If received duration is 0 (e.g. invalid source) and form had a duration, clear it.
      form.setValue('duration_ms', null, { shouldValidate: true, shouldDirty: true });
      console.log("Audio duration (ms) reset in form due to invalid source or 0 duration.");
    }
    // If newDurationMs is 0 and currentDurationInForm is already null or 0, do nothing.
  }, [form]); // form instance from useForm is stable

  const stripHtml = (html: string): string => {
    if (typeof document !== "undefined") {
      const doc = new DOMParser().parseFromString(html, 'text/html');
      return doc.body.textContent || "";
    }
    return html.replace(/<[^>]+>/g, ''); 
  };

  const handleLyricsChange = (newContent: string) => {
    setLyricsContent(newContent);
    form.setValue('lyrics', newContent, { shouldValidate: true, shouldDirty: true });
  };

  const handleEditorSelectionChange = (range: any, source: any, editor: any) => {
    setCurrentSelectionRange(range); 
  };

  const addAiHistory = (userPrompt: string, assistantResponse: string) => {
    setAiHistory((prev: AiHistoryItem[]) => [
      ...prev, 
      { role: 'user', content: userPrompt, timestamp: new Date() }, 
      { role: 'assistant', content: assistantResponse, timestamp: new Date() }
    ]);
  };

  const handleAiFormatLayout = async () => {
    console.log("Trigger AI Format Layout");
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    const currentHtmlContent = lyricsContent; 
    if (!currentHtmlContent.trim()) {
      toast({ title: "Formatage IA", description: "Aucun contenu à formater.", variant: "destructive" });
      setAiLoading(false); return;
    }
    const promptForAI = `You are a text formatting assistant for musicians... Input HTML:\n\`\`\`html\n${currentHtmlContent}\n\`\`\`\n\nFormatted Output:`;
    try {
      await new Promise(resolve => setTimeout(resolve, 1500)); 
      const aiResponseText = `[VERSE]\nC          G\nLyrics line one.\nAm           F\nAnother line here.`;
      setAiLastResult("Formatage simulé appliqué.");
      const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`;
      handleLyricsChange(newHtmlContent); 
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Formatage appliqué!"});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur de formatage", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  };

  const handleAiGeneralSuggestions = async () => {
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    const formData = form.getValues(); 
    const currentLyricsText = stripHtml(lyricsContent);
    const context = `Current Song Context:
- Title: ${formData.title || 'N/A'}
- Artist: ${formData.artist || 'N/A'}
- Featured Artists: ${formData.featured_artists || 'N/A'}
- Composer: ${formData.composer_name || 'N/A'}
- Genre: ${formData.genre || 'N/A'}
- Subgenre: ${formData.subgenre || 'N/A'}
- Mood: ${formData.mood || 'N/A'}
- Theme: ${formData.theme || 'N/A'}
- Key: ${formData.key || 'N/A'}
- BPM: ${formData.bpm || 'N/A'}
- Instrumentation: ${formData.instrumentation?.join(', ') || 'N/A'}
- Tags: ${formData.tags?.join(', ') || 'N/A'}
Current Lyrics:
\`\`\`
${currentLyricsText || '(No lyrics yet)'}
\`\`\``;
    const promptForAI = `${aiGeneralPrompt}\nBased on the following song context and lyrics, provide general suggestions... ${context}\nSuggestions:`;
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      const aiResponseText = `[AI Sim Suggestions based on Genre: ${formData.genre || 'N/A'} and Mood: ${formData.mood || 'N/A'}]\n1. Consider adding a bridge for the current theme: ${formData.theme || 'N/A'}.\n2. Explore the mood of '${formData.mood || 'mood'}' more.\n3. A chord progression like Am-G-C-F might fit the ${formData.genre || 'main genre'}.`;
      setAiLastResult(aiResponseText); addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Suggestions générées!"});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur de suggestion", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  };

  const handleAiRhymeSuggestions = async () => {
    const selection = currentSelectionRange; const editor = quillRef.current?.getEditor(); let selectedText = '';
    if (selection && selection.length > 0 && editor) { selectedText = editor.getText(selection.index, selection.length); } 
    else { toast({ title: "Suggestions de Rimes", description: "Veuillez sélectionner du texte.", variant: "destructive" }); return; }
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    const promptForAI = `${aiGeneralPrompt}\n\nSuggest rhymes for the last word of: "${selectedText}"`;
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Rhymes for "${selectedText}"]: suggestion1, suggestion2`;
      setAiLastResult(aiResponseText); addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Suggestions de rimes!"});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur rimes", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  };
  
  const handleAiMelodySuggestion = async () => {
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    const formData = form.getValues(); 
    const currentLyricsText = stripHtml(lyricsContent);
    const context = `Suggest melody ideas for:
- Genre: ${formData.genre || 'N/A'}
- Subgenre: ${formData.subgenre || 'N/A'}
- Mood: ${formData.mood || 'N/A'}
- Theme: ${formData.theme || 'N/A'}
- Lyrics: ${currentLyricsText.substring(0, 100)}...`;
    const promptForAI = `${aiGeneralPrompt}\n\n${context}`;
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Melody Idea]: Try ascending melody for verses, catchy for chorus, fitting ${formData.mood || 'mood'}.`;
      setAiLastResult(aiResponseText); addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Suggestion mélodie!"});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur mélodie", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  };
  
  const handleAiRecordingAdvice = async () => {
     setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
     const formData = form.getValues();
     const context = `Recording advice for:
- Genre: ${formData.genre || 'N/A'}
- Subgenre: ${formData.subgenre || 'N/A'}
- Instrumentation: ${formData.instrumentation?.join(', ') || 'N/A'}`;
     const promptForAI = `${aiGeneralPrompt}\n\n${context}`;
     try {
       await new Promise(resolve => setTimeout(resolve, 1000)); 
       const aiResponseText = `[AI Sim Recording Advice for ${formData.genre || 'main genre'}]: Condenser mic for vocals. Double-track guitars.`;
       setAiLastResult(aiResponseText); addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Conseil enregistrement!"});
     } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur conseil", description: e.message, variant: "destructive"});
     } finally { setAiLoading(false); }
  };

  const handleAiInstrumentationSuggestion = async () => {
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    const formData = form.getValues();
    const context = `Suggest instruments for:
- Genre: ${formData.genre || 'N/A'}
- Subgenre: ${formData.subgenre || 'N/A'}
- Mood: ${formData.mood || 'N/A'}
- Theme: ${formData.theme || 'N/A'}
- Existing Instrumentation: ${formData.instrumentation?.join(', ') || 'N/A'}`;
    const promptForAI = `${aiGeneralPrompt}\n\n${context}`;
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Instrumentation for ${formData.genre || 'main genre'}]: Subtle strings or synth pad for ${formData.mood || 'mood'}.`;
      setAiLastResult(aiResponseText); addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Suggestion instruments!"});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur instruments", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  };

  const handleAiCreativeFx = async () => {
     setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
     const formData = form.getValues();
     const context = `Suggest FX for:
- Genre: ${formData.genre || 'N/A'}
- Subgenre: ${formData.subgenre || 'N/A'}
- Mood: ${formData.mood || 'N/A'}`;
     const promptForAI = `${aiGeneralPrompt}\n\n${context}`;
     try {
       await new Promise(resolve => setTimeout(resolve, 1000)); 
       const aiResponseText = `[AI Sim FX for ${formData.genre || 'main genre'}]: Subtle delay on main vocal, phaser on guitar.`;
       setAiLastResult(aiResponseText); addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Idée FX!"});
     } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur FX", description: e.message, variant: "destructive"});
     } finally { setAiLoading(false); }
  };

  const handleAiArrangementAdvice = async () => {
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    const formData = form.getValues(); 
    const currentLyricsText = stripHtml(lyricsContent);
    const context = `Arrangement advice for:
- Genre: ${formData.genre || 'N/A'}
- Subgenre: ${formData.subgenre || 'N/A'}
- Mood: ${formData.mood || 'N/A'}
- Theme: ${formData.theme || 'N/A'}
- Lyrics: ${currentLyricsText.substring(0, 150)}...`;
    const promptForAI = `${aiGeneralPrompt}\n\n${context}`;
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Arrangement Advice]: Start stripped-down, build to chorus.`;
      setAiLastResult(aiResponseText); addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Conseil arrangement!"});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur arrangement", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  };

  const handleAiAnalyzeTone = async () => {
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    const formData = form.getValues(); // Get current form values
    const currentLyricsText = stripHtml(lyricsContent);
    if (!currentLyricsText.trim()) {
      toast({ title: "Analyse de Ton", description: "Aucune parole à analyser.", variant: "destructive" });
      setAiLoading(false); return;
    }
    const promptForAI = `${aiGeneralPrompt}\n\nAnalyze tone/mood of lyrics:\n\n${currentLyricsText}\n\nConsider the song's mood: ${formData.mood || 'N/A'}`;
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Tone Analysis]: Tone is ${formData.mood || 'neutral'} with reflection.`;
      setAiLastResult(aiResponseText); addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Analyse de ton!"});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur analyse ton", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  };

  const handleAiCorrect = async () => {
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    let textToProcess = ""; let promptForAI = "";
    const selection = currentSelectionRange; const editor = quillRef.current?.getEditor();
    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\nCorrect grammar/spelling:\n\n${textToProcess}`;
    } else {
      textToProcess = stripHtml(lyricsContent); 
      promptForAI = `${aiGeneralPrompt}\n\nCorrect grammar/spelling of all text:\n\n${textToProcess}`;
    }
    if (!textToProcess.trim()) { toast({ title: "Correction IA", description: "Aucun texte.", variant: "destructive" }); setAiLoading(false); return; }
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Corrected Sim: ${textToProcess.substring(0, 30)}...]`; 
      setAiLastResult(`Correction simulée pour: ${textToProcess.substring(0, 50)}...`);
      if (selection && selection.length > 0 && editor) {
        editor.deleteText(selection.index, selection.length, 'user');
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        handleLyricsChange(editor.root.innerHTML); 
      } else { handleLyricsChange(`<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`); }
      addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Texte corrigé!"});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur correction", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  };

  const handleAiTranslate = async (lang: string) => {
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    let textToProcess = ""; let promptForAI = "";
    const selection = currentSelectionRange; const editor = quillRef.current?.getEditor();
    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\nTranslate to ${lang}:\n\n${textToProcess}`;
    } else {
      textToProcess = stripHtml(lyricsContent); 
      promptForAI = `${aiGeneralPrompt}\n\nTranslate all text to ${lang}:\n\n${textToProcess}`;
    }
     if (!textToProcess.trim()) { toast({ title: "Traduction IA", description: "Aucun texte.", variant: "destructive" }); setAiLoading(false); return; }
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Translated Sim to ${lang}: ${textToProcess.substring(0, 30)}...]`; 
      setAiLastResult(`Traduction simulée vers ${lang} pour: ${textToProcess.substring(0, 50)}...`);
      if (selection && selection.length > 0 && editor) {
        editor.deleteText(selection.index, selection.length, 'user');
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        handleLyricsChange(editor.root.innerHTML); 
      } else { handleLyricsChange(`<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`); }
      addAiHistory(promptForAI, aiResponseText); toast({ title: `IA: Texte traduit en ${lang}!`});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur traduction", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  };

  const handleAiEditGeneralPrompt = useCallback((newPrompt: string) => {
    setAiGeneralPrompt(newPrompt); toast({ title: "Prompt général mis à jour", });
  }, []);

  const handleAiConfigChange = (newPartialConfig: Partial<AiConfig>) => {
    setAiConfig(prevConfig => ({ ...prevConfig, ...newPartialConfig }));
  };

  const handleFileUpload = async (file: File, type: 'audio' | 'image') => {
    if (!file) return;
    const fileExt = file.name.split('.').pop();
    const fileName = `${Math.random()}.${fileExt}`;
    const bucketName = type === 'audio' ? 'audio' : 'covers'; 
    const filePath = `${fileName}`;
    if (type === 'audio') setUploadingAudio(true); if (type === 'image') setUploadingImage(true);
    try {
      const { error: uploadError } = await supabaseClient.storage.from(bucketName).upload(filePath, file);
      if (uploadError) throw uploadError;
      const { data: publicURLData } = supabaseClient.storage.from(bucketName).getPublicUrl(filePath);
      if (!publicURLData || !publicURLData.publicUrl) throw new Error('Could not get public URL');
      if (type === 'audio') {
        form.setValue('audio_url', publicURLData.publicUrl, { shouldValidate: true, shouldDirty: true });
        setUploadedAudioUrl(publicURLData.publicUrl); 
        toast({ title: "Fichier audio téléversé" });
      } else if (type === 'image') {
        form.setValue('cover_art_url', publicURLData.publicUrl, { shouldValidate: true, shouldDirty: true }); 
        setUploadedCoverUrl(publicURLData.publicUrl); toast({ title: "Image de couverture téléversée" });
      }
    } catch (error: any) { toast({ title: `Erreur upload ${type}`, description: error.message, variant: "destructive" });
    } finally { if (type === 'audio') setUploadingAudio(false); if (type === 'image') setUploadingImage(false); }
  };

  const onSubmitWithStatus = (publish?: boolean) => {
    form.trigger().then(isValid => {
      if (!isValid) {
        toast({ title: "Erreurs de validation", description: "Veuillez corriger les erreurs indiquées.", variant: "destructive" });
        // Log specific errors to console for easier debugging
        console.error("Validation Errors:", form.formState.errors);
        // Extract and show specific error messages
        const errorMessages = Object.values(form.formState.errors)
          .map(err => err.message)
          .filter(Boolean)
          .join("\n");
        if (errorMessages) {
          toast({ title: "Détails des erreurs:", description: errorMessages, variant: "destructive", duration: 7000 });
        }
        return;
      }

      let dataToSubmit = { ...form.getValues() };

      if (typeof publish === 'boolean') {
        dataToSubmit.is_public = publish;
        dataToSubmit.visibility = publish ? 'public' : (dataToSubmit.visibility === 'public' ? 'private' : dataToSubmit.visibility); // if unpublishing, set to private, else keep current non-public visibility
      }
      // If just saving (publish is undefined), is_public and visibility from form are used.

      console.log(`onSubmitWithStatus called. Mode: ${mode}, Publish action: ${publish}, Data before JSON parse:`, dataToSubmit);

      try {
        if (dataToSubmit.credits && typeof dataToSubmit.credits === 'string') {
          if (dataToSubmit.credits.trim() === '') dataToSubmit.credits = null; // Store as null if empty string
          else dataToSubmit.credits = JSON.parse(dataToSubmit.credits);
        }
        if (dataToSubmit.editor_data && typeof dataToSubmit.editor_data === 'string') {
          if (dataToSubmit.editor_data.trim() === '') dataToSubmit.editor_data = null; // Store as null if empty string
          else dataToSubmit.editor_data = JSON.parse(dataToSubmit.editor_data);
        }
      } catch (e: any) {
        toast({ title: "Erreur de format JSON", description: `Veuillez vérifier les champs 'Crédits' et 'Données Éditeur'. ${e.message}`, variant: "destructive" });
        return; 
      }
      
      console.log("[SongForm] Data after JSON parse, before submitting to prop:", dataToSubmit);

      // The onFormSubmit prop should ideally handle the final SongFormValues type.
      // The status parameter for onFormSubmit is removed as is_public/visibility are now part of the data.
      onFormSubmit(dataToSubmit as SongFormValues).catch(error => {
        console.error("[SongForm] Error from onFormSubmit prop:", error);
        toast({ title: "Erreur Soumission", description: `Une erreur est survenue: ${error.message}`, variant: "destructive" });
      });
    });
  };
  
  const handleAiGenerate = async (customPrompt?: string) => { 
    setAiLoading(true); setAiError(undefined); setAiLastResult(undefined);
    let textToProcess = ""; let promptForAI = "";
    const selection = currentSelectionRange; const editor = quillRef.current?.getEditor();
    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\n${customPrompt || 'Improve or continue this selected text'}:\n\n${textToProcess}`;
    } else {
      textToProcess = stripHtml(lyricsContent); 
      promptForAI = `${aiGeneralPrompt}\n\n${customPrompt || 'Continue writing lyrics based on this text'}:\n\n${textToProcess}`;
    }
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = ` [AI Sim Response for: ${textToProcess.substring(0, 30)}...]`; 
      setAiLastResult(`Texte généré (simulation) pour: ${textToProcess.substring(0, 50)}...`);
      if (selection && selection.length > 0 && editor) {
        editor.deleteText(selection.index, selection.length, 'user');
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        handleLyricsChange(editor.root.innerHTML); 
      } else { handleLyricsChange(`<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`); }
      addAiHistory(promptForAI, aiResponseText); toast({ title: "IA: Texte généré!"});
    } catch (e: any) { setAiError(e.message); toast({ title: "IA: Erreur de génération", description: e.message, variant: "destructive"});
    } finally { setAiLoading(false); }
  }

   useEffect(() => {
     if (mode === 'edit' && initialValues) {
       const currentInitialValues = initialValues as Partial<SongFormValues & { id?: string; user_id?: string; tempo?: string; credits?: any }>; 
       
       let creditsData: any = {};
       if (typeof currentInitialValues.credits === 'string' && currentInitialValues.credits) {
         try { creditsData = JSON.parse(currentInitialValues.credits); } catch { console.error("Failed to parse credits JSON string from initialValues"); creditsData = {}; }
       } else if (typeof currentInitialValues.credits === 'object' && currentInitialValues.credits !== null) {
         creditsData = currentInitialValues.credits;
       }

       const valuesToReset: Partial<SongFormValues> = {
         title: currentInitialValues.title || "",
         artist: currentInitialValues.artist || "",
         featured_artists: creditsData.featured_artists || currentInitialValues.featured_artists || "",
         genre: currentInitialValues.genre || "",
         subgenre: currentInitialValues.subgenre || "",
         mood: currentInitialValues.mood || "",
         theme: currentInitialValues.theme || "",
         instrumentation: Array.isArray(currentInitialValues.instrumentation) ? currentInitialValues.instrumentation : [],
         key: currentInitialValues.key || "",
         bpm: currentInitialValues.bpm === undefined ? null : currentInitialValues.bpm,
         duration_ms: currentInitialValues.duration_ms === undefined ? null : currentInitialValues.duration_ms,
         description: currentInitialValues.description || "",
         tags: Array.isArray(currentInitialValues.tags) ? currentInitialValues.tags : [],
         audio_url: currentInitialValues.audio_url || null,
         cover_art_url: currentInitialValues.cover_art_url || null,
         album_id: currentInitialValues.album_id || null,
         composer_name: creditsData.composer_name || currentInitialValues.composer_name || "",
         writers: creditsData.writers || currentInitialValues.writers || "",
         producers: creditsData.producers || currentInitialValues.producers || "",
         record_label: creditsData.record_label || currentInitialValues.record_label || "",
         isrc: creditsData.isrc || currentInitialValues.isrc || "",
         upc: creditsData.upc || currentInitialValues.upc || "",
         release_date: currentInitialValues.release_date ? new Date(currentInitialValues.release_date) : null,
         lyrics: currentInitialValues.lyrics || "",
         credits: currentInitialValues.credits // Keep original logic for the main credits textarea
           ? (typeof currentInitialValues.credits === 'string' ? currentInitialValues.credits : JSON.stringify(currentInitialValues.credits, null, 2)) 
           : '', 
         editor_data: currentInitialValues.editor_data 
           ? (typeof currentInitialValues.editor_data === 'string' ? currentInitialValues.editor_data : JSON.stringify(currentInitialValues.editor_data, null, 2)) 
           : '', 
         is_public: !!currentInitialValues.is_public,
         visibility: currentInitialValues.visibility || 'private',
         is_archived: !!currentInitialValues.is_archived,
         is_explicit: !!currentInitialValues.is_explicit,
       };
       form.reset(valuesToReset);
     } else if (mode === 'create') {
        form.reset({
          title: "", artist: "", featured_artists: "", genre: "", subgenre: "", mood: "", theme: "", instrumentation: [], key: "",
          bpm: null, duration_ms: null, description: "", tags: [], audio_url: null, cover_art_url: null, album_id: null,
          composer_name: "", writers: "", producers: "", record_label: "", isrc: "", upc: "",
          release_date: null, lyrics: "", credits: '', editor_data: '',
          is_public: false, visibility: 'private', is_archived: false, is_explicit: false,
        });
     }
   }, [mode, initialValues, form]); 

  useEffect(() => {
    if (mode === 'edit' && initialValues) {
      const initialAudioUrl = initialValues.audio_url;
      setUploadedAudioUrl(initialAudioUrl && initialAudioUrl.startsWith('http') ? initialAudioUrl : null);
      setUploadedCoverUrl(initialValues.cover_art_url || null); // Use cover_art_url
      setLyricsContent(initialValues.lyrics || "");
    } else if (mode === 'create') {
      setUploadedAudioUrl(null);
      setUploadedCoverUrl(null); // Use cover_art_url
      setLyricsContent("");
    }
  }, [initialValues, mode]);

  useEffect(() => { /* AI Config loading logic (commented out) */ }, []);

  return (
    <RHFFormProvider {...form}>
      <form className="h-full flex flex-col" onSubmit={form.handleSubmit(data => onFormSubmit(data))}>
        <div className="w-full bg-background/90 py-6 px-0 md:px-4 flex flex-col md:flex-row md:items-center gap-8 border-b mb-8 max-w-none">
          <div className="flex flex-col items-center md:items-start gap-4 w-full md:w-1/4">
            <div className="w-full flex flex-col items-center">
              <FormField control={form.control} name="cover_art_url" render={({ field }) => ( <FormItem><FormLabel className="text-sm">Image de couverture</FormLabel><FormControl><div className="flex flex-col items-center gap-2"><Input type="file" accept="image/*" ref={imageFileInputRef} onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'image')} className="hidden" id="image-upload"/><Button type="button" variant="outline" size="sm" onClick={() => imageFileInputRef.current?.click()} disabled={uploadingImage || isSubmitting}>{uploadingImage ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}{uploadedCoverUrl ? 'Changer' : 'Choisir une image'}</Button>{uploadedCoverUrl && uploadedCoverUrl.startsWith('http') && ( <div className="w-[180px] h-[180px] mt-2 rounded-md overflow-hidden relative border"><Image src={uploadedCoverUrl} alt="Preview" fill className="object-cover" /></div>)}</div></FormControl></FormItem>)}/>
            </div>
          </div>
          <div className="flex-1 flex flex-col items-center md:items-start gap-4 w-full">
            <FormField control={form.control} name="title" render={({ field }) => ( <FormItem className="w-full"><FormLabel className="sr-only">Titre du morceau</FormLabel><FormControl><Input className="text-3xl md:text-4xl font-bold w-full bg-transparent border-none shadow-none px-0 mb-2" placeholder="Titre du morceau *" {...field} /></FormControl><FormMessage /></FormItem> )}/>
            {(form.watch('artist') ?? '').trim() !== '' && ( <div className="flex items-center mt-1 mb-2"><span className="px-3 py-1 rounded-full bg-secondary text-base font-medium text-foreground/80 shadow-sm">{form.watch('artist')}</span></div>)}
            <FormField 
              control={form.control} 
              name="audio_url" 
              render={({ field }) => ( 
                <FormItem className="w-full">
                  <FormLabel className="text-sm">Fichier audio</FormLabel>
                  <FormControl>
                    <div className="flex flex-col gap-2">
                      <Input 
                        type="file" 
                        accept="audio/*" 
                        ref={audioFileInputRef} 
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'audio')} 
                        className="hidden" 
                        id="audio-upload"
                      />
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm" 
                        onClick={() => audioFileInputRef.current?.click()} 
                        disabled={uploadingAudio || isSubmitting}
                      >
                        {uploadingAudio ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                        {uploadedAudioUrl ? 'Changer' : 'Choisir un fichier audio'}
                      </Button>
                      {/* Ensure uploadedAudioUrl is a non-empty string starting with http before rendering */}
                      {uploadedAudioUrl && typeof uploadedAudioUrl === 'string' && uploadedAudioUrl.startsWith('http') ? ( 
                        <AudioWaveformPreview 
                          audioUrl={uploadedAudioUrl} 
                          song={initialValues && 'id' in initialValues && 'user_id' in initialValues ? (initialValues as unknown as Song) : undefined} 
                          onReady={handleWaveformReady}
                          // allowDownload={form.watch('allow_downloads')} // Pass allow_downloads state // REMOVED
                        /> 
                      ) : null}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem> 
              )}
            />
            <div className="flex gap-2 mt-4">
              <Button variant="outline" onClick={() => router.back()} disabled={isSubmitting || uploadingAudio || uploadingImage}>Annuler</Button>
              {mode === 'create' ? (
                <>
                  <Button type="button" onClick={() => onSubmitWithStatus(false)} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="secondary">{(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}Enregistrer en Brouillon</Button>
                  <Button type="button" onClick={() => onSubmitWithStatus(true)} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg">{(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}Publier</Button>
                </>
              ) : (
                <Button type="button" onClick={() => onSubmitWithStatus()} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg">{(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}Sauvegarder</Button>
              )}
            </div>
          </div>
        </div>

        {/* Main content area with Song Form and Song Vault */}
        <div className="flex flex-row flex-grow overflow-hidden relative">
          {/* Left Column: Existing Form Tabs */}
          <ScrollArea className="flex-1 p-4 overflow-y-auto">
            <div className="w-full space-y-6"> 
              <Tabs defaultValue="general" className="w-full">
                <TabsList className="grid w-full grid-cols-4 mb-4"> {/* Changed grid-cols-3 to grid-cols-4 */}
                <TabsTrigger value="general">Général</TabsTrigger>
                <TabsTrigger value="lyrics-ia">Paroles / IA</TabsTrigger>
                <TabsTrigger value="progress-stats">Progression & Stats</TabsTrigger> {/* New Tab Trigger */}
                <TabsTrigger value="publication">Publication</TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="w-full">
                <div className="space-y-4 w-full">
                    <FormField control={form.control} name="title" render={({ field }) => ( <FormItem><FormLabel>Titre du morceau *</FormLabel><FormControl><Input placeholder="Ex: Mon Super Hit" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="artist" render={({ field }) => ( <FormItem><FormLabel>Artiste *</FormLabel><FormControl><Input placeholder="Ex: Nom de l'artiste" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="featured_artists" render={({ field }) => ( <FormItem><FormLabel>Artistes en featuring (Optionnel)</FormLabel><FormControl><Input placeholder="Artiste 1, Artiste 2, ..." {...field} value={field.value || ''} /></FormControl><FormDescription>Séparez les noms par des virgules.</FormDescription><FormMessage /></FormItem> )}/>
                    
                    {/* Replaced MetadataFields for genres, moods with simple Inputs */}
                    <FormField control={form.control} name="genre" render={({ field }) => ( <FormItem><FormLabel>Genre</FormLabel><FormControl><Input placeholder="Ex: Pop, Rock, Jazz" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="subgenre" render={({ field }) => ( <FormItem><FormLabel>Sous-genre</FormLabel><FormControl><Input placeholder="Ex: Synth-Pop, Indie Rock" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="mood" render={({ field }) => ( <FormItem><FormLabel>Ambiance / Mood</FormLabel><FormControl><Input placeholder="Ex: Joyeux, Mélancolique" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="theme" render={({ field }) => ( <FormItem><FormLabel>Thème</FormLabel><FormControl><Input placeholder="Ex: Amour, Voyage" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>

                    {/* MetadataFields for tags and instrumentation (arrays) */}
                    <MetadataFields
                      control={form.control}
                      configs={[
                        { name: 'tags', label: 'Tags', options: tagOptions, placeholder: 'Ajouter des tags' },
                        { name: 'instrumentation', label: 'Instrumentation', options: instrumentationOptions, placeholder: 'Ajouter des instruments' },
                      ]}
                      gridCols="grid-cols-1 md:grid-cols-2" // Adjusted gridCols
                      gap="gap-6"
                    />
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      <FormField control={form.control} name="key" render={({ field }) => ( <FormItem><FormLabel>Tonalité</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value || ''}><SelectTrigger><SelectValue placeholder="Sélectionner une tonalité" /></SelectTrigger><SelectContent className="bg-background border shadow-md">{musicalKeys.map(k => <SelectItem key={k} value={k}>{k}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="bpm" render={({ field }) => ( <FormItem><FormLabel>BPM</FormLabel><FormControl><Input type="number" placeholder="120" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                      {/* duration_ms is handled by AudioWaveformPreview or can be a manual input if needed */}
                      {/* Removed: time_signature, capo, tuning_frequency, language, lyrics_language */}
                    </div>
                    
                    <FormField control={form.control} name="description" render={({ field }) => ( <FormItem><FormLabel>Description</FormLabel><FormControl><Textarea placeholder="Décrivez votre morceau..." className="min-h-[100px]" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                    
                    <Separator className="my-6" />
                    <h3 className="text-lg font-medium">Crédits & Rôles</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField control={form.control} name="composer_name" render={({ field }) => ( <FormItem><FormLabel>Compositeur</FormLabel><FormControl><Input placeholder="Ex: Jean Dupont" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="writers" render={({ field }) => ( <FormItem><FormLabel>Auteurs/Paroliers</FormLabel><FormControl><Input placeholder="Auteur 1, Auteur 2, ..." {...field} value={field.value || ''} /></FormControl><FormDescription>Séparez les noms par des virgules.</FormDescription><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="producers" render={({ field }) => ( <FormItem><FormLabel>Producteurs</FormLabel><FormControl><Input placeholder="Producteur 1, Producteur 2, ..." {...field} value={field.value || ''} /></FormControl><FormDescription>Séparez les noms par des virgules.</FormDescription><FormMessage /></FormItem> )}/>
                    </div>
                    
                    <Separator className="my-6" />
                    <h3 className="text-lg font-medium">Crédits (JSON Avancé)</h3>
                     <FormField control={form.control} name="credits" render={({ field }) => ( <FormItem><FormLabel>Crédits (JSON)</FormLabel><FormControl><Textarea placeholder='Ex: { "engineer": "Jane Doe", "mix_master_engineer": ["MixMaster Mike"] }' className="min-h-[100px]" {...field} onChange={e => field.onChange(e.target.value)} value={typeof field.value === 'string' ? field.value : (field.value ? JSON.stringify(field.value, null, 2) : '')} /></FormControl><FormDescription>Entrez ici les crédits additionnels ou non couverts par les champs dédiés, au format JSON.</FormDescription><FormMessage /></FormItem> )}/>

                    <Separator className="my-6" />
                    <h3 className="text-lg font-medium">Données Éditeur (JSON)</h3>
                    <FormField control={form.control} name="editor_data" render={({ field }) => ( <FormItem><FormLabel>Données Éditeur</FormLabel><FormControl><Textarea placeholder='Ex: { "tempo_locked": true, "arrangements": [] }' className="min-h-[100px]" {...field} onChange={e => field.onChange(e.target.value)} value={typeof field.value === 'string' ? field.value : (field.value ? JSON.stringify(field.value, null, 2) : '')} /></FormControl><FormDescription>Entrez les données de l'éditeur au format JSON.</FormDescription><FormMessage /></FormItem> )}/>
                    {/* Removed bloc_note and right_column_notepad */}
                </div>
              </TabsContent>

              <TabsContent value="lyrics-ia" className="w-full p-0">
                <div className={`grid grid-cols-1 ${!isAiPanelCollapsed ? 'lg:grid-cols-3 gap-6' : ''}`}>
                  <div className={!isAiPanelCollapsed ? 'lg:col-span-2' : 'lg:col-span-3'}>
                    <Card className="w-full">
                      <CardHeader>
                        <CardTitle>Paroles</CardTitle>
                        <CardDescription>Écrivez ou générez les paroles de votre morceau.</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <FormField control={form.control} name="lyrics" render={({ field }) => ( <FormItem><FormControl><RichLyricsEditor value={lyricsContent} onChange={handleLyricsChange} placeholder="Commencez à écrire les paroles ici..." className="min-h-[400px]" quillRef={quillRef} onSelectionChange={handleEditorSelectionChange} /></FormControl><FormMessage /></FormItem> )}/>
                         <Collapsible open={showAiHistory} onOpenChange={setShowAiHistory}>
                          <CollapsibleTrigger asChild>
                            <Button variant="ghost" size="sm" className="text-xs w-full justify-start text-muted-foreground hover:text-foreground">
                              <History className="mr-2 h-4 w-4" />
                              Historique IA ({Math.floor(aiHistory.length / 2)} interactions)
                              {showAiHistory ? <ChevronUp className="ml-auto h-4 w-4" /> : <ChevronDown className="ml-auto h-4 w-4" />}
                            </Button>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="mt-2">
                            <ScrollArea className="h-[200px] border rounded-md p-2 bg-muted/20">
                              {aiHistory.length === 0 ? ( <p className="text-xs text-muted-foreground text-center py-4">Aucun historique.</p> ) : (
                                <div className="space-y-3">
                                  {aiHistory.map((item, index) => (
                                    <div key={index} className={`text-xs p-2 rounded ${item.role === 'user' ? 'bg-blue-50 dark:bg-blue-900/30' : 'bg-gray-50 dark:bg-gray-800/30'}`}>
                                      <p className="font-semibold mb-1 capitalize">{item.role}:</p>
                                      <p className="whitespace-pre-wrap break-words">{item.content}</p>
                                      {item.timestamp && <p className="text-muted-foreground text-[10px] mt-1">{item.timestamp.toLocaleTimeString()}</p>}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </ScrollArea>
                          </CollapsibleContent>
                        </Collapsible>
                      </CardContent>
                    </Card>
                  </div>

                  {!isAiPanelCollapsed && (
                    <div className="lg:col-span-1 space-y-6">
                       <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                          <div className="space-y-1">
                            <CardTitle className="text-lg"><Music2 className="mr-2 h-5 w-5 inline-block" />Assistance IA</CardTitle>
                            <CardDescription className="text-xs">Suggestions, corrections, formatage.</CardDescription>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button type="button" variant="ghost" size="icon" className="h-7 w-7" onClick={() => setShowAiConfigMenu(true)} title="Configurer l'IA"><Cog className="h-4 w-4" /></Button>
                            <Button type="button" variant="ghost" size="icon" className="h-7 w-7" onClick={() => setIsAiPanelCollapsed(true)} title="Réduire le panneau IA"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><line x1="9" y1="3" x2="9" y2="21"/></svg></Button>
                          </div>
                        </CardHeader>
                        <div className="p-0"> 
                          <AiQuickActions 
                            loading={aiLoading} 
                            lastResult={aiLastResult}
                            error={aiError}
                            iaHistory={aiHistory}
                            aiConfig={aiConfig}
                            setAiConfig={handleAiConfigChange}
                            generalPrompt={aiGeneralPrompt} 
                            onEditGeneralPrompt={handleAiEditGeneralPrompt}
                            onGeneralSuggestions={handleAiGeneralSuggestions} 
                            onMelodySuggestion={handleAiMelodySuggestion} 
                            onRecordingAdvice={handleAiRecordingAdvice} 
                            onInstrumentationSuggestion={handleAiInstrumentationSuggestion} 
                            onCreativeFx={handleAiCreativeFx} 
                            onArrangementAdvice={handleAiArrangementAdvice} 
                          />
                          <AiConfigMenu isPopoverOpen={showAiConfigMenu} setIsPopoverOpen={setShowAiConfigMenu}/>
                        </div> 
                      </Card>
                    </div>
                  )}
                </div> 
                 {isAiPanelCollapsed && (
                    <div className="absolute top-0 right-0 mt-2 mr-2 z-20"> 
                       <Button variant="outline" size="icon" onClick={() => setIsAiPanelCollapsed(false)} className="rounded-full shadow-lg bg-background hover:bg-muted h-8 w-8" title="Ouvrir l'assistant IA">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><line x1="15" y1="3" x2="15" y2="21"/></svg>
                        <span className="sr-only">Ouvrir Assistant IA</span>
                      </Button>
                    </div>
                  )}
              </TabsContent>

              <TabsContent value="publication">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader><CardTitle>Distribution & Statut</CardTitle><CardDescription>Informations relatives à la distribution et visibilité.</CardDescription></CardHeader>
                    <CardContent className="space-y-4">
                      <FormField control={form.control} name="album_id" render={({ field }) => ( <FormItem><FormLabel>Album</FormLabel><Select onValueChange={field.onChange} value={field.value || NO_ALBUM_SELECTED_VALUE}><FormControl><SelectTrigger disabled={isLoadingAlbums}><SelectValue placeholder={isLoadingAlbums ? "Chargement..." : "Sélectionner un album"} /></SelectTrigger></FormControl><SelectContent className="bg-background border shadow-md"><SelectItem value={NO_ALBUM_SELECTED_VALUE}>Aucun album</SelectItem>{albums.map((album) => ( <SelectItem key={album.id} value={album.id}>{album.title}</SelectItem> ))}</SelectContent></Select><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="release_date" render={({ field }) => ( <FormItem className="flex flex-col"><FormLabel>Date de sortie</FormLabel><DatePicker date={field.value ?? undefined} onSelect={(date: Date | undefined) => field.onChange(date)} /><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="record_label" render={({ field }) => ( <FormItem><FormLabel>Label</FormLabel><FormControl><Input placeholder="Mon Label Indépendant" {...field} value={field.value ?? ''}/></FormControl><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="isrc" render={({ field }) => ( <FormItem><FormLabel>ISRC</FormLabel><FormControl><Input placeholder="Ex: US-S1Z-99-00001" {...field} value={field.value ?? ''}/></FormControl><FormDescription>Code ISRC.</FormDescription><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="upc" render={({ field }) => ( <FormItem><FormLabel>UPC/EAN</FormLabel><FormControl><Input placeholder="Ex: 123456789012" {...field} value={field.value ?? ''}/></FormControl><FormDescription>Code-barres.</FormDescription><FormMessage /></FormItem> )}/>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader><CardTitle>Options de Publication</CardTitle><CardDescription>Gérez la visibilité et les options de votre morceau.</CardDescription></CardHeader>
                    <CardContent className="space-y-4">
                      <FormField control={form.control} name="is_public" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Public</FormLabel> <FormDescription>Rendre ce morceau visible publiquement.</FormDescription> </div> <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl> </FormItem> )}/>
                      <FormField control={form.control} name="visibility" render={({ field }) => ( <FormItem><FormLabel>Visibilité</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value}><FormControl><SelectTrigger><SelectValue placeholder="Choisir la visibilité" /></SelectTrigger></FormControl><SelectContent><SelectItem value="private">Privé</SelectItem><SelectItem value="unlisted">Non listé</SelectItem><SelectItem value="public">Public</SelectItem></SelectContent></Select><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="is_archived" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Archivé</FormLabel> <FormDescription>Archiver ce morceau (caché, non supprimé).</FormDescription> </div> <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl> </FormItem> )}/>
                      <FormField control={form.control} name="is_explicit" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Contenu Explicite</FormLabel> <FormDescription>Paroles ou thèmes explicites.</FormDescription> </div> <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} className="data-[state=checked]:bg-orange-500 data-[state=unchecked]:bg-slate-600 focus-visible:ring-orange-500" /></FormControl> </FormItem> )}/>
                      {/* Removed: is_ai_generated, stems_available, allow_downloads, allow_comments */}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

            <TabsContent value="progress-stats" className="w-full">
                <Card>
                  <CardHeader>
                    <CardTitle>Suivi de Progression du Morceau</CardTitle>
                    <CardDescription>Suivez l'avancement des différentes étapes de votre création.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <FormField
                      control={form.control}
                      // name="progress_data" // REMOVED
                      // render={({ field }) => (
                      //   <SongProgressTracker // REMOVED
                      //     value={field.value || {}}
                      //     onChange={(newProgressData) => {
                      //       field.onChange(newProgressData);
                      //     }}
                      //   />
                      // )}
                    />
                    <p className="text-sm text-muted-foreground">Le suivi de progression a été retiré de ce formulaire.</p>
                    {/* Placeholder for SongAnalyticsDisplay component */}
                    <div className="mt-8">
                       <h4 className="text-md font-semibold mb-2">Analyses Visuelles (Genre)</h4>
                       {/* <SongAnalyticsDisplay songData={form.getValues()} /> */}
                       <p className="text-sm text-muted-foreground">Graphique du genre à venir ici.</p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              </Tabs>
            </div>
          </ScrollArea>

          {/* Right Column: Song Vault (Collapsible) */}
          {!isVaultPanelCollapsed && (
            <div className="w-1/3 min-w-[320px] max-w-[480px] border-l bg-card flex flex-col overflow-hidden">
              {/* Header for the Vault Panel */}
              <div className="flex items-center justify-between p-2 border-b sticky top-0 bg-card z-10">
                <h3 className="font-semibold text-sm ml-1">Coffre à Chansons</h3>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsVaultPanelCollapsed(true)}
                  className="h-7 w-7"
                  title="Réduire le Coffre"
                >
                  <PanelRightClose className="h-4 w-4" />
                </Button>
              </div>
              {/* Scrollable content area for SongVault component */}
              <ScrollArea className="flex-grow">
                <div className="p-3"> {/* Adjusted padding */}
                  <SongVault
                    songId={initialValues?.id}
                    userId={initialValues?.user_id}
                    supabaseClient={supabaseClient}
                    onReady={(actions) => {
                      songVaultActionsRef.current = actions;
                    }}
                  />
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Floating button to open Vault Panel if collapsed */}
          {isVaultPanelCollapsed && (
            <div className="absolute top-0 right-0 mt-2 mr-2 z-20">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setIsVaultPanelCollapsed(false)}
                className="rounded-full shadow-lg bg-background hover:bg-muted h-8 w-8"
                title="Ouvrir le Coffre à Chansons (Vault)"
              >
                <Layers className="h-4 w-4" />
                <span className="sr-only">Ouvrir le Coffre à Chansons</span>
              </Button>
            </div>
          )}
           {/* Button to collapse Vault Panel - to be placed inside the vault header later */}
        </div>
      </form>
    </RHFFormProvider>
  );
});

// Add display name for the forwarded ref component
SongForm.displayName = 'SongForm';

export default SongForm;
