"use client"
import { useState } from "react"
import type React from "react"

import { useRouter } from "next/navigation"
import { getSupabaseClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { ImageUploader } from "@/components/ui/image-uploader"

export default function CreateBandPage() {
  const router = useRouter()
  const { toast } = useToast()
  const supabase = getSupabaseClient()

  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    genre: "",
    location: "",
    avatar_url: "",
    cover_url: "",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleImageUpload = (type: "avatar" | "cover", url: string) => {
    setFormData((prev) => ({
      ...prev,
      [type === "avatar" ? "avatar_url" : "cover_url"]: url,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const { data: userData, error: userError } = await supabase.auth.getUser()

      if (userError) throw userError

      // Create the band
      const { data: bandData, error: bandError } = await supabase
        .from("bands")
        .insert({
          name: formData.name,
          description: formData.description,
          genre: formData.genre,
          location: formData.location,
          avatar_url: formData.avatar_url,
          cover_url: formData.cover_url,
        })
        .select()
        .single()

      if (bandError) throw bandError

      // Add the creator as the owner
      const { error: memberError } = await supabase.from("band_members").insert({
        band_id: bandData.id,
        user_id: userData.user.id,
        role: "owner",
        permissions: ["manage_members", "manage_projects", "edit_band", "delete_band"],
      })

      if (memberError) throw memberError

      toast({
        title: "Groupe créé avec succès",
        description: `Le groupe "${formData.name}" a été créé.`,
      })

      router.push(`/bands/${bandData.id}`)
    } catch (error) {
      console.error("Error creating band:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création du groupe.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container max-w-3xl py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Créer un nouveau groupe</h1>
        <p className="text-muted-foreground">Créez un groupe pour collaborer avec d'autres artistes</p>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informations du groupe</CardTitle>
            <CardDescription>Entrez les informations de base de votre groupe</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Nom du groupe</Label>
              <Input
                id="name"
                name="name"
                placeholder="Entrez le nom du groupe"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Décrivez votre groupe et son style musical"
                value={formData.description}
                onChange={handleChange}
                rows={4}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="genre">Genre principal</Label>
                <Select value={formData.genre} onValueChange={(value) => handleSelectChange("genre", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez un genre" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rock">Rock</SelectItem>
                    <SelectItem value="electronic">Électronique</SelectItem>
                    <SelectItem value="jazz">Jazz</SelectItem>
                    <SelectItem value="hip-hop">Hip-Hop</SelectItem>
                    <SelectItem value="pop">Pop</SelectItem>
                    <SelectItem value="classical">Classique</SelectItem>
                    <SelectItem value="ambient">Ambient</SelectItem>
                    <SelectItem value="folk">Folk</SelectItem>
                    <SelectItem value="metal">Metal</SelectItem>
                    <SelectItem value="indie">Indie</SelectItem>
                    <SelectItem value="other">Autre</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Localisation</Label>
                <Input
                  id="location"
                  name="location"
                  placeholder="ex: Paris, France"
                  value={formData.location}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Avatar du groupe</Label>
              <ImageUploader
                onImageUploaded={(url) => handleImageUpload("avatar", url)}
                existingImageUrl={formData.avatar_url}
                aspectRatio="square"
                maxWidth={300}
                bucketName="band-avatars"
              />
              <p className="text-xs text-muted-foreground">Recommandé: image carrée, minimum 300x300px</p>
            </div>

            <div className="space-y-2">
              <Label>Image de couverture</Label>
              <ImageUploader
                onImageUploaded={(url) => handleImageUpload("cover", url)}
                existingImageUrl={formData.cover_url}
                aspectRatio="landscape"
                maxWidth={1200}
                bucketName="band-covers"
              />
              <p className="text-xs text-muted-foreground">Recommandé: image au format 16:9, minimum 1200x675px</p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Création en cours..." : "Créer le groupe"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
