import Link from "next/link"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { Disc, Plus, MoreHorizontal, Pencil, Eye } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default async function AlbumsPage() {
  const supabase = createSupabaseServerClient()

  const { data: albums } = await supabase.from("albums").select("*").order("created_at", { ascending: false })

  // Récupérer le nombre de morceaux par album
  const albumIds = albums?.map((album) => album.id) || []
  const { data: songCounts } = await supabase
    .from("songs")
    .select("album_id, count(*)")
    .in("album_id", albumIds)
    // .group("album_id") // Supabase/PostgREST does not support .group(), so group by using select and aggregation only

  // Defensive: ensure songCounts is always an array
  const songCountsArr = Array.isArray(songCounts) ? songCounts : []

  // Créer un objet pour faciliter l'accès aux compteurs
  const songCountMap: Record<string, number> = {}
  songCountsArr.forEach((item: any) => {
    if (item && typeof item.album_id === 'string' && typeof item.count === 'string') {
      songCountMap[item.album_id] = Number.parseInt(item.count)
    }
  })

  const formatDate = (date: string) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true, locale: fr })
  }

  return (
    <div className="min-h-screen w-full bg-transparent">
      <div className="frosted p-6 rounded-xl w-full">
        <div className="flex flex-col gap-6 w-full">
          <div className="flex items-center justify-between w-full">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Mes Albums</h1>
              <p className="text-muted-foreground">Gérez vos albums et collections</p>
            </div>
            <Button
              asChild
              size="lg"
              className="bg-primary text-white rounded-xl font-semibold shadow-md hover:bg-primary/90 transition-colors w-full max-w-xs"
            >
              <Link href="/albums/create">
                <Plus className="mr-2 h-5 w-5" />
                Créer un album
              </Link>
            </Button>
          </div>

          <Card className="w-full">
            <CardHeader>
              <div className="flex items-center justify-between w-full">
                <CardTitle>Tous les albums</CardTitle>
                <Tabs defaultValue="all">
                  <TabsList>
                    <TabsTrigger value="all">Tous</TabsTrigger>
                    <TabsTrigger value="published">Publiés</TabsTrigger>
                    <TabsTrigger value="drafts">Brouillons</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 w-full">
                <div className="grid grid-cols-12 text-xs font-medium text-muted-foreground w-full">
                  <div className="col-span-5">Titre</div>
                  <div className="col-span-2">Statut</div>
                  <div className="col-span-2">Morceaux</div>
                  <div className="col-span-2">Date de création</div>
                  <div className="col-span-1"></div>
                </div>
                {albums?.map((album) => (
                  <div
                    key={album.id}
                    className="grid grid-cols-12 items-center py-2 hover:bg-accent/50 rounded-md transition-colors w-full"
                  >
                    <div className="col-span-5 flex items-center gap-3">
                      <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                        {album.cover_url ? (
                          <img
                            src={album.cover_url || "/placeholder.svg"}
                            alt={album.title}
                            className="h-10 w-10 rounded-md object-cover"
                          />
                        ) : (
                          <Disc className="h-5 w-5 text-primary" />
                        )}
                      </div>
                      <div>
                        <div className="font-medium">{album.title}</div>
                        <div className="text-xs text-muted-foreground">
                          {album.description?.substring(0, 50) || "Aucune description"}
                          {album.description?.length > 50 ? "..." : ""}
                        </div>
                      </div>
                    </div>
                    <div className="col-span-2">
                      <div
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          album.status === "published"
                            ? "bg-green-500/10 text-green-500"
                            : album.status === "draft"
                              ? "bg-yellow-500/10 text-yellow-500"
                              : "bg-red-500/10 text-red-500"
                        }`}
                      >
                        {album.status === "published" ? "Publié" : album.status === "draft" ? "Brouillon" : "Archivé"}
                      </div>
                    </div>
                    <div className="col-span-2">{songCountMap[album.id] || 0}</div>
                    <div className="col-span-2 text-sm text-muted-foreground">{formatDate(album.created_at)}</div>
                    <div className="col-span-1 flex justify-end">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/albums/${album.id}/edit`}>
                              <Pencil className="mr-2 h-4 w-4" />
                              Modifier
                            </Link>
                          </DropdownMenuItem>
                          {album.status === "published" && (
                            <DropdownMenuItem asChild>
                              <Link href={`/albums/${album.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                Voir
                              </Link>
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
                {(!albums || albums.length === 0) && (
                  <div className="py-6 text-center text-muted-foreground w-full">
                    Aucun album trouvé. Créez votre premier album !
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
