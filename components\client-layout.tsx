"use client";

import type React from "react";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AudioProvider } from "@/contexts/audio-context";
import { GlobalAudioPlayer } from "@/components/audio/global-audio-player";
import { AuthPlayerWrapper } from "@/components/auth-player-wrapper";

interface ClientLayoutProps {
  children: React.ReactNode;
}

export function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem
      disableTransitionOnChange
    >
      <AudioProvider>
        <SidebarProvider>{children}</SidebarProvider>
        {/* Global Audio Player - visible uniquement si connecté */}
        <AuthPlayerWrapper />
      </AudioProvider>
      <Toaster />
    </ThemeProvider>
  );
}
