# Fiche technique de référence — Page "Créer une chanson"

## Objectif
Ce document sert de mémo technique pour la page de création de chanson (`/app/(authenticated)/songs/create/page.tsx`). Il liste tous les champs de formulaire, constantes, structures, hooks, composants, et logiques clés pour faciliter l’évolution et la maintenance.

---

## Schéma de validation (zod) - Mis à jour
```ts
export const songFormSchema = z.object({
  title: z.string().min(1, { message: "Le titre est requis." }),
  artist_name: z.string().min(1, { message: "Le nom de l'artiste principal est requis." }), 
  featured_artists: z.array(z.string()).optional(),
  genre: z.array(z.string()).optional(), 
  moods: z.array(z.string()).optional(),
  instrumentation: z.array(z.string()).optional(),
  key: z.string().optional(),
  bpm: z.number().min(0).nullable().optional(),
  time_signature: z.string().optional(),
  capo: z.number().min(0).nullable().optional(),
  tuning_frequency: z.number().min(0).nullable().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(), // Note: Persistence of these tags likely involves the generic 'resource_tags' table.
  audio_url: z.string().optional().nullable(), // Renommé depuis song_file_path
  image_path: z.string().optional().nullable(), 
  album_id: z.string().nullable().optional(),
  composer_name: z.string().optional(),
  release_date: z.date().optional().nullable(),
  record_label: z.string().optional(),
  distributor: z.string().optional(),
  isrc: z.string().optional(),
  upc: z.string().optional(),
  is_explicit: z.boolean().default(false).optional(), 
  lyrics: z.string().optional(),
  bloc_note: z.string().optional(),
  right_column_notepad: z.string().optional(),
  status: z.enum(['draft', 'published']).optional(),
  // Visibility flags
  is_description_public: z.boolean().default(false).optional(),
  is_lyrics_public: z.boolean().default(false).optional(),
  is_chords_public: z.boolean().default(false).optional(),
  is_bpm_public: z.boolean().default(false).optional(),
  is_key_public: z.boolean().default(false).optional(),
  is_album_public: z.boolean().default(false).optional(),
  is_composer_public: z.boolean().default(false).optional(),
  is_release_date_public: z.boolean().default(false).optional(),
  is_record_label_public: z.boolean().default(false).optional(),
  is_distributor_public: z.boolean().default(false).optional(),
  is_isrc_public: z.boolean().default(false).optional(),
  is_upc_public: z.boolean().default(false).optional(),
  are_stems_available_public: z.boolean().default(false).optional(), 
  is_download_allowed_public: z.boolean().default(false).optional(), 
  are_comments_allowed_public: z.boolean().default(false).optional(), 
});
```

## Champs principaux du formulaire - Mis à jour
- **title** : Titre de la chanson (obligatoire)
- **artist_name** : Artiste principal (obligatoire)
- **featured_artists** : Artistes invités (array)
- **genre** : Genres (array)
- **moods** : Ambiances (array)
- **instrumentation** : Instruments (array)
- **key** : Tonalité
- **bpm** : Tempo (nombre)
- **time_signature** : Signature rythmique
- **capo** : Capo (nombre)
- **tuning_frequency** : Fréquence d’accordage (nombre)
- **description** : Description libre
- **tags** : Tags (array). *Note : La sauvegarde de ces tags dans la base de données devrait utiliser le système générique `tags` et `resource_tags`.*
- **audio_url** : Chemin du fichier audio (remplace `song_file_path`)
- **image_path** : Chemin de l’image
- **album_id** : ID album (nullable)
- **composer_name** : Compositeur
- **release_date** : Date de sortie
- **record_label** : Label
- **distributor** : Distributeur
- **isrc** : Code ISRC
- **upc** : Code UPC
- **is_explicit** : Paroles explicites (booléen)
- **lyrics** : Paroles
- **bloc_note** : Bloc note principal (onglet Général)
- **right_column_notepad** : Bloc note colonne droite (onglet Général)
- **status**: Statut ('draft', 'published')
- **is_description_public**: Visibilité publique de la description (booléen)
- **is_lyrics_public**: Visibilité publique des paroles (booléen)
- **is_chords_public**: Visibilité publique des accords (booléen)
- **is_bpm_public**: Visibilité publique du BPM (booléen)
- **is_key_public**: Visibilité publique de la tonalité (booléen)
- **is_album_public**: Visibilité publique du lien vers l'album (booléen)
- **is_composer_public**: Visibilité publique du compositeur (booléen)
- **is_release_date_public**: Visibilité publique de la date de sortie (booléen)
- **is_record_label_public**: Visibilité publique du label (booléen)
- **is_distributor_public**: Visibilité publique du distributeur (booléen)
- **is_isrc_public**: Visibilité publique de l'ISRC (booléen)
- **is_upc_public**: Visibilité publique de l'UPC (booléen)
- **are_stems_available_public**: Visibilité publique de la disponibilité des stems (booléen)
- **is_download_allowed_public**: Visibilité publique de l'autorisation de téléchargement (booléen)
- **are_comments_allowed_public**: Visibilité publique de l'autorisation des commentaires (booléen)

## Constantes et listes utiles
- **musicalKeys** : Liste des tonalités musicales
- **timeSignatures** : Signatures rythmiques
- **NO_ALBUM_SELECTED_VALUE** : Valeur spéciale pour "Aucun album"
- **Constantes de localStorage pour IA** :
  - ai_selected_provider_mouvik
  - ollama_selected_model_mouvik
  - openai_selected_model_mouvik
  - openrouter_selected_model_mouvik
  - anthropic_selected_model_mouvik

## Interfaces
- **Album** :
  ```ts
  interface Album {
    id: string;
    title: string;
  }
  ```
- **SongFormValues** :
  ```ts
  interface SongFormValues extends z.infer<typeof songFormSchema> {}
  ```
- **AiConfig**: Interface pour la configuration de l'IA.
- **AiHistoryItem**: Interface pour un élément de l'historique IA.

## Composants clés utilisés - Mis à jour
- **LyricsEditorWithAI**: Gère l'éditeur de texte riche pour les paroles et les actions IA associées (Générer, Corriger, Traduire, Formater, Rimes, Analyser Ton). Inclut l'affichage de l'historique IA.
- **AiAssistantPanel**: Gère le panneau latéral de l'assistant IA, incluant `AiQuickActions` (pour les suggestions générales) et `AiConfigMenu`.
- **VisibilityToggle**: Composant helper pour afficher le label et le switch de visibilité publique.
- **AudioWaveformPreview** : Preview visuelle de la forme d’onde.
- **AiQuickActions** : Affiche les boutons pour les actions IA générales (Suggestions, Mélodie, Enregistrement, etc.).
- **AiConfigMenu** : Menu pour configurer le fournisseur et modèle IA.
- **DatePicker** : Sélection de date de sortie.
- **Tabs** : Navigation entre les sections (Général, Paroles / IA, Publication & Options).
- **Form, Input, Textarea, Select, Switch, Card, Popover, MultiSelect, etc.** : Composants UI de base (shadcn/ui).

## Hooks et logique
- **useForm / react-hook-form** : Gestion du formulaire
- **zodResolver** : Validation avec Zod
- **useRouter** : Navigation Next.js
- **useState, useEffect, useCallback, useRef** : Gestion d’état et effets
- **Supabase** : Connexion et upload fichiers
- **Toast** : Notifications utilisateur

## Points d’architecture - Mis à jour
- **Modularité** : Le formulaire principal (`SongForm`) délègue la gestion des paroles/IA à `LyricsEditorWithAI` et `AiAssistantPanel`.
- **Séparation des onglets** : Général, Paroles / IA, Publication & Options (l'onglet Média a été supprimé).
- **Organisation Onglet Général** : Utilisation de `Card` et d'une grille à deux colonnes pour structurer les informations (Infos principales, Description/Catégorisation, Détails Musicaux, Instrumentation, Bloc-notes).
- **Gestion des fichiers** : Upload et preview gérés dans l'en-tête du formulaire.
- **Assistance IA** : Actions IA spécifiques aux paroles regroupées dans `LyricsEditorWithAI`, actions générales dans `AiAssistantPanel`.
- **Visibilité Publique** : Ajout de `Switch` pour contrôler la visibilité de nombreux champs via des flags `is_*_public` / `are_*_public`.
- **Blocs Notes** : Déplacés dans l'onglet Général.

## Pour évoluer ou déboguer
- **Formulaire principal / État global**: Voir `SongForm.tsx`.
- **Éditeur de paroles / Actions IA sur paroles**: Voir `LyricsEditorWithAI.tsx`.
- **Panneau Assistant IA / Actions IA générales**: Voir `AiAssistantPanel.tsx` et `AiQuickActions.tsx`.
- **Visibilité des champs**: Voir le helper `VisibilityToggle` et les champs `is_*_public` dans `SongForm.tsx`.
- **Schéma de données / Zod**: Vérifier `songFormSchema` dans `SongForm.tsx` et le schéma SQL correspondant.

---

*Document généré automatiquement pour servir de référence rapide lors du développement ou de la refonte de la page "Créer une chanson".*
