# Architecture de MOUVIK

## Vue d'ensemble

**Note:** The project has switched from using `auth-helpers-nextjs` to `SSR` for authentication.

MOUVIK est une application full-stack construite avec Next.js, React et Supabase. L'architecture est conçue pour être modulaire, évolutive et maintenable.

## Diagramme d'architecture

\`\`\`mermaid
graph TD
    Client[Client Browser] --> NextJS[Next.js App]
    NextJS --> ServerComponents[Server Components]
    NextJS --> ClientComponents[Client Components]
    ServerComponents --> SupabaseServer[Supabase Server Client]
    ClientComponents --> SupabaseClient[Supabase Browser Client]
    SupabaseServer --> SupabaseBackend[Supabase Backend]
    SupabaseClient --> SupabaseBackend
    SupabaseBackend --> PostgreSQL[(PostgreSQL Database)]
    SupabaseBackend --> Auth[Auth Service]
    SupabaseBackend --> Storage[Storage Service]
    NextJS --> AIServices[AI Services]
    AIServices --> OpenAI[OpenAI]
    AIServices --> CustomModels[Custom Music Models]
\`\`\`

## Couches d'application

### 1. Interface utilisateur (UI)
- **Composants React** : Composants d'interface utilisateur réutilisables (ex: `Button`, `Card`, `Input`, `LyricsEditorWithAI`, `AiAssistantPanel`, `VisibilityToggle`).
- **Pages Next.js** : Pages de l'application avec rendu côté serveur (App Router).
- **Layouts** : Structures de mise en page partagées.

### 2. Logique métier
- **Hooks personnalisés** : Logique réutilisable pour les composants
- **Context API** : État global de l'application
- **Actions serveur** : Fonctions exécutées côté serveur

### 3. Accès aux données
- **Client Supabase** : Interaction avec la base de données et les services Supabase
- **Utilitaires de requête** : Fonctions pour simplifier les requêtes courantes

### 4. Services externes
- **Services IA** : Intégration avec des modèles d'IA pour la génération musicale
- **Traitement audio** : Services pour le traitement et l'analyse audio

## Flux de données

1. **Création de contenu** : L'utilisateur crée du contenu (chansons, albums) via l'interface
2. **Stockage** : Les fichiers sont stockés dans Supabase Storage
3. **Métadonnées** : Les métadonnées sont stockées dans la base de données PostgreSQL
4. **Récupération** : Les données sont récupérées via des requêtes Supabase
5. **Rendu** : Les données sont rendues dans l'interface utilisateur

## Sécurité

### Authentification

L'authentification est gérée par **Supabase Auth**, en utilisant le package `@supabase/ssr`. Ce package est spécifiquement conçu pour Next.js et facilite la gestion des sessions et des clients Supabase dans les différents contextes (Server Components, Client Components, API Routes, Actions).

- **Client Côté Serveur** (`/lib/supabase/server.ts`):
  - Utilise `createServerClient` de `@supabase/ssr`.
  - Wrapper `createSupabaseServerClient` pour simplifier l'instanciation dans les Server Components, API Routes et Server Actions.
  - Gère les cookies pour maintenir la session serveur.

- **Client Côté Client** (`/lib/supabase/client.ts`):
  - Utilise `createBrowserClient` de `@supabase/ssr`.
  - Wrapper `createBrowserClient` pour simplifier l'instanciation dans les Client Components.
  - Assure la synchronisation de la session entre le client et le serveur.

- **Flux d'Authentification**:
  - Popup d'authentification (`components/auth/auth-popup.tsx`) pour login/signup via email/password.
  - Gestion des callbacks OAuth (si ajoutés plus tard).
  - Protection des routes via les layouts (`app/(authenticated)/layout.tsx`) qui vérifient la session serveur.

Le passage de l'ancien `@supabase/auth-helpers-nextjs` à `@supabase/ssr` modernise l'approche et s'intègre mieux avec l'App Router de Next.js.

### Base de Données
Voir [DATABASE.md](./DATABASE.md) pour une description détaillée du modèle de données.

## Stratégie de déploiement

- **Environnement de développement** : Vercel Preview Deployments
- **Environnement de production** : Vercel Production Deployment
- **Base de données** : Supabase Project

## Points d'architecture clés (Focus Formulaire Chanson)

- **Modularité du Formulaire (`SongForm`)**: Le composant `SongForm` a été refactorisé pour déléguer la logique et l'affichage spécifiques à des sous-composants :
    - `LyricsEditorWithAI`: Gère l'éditeur de paroles (Rich Text) et les actions IA directement liées au contenu textuel (Génération, Correction, Traduction, Formatage, Rimes, Analyse de Ton).
    - `AiAssistantPanel`: Gère le panneau latéral de l'assistant IA, affichant les actions IA plus générales (Suggestions globales, Mélodie, Enregistrement, Instrumentation, FX, Arrangement) via `AiQuickActions`.
    - `VisibilityToggle`: Composant helper réutilisable pour afficher un label de champ à côté d'un `Switch` contrôlant sa visibilité publique.
- **Organisation par Onglets**: Le formulaire utilise des onglets (`Tabs`) pour séparer les sections : Général, Paroles / IA, Publication & Options. L'onglet Média a été supprimé car les uploads sont gérés dans l'en-tête.
- **Onglet "Général" Structuré**: Cet onglet utilise une grille et des `Card` pour regrouper logiquement les champs (Infos principales, Description/Catégorisation, Détails Musicaux, Instrumentation, Bloc-notes).
- **Visibilité des Champs**: Introduction de flags booléens (`is_*_public`, `are_*_public`) dans la base de données (`songs` table) et le schéma Zod (`songFormSchema`) pour permettre un contrôle fin de la visibilité publique de chaque champ pertinent. Des `Switch` sont ajoutés dans le formulaire pour gérer ces flags.
- **Bloc-notes**: Les champs `bloc_note` et `right_column_notepad` sont maintenant situés dans l'onglet "Général".

## Améliorations Futures / TODO

- **Commentaires sur la Timeline du Lecteur**: Implémenter une fonctionnalité permettant aux utilisateurs d'ajouter des commentaires à des moments spécifiques sur la timeline du lecteur audio (similaire à SoundCloud). Cela nécessitera :
    - Une nouvelle table de base de données (ex: `timeline_comments`) avec `song_id`, `user_id`, `timestamp_seconds`, `comment_text`, `avatar_url_snapshot`.
    - Des composants UI pour afficher les marqueurs de commentaires sur la waveform et pour afficher/ajouter des commentaires.
    - Une logique pour lier les commentaires aux timestamps audio.
- **Configuration CORS pour Supabase Storage**: S'assurer que les politiques CORS pour les buckets `covers` et `audio` dans Supabase Storage sont correctement configurées pour autoriser les requêtes `GET` depuis les domaines de l'application (ex: `http://localhost:3000` et le domaine de production). Ceci est crucial pour l'affichage des images et la lecture des fichiers audio.
- **Contraintes d'Upload de Fichiers**: Afficher des informations à l'utilisateur sur les contraintes recommandées pour les fichiers audio et images (ex: taille max, dimensions pour les images, formats) et potentiellement implémenter des validations côté client ou serveur.
- **Synchronisation du Lecteur Waveform**: Améliorer la synchronisation entre le lecteur waveform de l'en-tête de la page du morceau et le lecteur audio global pour une expérience utilisateur plus fluide.
- **Affichage des Données Publiques**: Continuer à affiner l'affichage des informations sur la page publique du morceau pour assurer la cohérence avec les champs disponibles dans le formulaire d'édition et éviter les redondances.
