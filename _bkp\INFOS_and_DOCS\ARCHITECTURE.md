# Architecture Technique - MOUVIK

## Vue d'Ensemble

MOUVIK est construit sur une architecture moderne basée sur Next.js avec l'App Router et Supabase comme backend. Cette architecture permet une expérience utilisateur fluide tout en maintenant une séparation claire entre le frontend et le backend.

## Diagramme d'Architecture

\`\`\`
+----------------------------------+
|            Client                |
|  +----------------------------+  |
|  |      Next.js Frontend      |  |
|  | (React + App Router + SSR) |  |
|  +----------------------------+  |
+----------------------------------+
              |
              | API Calls / Server Actions
              ↓
+----------------------------------+
|         Next.js Backend          |
|  +------------+ +-------------+  |
|  | API Routes | |   Server    |  |
|  |            | |   Actions   |  |
|  +------------+ +-------------+  |
+----------------------------------+
              |
              | Database Queries
              ↓
+----------------------------------+
|           Supabase               |
| +------------+ +---------------+ |
| | PostgreSQL | | Authentication | |
| +------------+ +---------------+ |
| +------------+ +---------------+ |
| |  Storage   | |   Functions   | |
| +------------+ +---------------+ |
+----------------------------------+
\`\`\`

## Couches Applicatives

### 1. Couche Présentation (Frontend)

- **Technologie**: React, Next.js App Router
- **Responsabilités**:
  - Rendu des interfaces utilisateur
  - Gestion des états locaux avec React Hooks
  - Routing côté client
  - Interactions utilisateur
  - Lecture et manipulation audio côté client

### 2. Couche Serveur (Backend)

- **Technologie**: Next.js API Routes, Server Actions
- **Responsabilités**:
  - Traitement des requêtes API
  - Validation des données
  - Logique métier
  - Communication avec Supabase
  - Authentification et autorisation

### 3. Couche Données

- **Technologie**: Supabase (PostgreSQL)
- **Responsabilités**:
  - Stockage persistant des données
  - Relations entre entités
  - Indexation et recherche
  - Sécurité au niveau de la base de données

## Modèle de Données

### Principales Entités

1. **Users**
   - Informations d'authentification
   - Rôles et permissions

2. **Profiles**
   - Informations personnelles
   - Préférences utilisateur
   - Relations sociales

3. **Tracks**
   - Métadonnées des pistes audio
   - Références aux fichiers audio
   - Statistiques d'écoute

4. **Albums**
   - Collections de pistes
   - Métadonnées (titre, date, artwork)
   - Relations avec les artistes

5. **Playlists**
   - Collections personnalisées de pistes
   - Métadonnées (titre, description)
   - Visibilité et partage

6. **Comments**
   - Commentaires sur les pistes/albums
   - Relations avec les utilisateurs
   - Horodatage

7. **Subscriptions**
   - Informations d'abonnement
   - Historique de paiement
   - Niveau d'accès

## Flux de Données

### Authentification

1. L'utilisateur soumet ses identifiants via le formulaire de connexion
2. Next.js transmet les identifiants à Supabase Auth
3. Supabase valide les identifiants et génère un JWT
4. Le JWT est stocké côté client pour les requêtes authentifiées
5. Les routes protégées vérifient la validité du JWT

### Lecture Audio

1. L'utilisateur sélectionne une piste à lire
2. Le client demande l'URL du fichier audio via une API Route
3. L'API Route vérifie les permissions et génère une URL signée
4. Le client charge et lit le fichier audio
5. Les événements de lecture sont enregistrés pour les statistiques

### Création de Contenu

1. L'utilisateur téléverse un fichier audio
2. Le fichier est envoyé à Supabase Storage via une API Route
3. Les métadonnées sont extraites et stockées dans la base de données
4. Le traitement audio (waveform, normalisation) est effectué
5. La piste devient disponible dans la bibliothèque de l'utilisateur

## Sécurité

### Authentification et Autorisation

- Authentification basée sur JWT avec Supabase Auth
- Politiques RLS (Row Level Security) dans PostgreSQL
- Vérification des rôles pour les fonctionnalités administratives
- Protection CSRF pour les formulaires et API Routes

### Sécurité des Données

- Chiffrement des données sensibles
- URLs signées pour les ressources protégées
- Validation des entrées utilisateur
- Sanitization des sorties HTML

## Performances

### Stratégies d'Optimisation

- Rendu côté serveur (SSR) pour le chargement initial rapide
- Streaming audio adaptatif
- Mise en cache des ressources statiques
- Chargement paresseux des composants et médias
- Optimisation des images et ressources audio

## Extensibilité

L'architecture est conçue pour permettre l'ajout facile de nouvelles fonctionnalités :

- Système modulaire de composants React
- API Routes indépendantes pour chaque domaine fonctionnel
- Modèle de données extensible avec relations flexibles
- Hooks personnalisés pour la logique réutilisable

## Architecture Technique - Intégration des Nouvelles Fonctionnalités

Ce document détaille l'architecture technique pour l'intégration des modules "Découvrir & Playlists" et "Communauté" au sein de la plateforme MOUVIK (Next.js App Router + Supabase).

## 1. Vue d'Ensemble

L'intégration repose sur :
- **Nouvelles routes dédiées** dans `/app` pour les pages principales (`/discover`, `/community`, `/playlists/[id]`, `/bands/[id]`).
- **Nouvelles routes API** dans `/app/api` pour les interactions backend (`/api/discover/*`, `/api/community/*`).
- **Nouveaux composants React réutilisables** dans `/components/discover` et `/components/community`.
- **Nouvelles tables Supabase** pour stocker les données spécifiques (`music_resources`, `tags`, `activity`, `playlists`, etc.).
- **Utilisation des Server Actions Next.js** pour certaines mutations (likes, commentaires, ajout rapide).
- **Intégration avec les tables existantes** (`profiles`, `my_songs`, `bands`, `albums`).

## 2. Module "Découvrir & Playlists"

### 2.1. Flux de Données (Import URL)
1.  **Frontend (`/discover/import`)**: L'utilisateur saisit une URL.
2.  **Frontend Component (`ImportForm`)**: Envoi de l'URL à l'API (`/api/discover/preview`).
3.  **API Route (`/api/discover/preview`)**: 
    - Détecte la plateforme (Spotify, Suno, etc.).
    - Appelle le service d'extraction approprié (ex: Spotify API via `fetch`).
    - Retourne les métadonnées extraites (titre, artiste, cover, etc.).
4.  **Frontend Component (`ImportForm`)**: Affiche la preview et les suggestions de tags (requête `/api/tags/suggest`).
5.  **Frontend Component (`ImportForm`)**: L'utilisateur valide/modifie et soumet le formulaire.
6.  **API Route (`/api/discover/resources` - POST)**: 
    - Valide les données.
    - Vérifie l'unicité de l'URL externe.
    - Recherche/Crée les `tags` nécessaires.
    - Insère la nouvelle entrée dans `music_resources` (Supabase).
    - Crée les liens dans `resource_tags` (Supabase).
    - Crée une entrée dans `activity` (Supabase) via une fonction de service partagée.
    - Retourne la ressource créée.
7.  **Frontend**: Affiche une confirmation et redirige (optionnel).

### 2.2. Composants Clés
- `ResourceCard`: Affiche une ressource musicale (interne/externe) avec badge provenance.
- `ResourceList`: Affiche une liste de `ResourceCard` avec filtres/pagination.
- `ImportForm`: Formulaire d'importation via URL.
- `PlaylistManager`: UI pour créer/gérer les playlists.
- `TagInput`: Champ de saisie avec auto-complétion pour les tags.

### 2.3. Tables Supabase Principales
- `music_resources`
- `tags`
- `resource_tags`
- `playlists`
- `playlist_resources`

### 2.4. API Endpoints (/api/discover/*)
- `GET /resources`: Lister les ressources (avec filtres: plateforme, tags, user, etc.).
- `POST /resources`: Ajouter une nouvelle ressource (interne ou externe).
- `GET /resources/{id}`: Obtenir détails d'une ressource.
- `PUT /resources/{id}`: Mettre à jour une ressource.
- `DELETE /resources/{id}`: Supprimer une ressource.
- `POST /preview`: Obtenir la preview d'une URL externe.
- `GET /playlists`: Lister les playlists de l'utilisateur/groupe.
- `POST /playlists`: Créer une playlist.
- `GET /playlists/{id}`: Obtenir détails d'une playlist.
- `PUT /playlists/{id}`: Mettre à jour une playlist (infos, membres).
- `DELETE /playlists/{id}`: Supprimer une playlist.
- `POST /playlists/{id}/resources`: Ajouter une ressource à une playlist.
- `DELETE /playlists/{id}/resources/{resource_id}`: Retirer une ressource d'une playlist.

## 3. Module "Communauté"

### 3.1. Flux de Données (Post Texte)
1.  **Frontend (`/community` ou `/bands/[id]`)**: L'utilisateur écrit un post dans `PostInput`.
2.  **Frontend Component (`PostInput`)**: Utilisation d'une Server Action (`createPostAction`) ou appel API (`/api/community/activity` - POST).
3.  **Server Action / API Route**: 
    - Valide le contenu.
    - Détecte les `#hashtags`.
    - Recherche/Crée les `hashtags` nécessaires.
    - Insère la nouvelle entrée dans `activity` (`type='post_text'`).
    - Crée les liens dans `activity_hashtags`.
    - Rafraîchit potentiellement le cache (revalidation).
    - Retourne l'activité créée.
4.  **Frontend (`ActivityFeed`)**: Le nouveau post apparaît (via revalidation ou mise à jour manuelle du state).

### 3.2. Composants Clés
- `ActivityFeed`: Affiche le flux d'activités avec filtres.
- `ActivityCard`: Affiche une entrée d'activité (post, ajout ressource, etc.).
- `PostInput`: Composant pour créer un nouveau post texte.
- `CommentSection`: Affiche et permet d'ajouter des commentaires.
- `LikeButton`: Bouton pour liker une activité/commentaire (utilise Server Action).
- `BandManager`: UI pour créer/gérer les groupes.
- `HashtagPill`: Affiche un hashtag cliquable.

### 3.3. Tables Supabase Principales
- `activity`
- `hashtags`
- `activity_hashtags`
- `likes`
- `band_members` (existante)
- `bands` (existante)

### 3.4. API Endpoints (/api/community/*)
- `GET /activity`: Lister les activités (avec filtres: type, user, band, hashtag, parent_id).
- `POST /activity`: Créer une nouvelle activité (post, commentaire).
- `GET /activity/{id}`: Obtenir détails d'une activité.
- `DELETE /activity/{id}`: Supprimer une activité.
- `POST /activity/{id}/like`: Liker une activité (ou Server Action).
- `DELETE /activity/{id}/like`: Unlike une activité (ou Server Action).
- `GET /hashtags`: Lister les hashtags (potentiellement avec trending).
- `GET /hashtags/{label}`: Lister les activités pour un hashtag.
- (Gestion des `bands` via API existante ou nouvelle API `/api/bands/*` si besoin d'extension)

## 4. Intégration & Points Clés

- **Authentification**: Gérée par Supabase Auth, `user_id` est central.
- **Services Partagés**: Des fonctions utilitaires (ex: `createActivityEntry`, `extractMetadata`) seront placées dans `/lib` pour être réutilisées par les API Routes et Server Actions.
- **Gestion des Tags**: Un endpoint `/api/tags` centralisé pourrait gérer la création, la recherche et la suggestion des tags (utilisé par `discover` et potentiellement `community`).
- **Notifications**: Un système de notifications (probablement une nouvelle table `notifications` et des triggers Supabase ou logique API) sera nécessaire mais est défini comme P3.
- **Modération**: Des mécanismes de modération (signalement, rôles) seront intégrés progressivement.
- **Extensibilité**: L'architecture modulaire (composants dédiés, API séparées, tables spécifiques) facilite l'ajout futur de nouvelles plateformes (Découvrir) ou types d'activités (Communauté).

## 5. Workflows détaillés & Points critiques

### 5.1. Workflow d'import multi-plateforme (Découvrir)

1. **Saisie URL** (UI `/discover/import`)
2. **Détection plateforme** (API `/api/discover/preview`)
3. **Extraction métadonnées** (service dédié, API officielle prioritaire, scraping en fallback)
4. **Preview UI** : Affichage des métadonnées, suggestion intelligente de tags, badges de provenance
5. **Validation utilisateur** : L'utilisateur peut modifier/compléter les tags (auto-complétion sur la table `tags`)
6. **Création ressource** (API `/api/discover/resources`)
    - Validation de l'unicité (URL, titre)
    - Création de la ressource dans `music_resources` (stockage de `platform_data` brut)
    - Ajout automatique du tag `platform:[nom]`
    - Création/lien des tags (table `resource_tags`)
    - Création d'une activité `add_resource` (table `activity`)
7. **Redirection/confirmation**

### 5.2. Workflow gestion playlists

- Création/édition de playlists via UI dédiée (`PlaylistManager`)
- Ajout/retrait de ressources (internes/externes) via API (`playlist_resources`)
- Gestion des permissions (privé/public/partagé)
- Maintien de l'ordre via champ `order`

### 5.3. Workflow activité communautaire

- Création de posts texte, commentaires, likes via API `/api/community/activity` et Server Actions
- Détection/gestion automatique des hashtags (création, liaison, unicité)
- Affichage du mur d'activité filtrable (type, user, groupe, hashtag)
- Gestion des commentaires imbriqués (parent_activity_id)
- Système de likes (table `likes`), UI réactive (optimistic update)

### 5.4. Modération & rôles

- Signalement possible sur posts, commentaires, ressources, tags (table `reports` à prévoir)
- Rôles via `band_members` ou table `roles` (admin, modérateur, user)
- Historique des actions (soft delete recommandé)

### 5.5. Gestion avancée des tags et taxonomie

- Centralisation dans table `tags` (types : genre, style, mood, platform, custom)
- Normalisation et unicité des labels (minuscule, sans espace)
- Suggestion intelligente et auto-complétion UI
- Système de modération/suggestion pour les tags custom

### 5.6. Provenance & UI/UX

- Badges/logos de plateforme sur chaque ressource (ex: Spotify, Suno, MOUVIK)
- Player adapté selon la provenance (player interne ou embed)
- Preview systématique à l'import
- Affichage contextuel dans le feed (type d'activité, ressource, post)
- Pagination efficace (curseur ou offset)

### 5.7. Relations clés entre modules

- Les activités `add_resource` relient le module Découvrir au mur Communauté
- Les tags sont utilisés à la fois pour filtrer Découvrir et structurer la navigation Communauté (hashtags)
- Les playlists peuvent être partagées dans le mur d'activité
- Les groupes/bands ont leur propre feed communautaire (filtrage par `band_id`)

### 5.8. Exemples de schémas relationnels (simplifiés)

```mermaid
erDiagram
    music_resources ||--o{ resource_tags : ""
    tags ||--o{ resource_tags : ""
    playlists ||--o{ playlist_resources : ""
    music_resources ||--o{ playlist_resources : ""
    activity ||--o{ activity_hashtags : ""
    hashtags ||--o{ activity_hashtags : ""
    activity ||--o{ likes : ""
    profiles ||--o{ activity : ""
    bands ||--o{ band_members : ""
    bands ||--o{ activity : ""
```

## 6. Sécurité, performances et extensibilité

- **Validation stricte** des entrées utilisateur (backend & frontend)
- **Gestion des tokens OAuth** sécurisée pour les APIs externes
- **Rate limiting** sur les endpoints d'import/scraping
- **Modularité** : chaque plateforme/source peut être ajoutée comme un service indépendant
- **Tests** : endpoints critiques couverts par des tests unitaires/intégration
- **Respect RGPD** : suppression/édition des données utilisateurs, anonymisation si nécessaire

## 7. Points d’intégration avec la roadmap et les guidelines

- Toutes les entités, workflows et endpoints décrits ici sont alignés avec les tâches et directives de [TASKS.md] et [GUIDELINES.md].
- La roadmap ([ROADMAP.md]) suit la progression logique de l’implémentation de chaque module, avec des livrables clairs à chaque phase.
- Les conventions de nommage, la gestion des tags, la modération, et l’UI sont détaillées dans [GUIDELINES.md] et respectées ici.

---

Pour toute modification, se référer systématiquement à la roadmap, aux guidelines et à la liste des tâches pour garantir la cohérence globale du projet.
