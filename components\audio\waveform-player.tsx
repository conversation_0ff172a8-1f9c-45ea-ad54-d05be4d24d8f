"use client"

import React, { useEffect, useRef, useState } from "react"
import WaveSurfer from "wavesurfer.js"
import { Play, Pause } from "lucide-react";

interface WaveformPlayerProps {
  audioUrl: string
  height?: number
  waveColor?: string
  progressColor?: string
}

export function WaveformPlayer({
  audioUrl,
  height = 64,
  waveColor = "#38bdf8",
  progressColor = "#0ea5e9"
}: WaveformPlayerProps) {
  const waveformRef = useRef<HTMLDivElement>(null)
  const wavesurfer = useRef<WaveSurfer | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)

  useEffect(() => {
    if (!audioUrl || !waveformRef.current) return
    if (wavesurfer.current) {
      wavesurfer.current.destroy()
      wavesurfer.current = null
    }
    wavesurfer.current = WaveSurfer.create({
      container: waveformRef.current,
      waveColor,
      progressColor,
      height,
      barWidth: 2,
      barRadius: 2,

      cursorColor: progressColor,
      cursorWidth: 2,
      normalize: true,
      interact: true,
    })
    wavesurfer.current.load(audioUrl)
    wavesurfer.current.on("ready", () => {
      setDuration(wavesurfer.current!.getDuration())
    })
    wavesurfer.current.on("audioprocess", () => {
      setCurrentTime(wavesurfer.current!.getCurrentTime())
    })
    wavesurfer.current.on("interaction", () => {
      setCurrentTime(wavesurfer.current!.getCurrentTime())
    })
    wavesurfer.current.on("finish", () => {
      setIsPlaying(false)
    })
    return () => {
      wavesurfer.current?.destroy()
      wavesurfer.current = null
    }
  }, [audioUrl, height, waveColor, progressColor])

  const togglePlay = () => {
    if (wavesurfer.current) {
      wavesurfer.current.playPause()
      setIsPlaying(wavesurfer.current.isPlaying())
    }
  }

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value)
    if (wavesurfer.current) {
      wavesurfer.current.seekTo(value / duration)
    }
  }

  return (
    <div className="w-full flex flex-col items-center">
      <div
        ref={waveformRef}
        className="w-full bg-transparent rounded-lg shadow-md cursor-pointer"
        style={{ height }}
      />
      <div className="flex items-center gap-2 w-full mt-2">
        {/* Modern round play/pause icon button */}
        <button
          onClick={togglePlay}
          aria-label={isPlaying ? "Pause" : "Play"}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-cyan-500 text-white hover:bg-cyan-600 transition shadow-lg focus:outline-none focus:ring-2 focus:ring-cyan-400"
        >
          {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
        </button>
        <input
          type="range"
          min={0}
          max={duration}
          step={0.01}
          value={currentTime}
          onChange={handleSliderChange}
          className="flex-1 accent-cyan-500"
        />
        <span className="text-xs tabular-nums w-16 text-right">
          {formatTime(currentTime)} / {formatTime(duration)}
        </span>
      </div>
    </div>
  )
}

function formatTime(time: number) {
  const min = Math.floor(time / 60)
  const sec = Math.floor(time % 60)
  return `${min}:${sec.toString().padStart(2, "0")}`
}
