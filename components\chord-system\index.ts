/**
 * 🎼 SYSTÈME D'ACCORDS UNIFIÉ - Point d'Entrée Principal
 * 
 * Module unifié pour la gestion des accords dans MOUVIK
 * Interface simple et cohérente pour tous les composants
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

// ============================================================================
// EXPORTS PRINCIPAUX - COMPOSANTS
// ============================================================================

// Provider principal
export { ChordSystemProvider, useChordSystem } from './providers/ChordSystemProvider';

// Composants de navigation et sélection
export { ChordLibraryBrowser } from './components/ChordLibraryBrowser';
export { ChordPickerModal } from './components/ChordPickerModal';

// Composants de visualisation
export { ChordDiagramViewer } from './components/ChordDiagramViewer';

// Composants de construction
export { ChordProgressionBuilder } from './components/ChordProgressionBuilder';
export { ChordGridSystem } from './components/ChordGridSystem';

// Composants de gestion
export { ChordSaveManager } from './components/ChordSaveManager';

// ============================================================================
// EXPORTS PRINCIPAUX - HOOKS
// ============================================================================

// Hook principal unifié
export { useChordSystem } from './providers/ChordSystemProvider';

// Hooks spécialisés
export { useChordLibrary } from './hooks/useChordLibrary';
export { useChordPlayer } from './hooks/useChordPlayer';
export { useChordPersistence } from './hooks/useChordPersistence';
export { useChordSearch } from './hooks/useChordSearch';
export { useAIChordSuggestions } from './hooks/useAIChordSuggestions';

// ============================================================================
// EXPORTS PRINCIPAUX - TYPES
// ============================================================================

// Types principaux
export type {
  // Types de base
  InstrumentType,
  DifficultyLevel,
  ChordCategory,
  PlaybackMode,
  ArpeggioPattern,
  
  // Interfaces principales
  UnifiedChordPosition,
  ChordProgression,
  ChordGridSection,
  ChordMeasure,
  ChordPlacement,
  InstrumentConfig,
  
  // Interfaces de recherche
  ChordSearchFilters,
  ChordSearchResult,
  ChordSuggestion,
  
  // Interfaces système
  ChordSystemState,
  ChordSystemActions,
  PlaybackOptions,
  
  // Interfaces JSON (compatibilité)
  ChordJsonDefinition,
  ChordJsonVariation,
  ChordJsonPosition,
  BarrePosition
} from './types/chord-system';

// ============================================================================
// EXPORTS PRINCIPAUX - UTILITAIRES
// ============================================================================

// Gestionnaire de données
export { ChordDataManager, loadInstrumentSafely, isInstrumentAvailable, getAvailableInstruments, getAvailableTunings } from './utils/ChordDataManager';

// Moteur audio
export { ChordAudioEngine } from './utils/ChordAudioEngine';

// Validation
export { ChordValidation } from './utils/ChordValidation';

// Théorie musicale
export { ChordMusicTheory } from './utils/ChordMusicTheory';

// ============================================================================
// EXPORTS PRINCIPAUX - CONSTANTES
// ============================================================================

// Constantes importantes
export {
  SUPPORTED_INSTRUMENTS,
  CHORD_DIFFICULTIES,
  DEFAULT_TUNINGS
} from './types/chord-system';

// ============================================================================
// INTERFACE SIMPLIFIÉE POUR DÉVELOPPEURS
// ============================================================================

/**
 * Interface simplifiée pour utiliser le système d'accords
 * 
 * @example
 * ```tsx
 * import { ChordSystem } from '@/components/chord-system';
 * 
 * function MyComponent() {
 *   return (
 *     <ChordSystem.Provider>
 *       <ChordSystem.Browser />
 *       <ChordSystem.Viewer />
 *       <ChordSystem.ProgressionBuilder />
 *     </ChordSystem.Provider>
 *   );
 * }
 * ```
 */
export const ChordSystem = {
  // Provider
  Provider: ChordSystemProvider,
  
  // Composants principaux
  Browser: ChordLibraryBrowser,
  Viewer: ChordDiagramViewer,
  Picker: ChordPickerModal,
  ProgressionBuilder: ChordProgressionBuilder,
  Grid: ChordGridSystem,
  SaveManager: ChordSaveManager,
  
  // Hooks
  useSystem: useChordSystem,
  useLibrary: useChordLibrary,
  usePlayer: useChordPlayer,
  usePersistence: useChordPersistence,
  useSearch: useChordSearch,
  useAISuggestions: useAIChordSuggestions,
  
  // Utilitaires
  DataManager: ChordDataManager,
  AudioEngine: ChordAudioEngine,
  Validation: ChordValidation,
  MusicTheory: ChordMusicTheory
} as const;

// ============================================================================
// FONCTIONS UTILITAIRES RAPIDES
// ============================================================================

/**
 * Crée un accord unifié à partir de données JSON
 */
export function createUnifiedChord(
  jsonData: ChordJsonPosition,
  chordName: string,
  instrument: InstrumentType,
  tuning: string = 'standard'
): UnifiedChordPosition {
  return {
    id: crypto.randomUUID(),
    chord: chordName,
    instrument,
    tuning,
    frets: jsonData.frets,
    fingers: jsonData.fingers,
    baseFret: jsonData.baseFret || 1,
    barres: jsonData.barres,
    midi: jsonData.midi,
    difficulty: jsonData.difficulty || 'intermediate',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
}

/**
 * Valide si un objet est un accord unifié valide
 */
export function isValidUnifiedChord(obj: unknown): obj is UnifiedChordPosition {
  if (!obj || typeof obj !== 'object') return false;
  
  const chord = obj as Partial<UnifiedChordPosition>;
  
  return !!(
    chord.id &&
    chord.chord &&
    chord.instrument &&
    chord.frets &&
    Array.isArray(chord.frets) &&
    chord.baseFret !== undefined
  );
}

/**
 * Convertit un accord existant vers le format unifié
 */
export function convertToUnifiedChord(
  existingChord: any,
  sourceFormat: 'legacy' | 'json' | 'component' = 'legacy'
): UnifiedChordPosition {
  // Logique de conversion selon le format source
  switch (sourceFormat) {
    case 'json':
      return createUnifiedChord(
        existingChord,
        existingChord.name || 'Unknown',
        existingChord.instrument || 'guitar'
      );
      
    case 'component':
      return {
        id: existingChord.id || crypto.randomUUID(),
        chord: existingChord.chord || existingChord.name || 'Unknown',
        instrument: existingChord.instrument || 'guitar',
        tuning: existingChord.tuning || 'standard',
        frets: existingChord.frets || existingChord.positions || [],
        fingers: existingChord.fingers,
        baseFret: existingChord.baseFret || 1,
        barres: existingChord.barres,
        midi: existingChord.midi || existingChord.midi_notes,
        difficulty: existingChord.difficulty || 'intermediate',
        createdAt: existingChord.created_at || new Date().toISOString(),
        updatedAt: existingChord.updated_at || new Date().toISOString()
      };
      
    default: // legacy
      return {
        id: crypto.randomUUID(),
        chord: existingChord.chord || existingChord.name || 'Unknown',
        instrument: existingChord.instrument || 'guitar',
        tuning: 'standard',
        frets: existingChord.frets || [],
        baseFret: 1,
        difficulty: 'intermediate',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
  }
}

/**
 * Obtient la configuration d'un instrument
 */
export function getInstrumentConfig(instrument: InstrumentType): InstrumentConfig {
  return SUPPORTED_INSTRUMENTS[instrument];
}

/**
 * Obtient les accordages disponibles pour un instrument
 */
export function getAvailableTunings(instrument: InstrumentType): string[] {
  const config = getInstrumentConfig(instrument);
  return Object.keys(config.tunings);
}

/**
 * Obtient l'accordage par défaut d'un instrument
 */
export function getDefaultTuning(instrument: InstrumentType): string {
  return DEFAULT_TUNINGS[instrument];
}

/**
 * Vérifie si un instrument supporte les cordes
 */
export function isStringedInstrument(instrument: InstrumentType): boolean {
  return instrument !== 'piano';
}

/**
 * Obtient le nombre de cordes d'un instrument
 */
export function getStringCount(instrument: InstrumentType): number {
  const config = getInstrumentConfig(instrument);
  return config.strings || 0;
}

// ============================================================================
// TYPES POUR L'EXPORT
// ============================================================================

// Re-export des types principaux pour faciliter l'import
export type { UnifiedChordPosition as ChordPosition } from './types/chord-system';
export type { ChordProgression } from './types/chord-system';
export type { ChordSystemState, ChordSystemActions } from './types/chord-system';

// ============================================================================
// VERSION ET MÉTADONNÉES
// ============================================================================

/** Version du système d'accords */
export const CHORD_SYSTEM_VERSION = '1.0.0';

/** Métadonnées du système */
export const CHORD_SYSTEM_META = {
  version: CHORD_SYSTEM_VERSION,
  name: 'MOUVIK Unified Chord System',
  description: 'Système d\'accords unifié pour musiciens professionnels',
  author: 'MOUVIK Team',
  date: '2025-06-11',
  features: [
    'Multi-instruments (guitare, piano, ukulélé, mandoline, banjo, basse)',
    'Accordages multiples par instrument',
    'Recherche et filtrage avancés',
    'Suggestions IA harmoniques',
    'Grille de composition professionnelle',
    'Audio et MIDI intégrés',
    'Persistance Supabase',
    'Interface ergonomique pour musiciens'
  ]
} as const;
