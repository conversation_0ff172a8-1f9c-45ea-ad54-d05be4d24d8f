"use client";

import { useEffect, useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation'; 
import { SupabaseClient } from '@supabase/supabase-js'; 
import { useForm, UseFormReturn, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from "@/hooks/use-toast"; 

import { Button } from "@/components/ui/button"; 
import { MultiSelect } from '@/components/ui/multi-select';
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { ScrollArea } from '@/components/ui/scroll-area'; 
import { Cog, Loader2 as LoadingSpinner, Music2, UploadCloud, History, ChevronDown, ChevronUp } from 'lucide-react'; 
// import SongVault from '@/components/song-vault'; // Uncomment if SongVault is used
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from '@/components/ui/switch'; 
import { DatePicker } from "@/components/ui/date-picker"; 
import Image from 'next/image';
import { AiConfigMenu } from "@/components/ia/ai-config-menu";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { AiQuickActions } from "@/components/ia/ai-quick-actions";
import AudioWaveformPreview from '@/components/audio-waveform-preview';
import { RichLyricsEditor } from "@/components/ui/rich-lyrics-editor";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"; // Import Collapsible
import type Quill from 'quill'; // Import Quill type for ref

// Constants
export const musicalKeys = [
  "C", "C#", "Db", "D", "D#", "Eb", "E", "F", "F#", "Gb", "G", "G#", "Ab", "A", "A#", "Bb", "B",
  "Am", "A#m", "Bbm", "Bm", "Cm", "C#m", "Dbm", "Dm", "D#m", "Ebm", "Em", "Fm", "F#m", "Gbm", "Gm", "G#m", "Abm"
];
export const timeSignatures = ["2/4", "3/4", "4/4", "5/4", "6/8", "7/8", "9/8", "12/8", "Autre"];
export const NO_ALBUM_SELECTED_VALUE = "__NO_ALBUM__";
export const LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY = "ai_selected_provider_mouvik";
export const LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY = "ollama_selected_model_mouvik";
export const LOCAL_STORAGE_OPENAI_SELECTED_MODEL_KEY = "openai_selected_model_mouvik";
export const LOCAL_STORAGE_OPENROUTER_SELECTED_MODEL_KEY = "openrouter_selected_model_mouvik";
export const LOCAL_STORAGE_ANTHROPIC_SELECTED_MODEL_KEY = "anthropic_selected_model_mouvik";

// Expanded Options for MultiSelects
const genreOptions = [
  { value: "pop", label: "Pop" }, { value: "rock", label: "Rock" }, { value: "rap", label: "Rap/Hip-Hop" },
  { value: "rnb", label: "R&B/Soul" }, { value: "electro", label: "Electro/Dance" }, { value: "chanson", label: "Chanson Française" },
  { value: "jazz", label: "Jazz" }, { value: "blues", label: "Blues" }, { value: "reggae", label: "Reggae/Dancehall" },
  { value: "metal", label: "Metal" }, { value: "punk", label: "Punk" }, { value: "funk", label: "Funk/Disco" },
  { value: "classique", label: "Classique" }, { value: "folk", label: "Folk/Acoustique" }, { value: "country", label: "Country" },
  { value: "latino", label: "Latino (Salsa, Bachata...)" }, { value: "world", label: "World Music" }, { value: "afro", label: "Afro (Beat, Pop...)" },
  { value: "ambient", label: "Ambient/Experimental" }, { value: "soundtrack", label: "Musique de Film/Soundtrack" }, { value: "autre", label: "Autre" },
];
const moodOptions = [
  { value: "joyeux", label: "Joyeux/Optimiste" }, { value: "triste", label: "Triste/Mélancolique" }, { value: "énergique", label: "Énergique/Puissant" },
  { value: "calme", label: "Calme/Relaxant" }, { value: "romantique", label: "Romantique/Amoureux" }, { value: "sombre", label: "Sombre/Introspectif" },
  { value: "épique", label: "Épique/Grandios" }, { value: "planant", label: "Planant/Aérien" }, { value: "mystérieux", label: "Mystérieux/Intrigant" },
  { value: "festif", label: "Festif/Dansant" }, { value: "agressif", label: "Agressif/Rageur" }, { value: "sensuel", label: "Sensuel/Séduisant" },
  { value: "inspirant", label: "Inspirant/Motivant" }, { value: "nostalgique", label: "Nostalgique" }, { value: "reveur", label: "Rêveur/Onirique" },
  { value: "autre", label: "Autre" },
];
const tagOptions = [
  { value: "summer", label: "Summer/Été" }, { value: "love", label: "Love/Amour" }, { value: "chill", label: "Chill/Détente" },
  { value: "party", label: "Party/Fête" }, { value: "workout", label: "Workout/Sport" }, { value: "roadtrip", label: "Road Trip" },
  { value: "focus", label: "Focus/Concentration" }, { value: "sleep", label: "Sleep/Sommeil" }, { value: "vintage", label: "Vintage/Rétro" },
  { value: "futuriste", label: "Futuriste" }, { value: "urbain", label: "Urbain/Street" }, { value: "nature", label: "Nature" },
  { value: "politique", label: "Politique/Engagé" }, { value: "humour", label: "Humour" }, { value: "instrumental", label: "Instrumental" },
  { value: "autre", label: "Autre" },
];
const instrumentationOptions = [
  { value: "piano", label: "Piano" }, { value: "guitare-acoustique", label: "Guitare Acoustique" }, { value: "guitare-electrique", label: "Guitare Electrique" },
  { value: "basse", label: "Basse" }, { value: "batterie", label: "Batterie" }, { value: "voix", label: "Voix Lead" },
  { value: "choeurs", label: "Chœurs/Backing Vocals" }, { value: "synthetiseur", label: "Synthétiseur" }, { value: "boite-a-rythmes", label: "Boîte à rythmes/Beat" },
  { value: "cordes", label: "Cordes (Violon, Violoncelle...)" }, { value: "cuivres", label: "Cuivres (Trompette, Sax...)" }, { value: "vents", label: "Vents (Flûte, Clarinette...)" },
  { value: "percussions", label: "Percussions (Congas, Bongos...)" }, { value: "orchestre", label: "Orchestre" }, { value: "samples", label: "Samples/Loops" },
  { value: "autre", label: "Autre" },
];


// Custom Types for AI features
export interface AiConfig {
  provider: 'ollama' | 'openai' | 'openrouter' | 'anthropic' | string;
  model: string;
  temperature: number;
}

export interface AiHistoryItem {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: Date; // Optional timestamp
}

// Zod Schema
export const songFormSchema = z.object({
  title: z.string().min(1, { message: "Le titre est requis." }),
  artist_name: z.string().min(1, { message: "Le nom de l'artiste principal est requis." }), // Modifié: rendu obligatoire
  featured_artists: z.array(z.string()).optional(),
  genre: z.array(z.string()).optional(), 
  moods: z.array(z.string()).optional(),
  instrumentation: z.array(z.string()).optional(),
  key: z.string().optional(),
  bpm: z.number().min(0).nullable().optional(),
  time_signature: z.string().optional(),
  capo: z.number().min(0).nullable().optional(),
  tuning_frequency: z.number().min(0).nullable().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  audio_url: z.string().optional().nullable(), // correspond à song_file_path dans le markdown
  image_path: z.string().optional().nullable(), // Chemin de l'image principale
  album_id: z.string().nullable().optional(),
  composer_name: z.string().optional(),
  release_date: z.date().optional().nullable(),
  record_label: z.string().optional(),
  distributor: z.string().optional(),
  isrc: z.string().optional(),
  upc: z.string().optional(),
  is_explicit: z.boolean().default(false).optional(),
  stems_available: z.boolean().default(false).optional(),
  allow_downloads: z.boolean().default(false).optional(),
  allow_comments: z.boolean().default(true).optional(),
  lyrics: z.string().optional(),
  bloc_note: z.string().optional(),
  right_column_notepad: z.string().optional(),
  status: z.enum(['draft', 'published']).optional(), 
});
export type SongFormValues = z.infer<typeof songFormSchema>;

// Interfaces
export interface Album {
  id: string;
  title: string;
}

interface SongFormProps {
  mode: 'create' | 'edit';
  initialValues?: Partial<SongFormValues>;
  onFormSubmit: (data: SongFormValues, status?: 'draft' | 'published') => Promise<void>;
  isSubmitting: boolean;
  albums: Album[];
  isLoadingAlbums: boolean;
  supabaseClient: SupabaseClient;
}

// Component
export function SongForm({
  mode,
  initialValues,
  onFormSubmit,
  isSubmitting,
  albums,
  isLoadingAlbums,
  supabaseClient,
}: SongFormProps) {
  const router = useRouter();

  const form = useForm<SongFormValues>({
    resolver: zodResolver(songFormSchema),
    defaultValues: mode === 'create' ? {
      title: "", artist_name: "", featured_artists: [], genre: [], moods: [], instrumentation: [], key: "",
      bpm: undefined, time_signature: "", capo: undefined, tuning_frequency: 440, description: "", tags: [],
      audio_url: null, image_path: null, /* cover_url: null, */ album_id: null, composer_name: "", release_date: null,
      record_label: "", distributor: "", isrc: "", upc: "", is_explicit: false, stems_available: false,
      allow_downloads: false, allow_comments: true, lyrics: "", bloc_note: "", right_column_notepad: "", status: 'draft'
    } : { ...initialValues, release_date: initialValues?.release_date ? new Date(initialValues.release_date) : null }, 
    mode: 'onChange',
  });

  const [uploadingAudio, setUploadingAudio] = useState(false);
  const [uploadedAudioUrl, setUploadedAudioUrl] = useState<string | null>(initialValues?.audio_url || null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(initialValues?.image_path || null); // Modifié: utilise image_path
  
  const audioFileInputRef = useRef<HTMLInputElement>(null);
  const imageFileInputRef = useRef<HTMLInputElement>(null);
  const quillRef = useRef<any>(null); // Ref for Quill instance

  const [lyricsContent, setLyricsContent] = useState<string>(initialValues?.lyrics || "");
  // Store the actual selection range, not just a boolean
  const [currentSelectionRange, setCurrentSelectionRange] = useState<any>(null); 
  const [aiConfig, setAiConfig] = useState<AiConfig>({
    provider: 'ollama', model: '', temperature: 0.7,
  });
  const [aiLoading, setAiLoading] = useState(false);
  const [aiLastResult, setAiLastResult] = useState<string | undefined>(undefined);
  const [aiError, setAiError] = useState<string | undefined>(undefined);
  const [aiHistory, setAiHistory] = useState<AiHistoryItem[]>([]);
  const [aiMaxOutputTokens, setAiMaxOutputTokens] = useState<number>(256);
  const [aiGeneralPrompt, setAiGeneralPrompt] = useState<string>("You are a helpful assistant for songwriting.");
  const [showAiConfigMenu, setShowAiConfigMenu] = useState(false);
  const [isAiPanelCollapsed, setIsAiPanelCollapsed] = useState(false);
  const [showAiHistory, setShowAiHistory] = useState(false); // State for AI history visibility

  // Utility to strip HTML tags
  const stripHtml = (html: string): string => {
    if (typeof document !== "undefined") {
      const doc = new DOMParser().parseFromString(html, 'text/html');
      return doc.body.textContent || "";
    }
    // Fallback for server-side or environments without DOMParser
    return html.replace(/<[^>]+>/g, ''); 
  };

  // Handler for editor content changes
  const handleLyricsChange = (newContent: string) => {
    setLyricsContent(newContent);
    form.setValue('lyrics', newContent, { shouldValidate: true, shouldDirty: true });
  };

  // Handler for editor selection changes
  const handleEditorSelectionChange = (range: any, source: any, editor: any) => {
    // Store the range object itself. Null if no selection.
    setCurrentSelectionRange(range); 
    // console.log('Selection changed:', range);
  };

  // --- AI Action Handlers ---

  // Helper to add interaction to history
  const addAiHistory = (userPrompt: string, assistantResponse: string) => {
    setAiHistory((prev: AiHistoryItem[]) => [
      ...prev, 
      { role: 'user', content: userPrompt, timestamp: new Date() }, 
      { role: 'assistant', content: assistantResponse, timestamp: new Date() }
    ]);
  };

  // AI Action: Format Lyrics and Chords
  const handleAiFormatLayout = async () => {
    console.log("Trigger AI Format Layout");
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);

    const currentHtmlContent = lyricsContent; 
    if (!currentHtmlContent.trim()) {
      toast({ title: "Formatage IA", description: "Aucun contenu à formater.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    const promptForAI = `
      You are a text formatting assistant for musicians.
      Analyze the following HTML content which contains song lyrics and chords. Chords are marked with bold tags (<strong> or <b>).
      Your task is to reformat this content into a standard chord-over-lyrics format.
      - Identify section headers like [VERSE], [CHORUS], [BRIDGE], [INTRO], [OUTRO], etc., and preserve them on their own lines.
      - Place the identified chords (text within bold tags) on the line directly above the corresponding lyrics.
      - Align chords properly above the syllable or word they correspond to. Use spaces for alignment.
      - Each line of lyrics should be on its own line.
      - Preserve empty lines between sections or verses where appropriate.
      - Output the result as plain text, suitable for a text editor or display. Do not include HTML tags in the output.

      Input HTML:
      \`\`\`html
      ${currentHtmlContent}
      \`\`\`

      Formatted Output:
    `;

    console.log("AI Format Layout Prompt:", promptForAI);

    try {
      // TODO: Replace with actual AI call logic using promptForAI
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call
      const aiResponseText = `[VERSE]\nC          G\nLyrics line one.\nAm           F\nAnother line here.`; // Simulate AI plain text response

      setAiLastResult("Formatage simulé appliqué.");
      const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`;
      handleLyricsChange(newHtmlContent); 
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Formatage appliqué!"});

    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de formatage", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: General Song Suggestions
  const handleAiGeneralSuggestions = async () => {
    console.log("Trigger AI General Suggestions");
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);

    const formData = form.getValues();
    const currentLyricsText = stripHtml(lyricsContent);
    const context = `
      Current Song Context:
      - Title: ${formData.title || 'N/A'}
      - Artist: ${formData.artist_name || 'N/A'}
      - Genre(s): ${formData.genre?.join(', ') || 'N/A'}
      - Mood(s): ${formData.moods?.join(', ') || 'N/A'}
      - Key: ${formData.key || 'N/A'}
      - BPM: ${formData.bpm || 'N/A'}
      - Tags: ${formData.tags?.join(', ') || 'N/A'}
      Current Lyrics:
      \`\`\`
      ${currentLyricsText || '(No lyrics yet)'}
      \`\`\`
    `;
    const promptForAI = `
      ${aiGeneralPrompt}
      Based on the following song context and lyrics, provide general suggestions for improvement. Focus on aspects like:
      - Lyric themes and consistency with genre/mood.
      - Potential song structure ideas (verse, chorus, bridge).
      - Melodic direction or chord progression ideas suitable for the style.
      - Maintaining the overall tone and style indicated by the context.
      - Offer 2-3 distinct suggestions.
      ${context}
      Suggestions:
    `;

    console.log("AI General Suggestions Prompt:", promptForAI);

    try {
      // TODO: Replace with actual AI call logic using promptForAI
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call
      const aiResponseText = `[AI Sim Suggestions based on Genre: ${formData.genre?.join(', ') || 'N/A'} and Mood: ${formData.moods?.join(', ') || 'N/A'}]\n1. Consider adding a bridge after the second chorus to add variation.\n2. The lyrics could explore the theme of '${formData.moods?.[0] || 'the main mood'}' more explicitly in the verses.\n3. A chord progression like Am-G-C-F might fit the ${formData.genre?.[0] || 'song genre'} well for the chorus.`; // Simulate response
      setAiLastResult(aiResponseText); 
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Suggestions générées!"});
    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de suggestion", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Rhyme Suggestions
  const handleAiRhymeSuggestions = async () => {
    console.log("Trigger AI Rhyme Suggestions");
    const selection = currentSelectionRange; 
    const editor = quillRef.current?.getEditor();
    let selectedText = '';

    if (selection && selection.length > 0 && editor) {
      selectedText = editor.getText(selection.index, selection.length);
    } else {
       toast({ title: "Suggestions de Rimes", description: "Veuillez sélectionner du texte pour obtenir des rimes.", variant: "destructive" });
       return;
    }

    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);

    const promptForAI = `${aiGeneralPrompt}\n\nSuggest rhymes for the last word of the selected text: "${selectedText}"`;
    console.log("AI Rhyme Prompt:", promptForAI);

    try {
      // TODO: Replace with actual AI call
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Rhymes for "${selectedText}"]: suggestion1, suggestion2, suggestion3`;
      setAiLastResult(aiResponseText); // Display rhymes in the result area
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Suggestions de rimes générées!"});
    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de suggestion de rimes", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };
  
  // AI Action: Melody Suggestion
  const handleAiMelodySuggestion = async () => {
    console.log("Trigger AI Melody Suggestion");
    const formData = form.getValues();
    const currentLyricsText = stripHtml(lyricsContent);
    const context = `Suggest melody ideas based on:\nGenre: ${formData.genre?.join(', ')}\nMood: ${formData.moods?.join(', ')}\nLyrics: ${currentLyricsText.substring(0, 100)}...`;
    const promptForAI = `${aiGeneralPrompt}\n\n${context}`;
    
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    console.log("Context for Melody Suggestion:", context);

    try {
      // TODO: Implement actual AI call
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Melody Idea]: Try a simple ascending melody in the verses and a more catchy, repetitive one in the chorus, fitting the ${formData.moods?.[0] || 'song'} mood.`;
      setAiLastResult(aiResponseText); 
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Suggestion de mélodie générée!"});
    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de suggestion de mélodie", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };
  
  // AI Action: Recording Advice
  const handleAiRecordingAdvice = async () => {
    console.log("Trigger AI Recording Advice");
     const formData = form.getValues();
     const context = `Provide recording advice for a song with:\nGenre: ${formData.genre?.join(', ')}\nInstrumentation: ${formData.instrumentation?.join(', ')}\nKey: ${formData.key}\nBPM: ${formData.bpm}`;
     const promptForAI = `${aiGeneralPrompt}\n\n${context}`;

     setAiLoading(true); 
     setAiError(undefined); 
     setAiLastResult(undefined);
     console.log("Context for Recording Advice:", context);

     try {
      // TODO: Implement actual AI call (potentially needs profile info later)
       await new Promise(resolve => setTimeout(resolve, 1000)); 
       const aiResponseText = `[AI Sim Recording Advice for ${formData.genre?.[0] || 'this genre'}]: Consider using a condenser mic for vocals. Double-track acoustic guitars for width.`;
       setAiLastResult(aiResponseText); 
       addAiHistory(promptForAI, aiResponseText);
       toast({ title: "IA: Conseil d'enregistrement généré!"});
     } catch (e: any) { 
       setAiError(e.message); 
       toast({ title: "IA: Erreur de conseil d'enregistrement", description: e.message, variant: "destructive"});
     } finally { 
       setAiLoading(false); 
     }
  };

  // AI Action: Instrumentation Suggestion
  const handleAiInstrumentationSuggestion = async () => {
    console.log("Trigger AI Instrumentation Suggestion");
    const formData = form.getValues();
    const context = `Suggest additional instrumentation for a song with:\nGenre: ${formData.genre?.join(', ')}\nMood: ${formData.moods?.join(', ')}\nExisting Instruments: ${formData.instrumentation?.join(', ')}`;
    const promptForAI = `${aiGeneralPrompt}\n\n${context}`;

    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    console.log("Context for Instrumentation Suggestion:", context);

    try {
     // TODO: Implement actual AI call
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Instrumentation Idea for ${formData.genre?.[0] || 'this genre'}]: Adding subtle strings or a synth pad could enhance the ${formData.moods?.[0] || 'current'} mood.`;
      setAiLastResult(aiResponseText); 
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Suggestion d'instruments générée!"});
    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de suggestion d'instruments", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Creative FX Ideas
  const handleAiCreativeFx = async () => {
    console.log("Trigger AI Creative FX Ideas");
     const formData = form.getValues();
     const context = `Suggest creative FX ideas for a song with:\nGenre: ${formData.genre?.join(', ')}\nMood: ${formData.moods?.join(', ')}\nInstrumentation: ${formData.instrumentation?.join(', ')}`;
     const promptForAI = `${aiGeneralPrompt}\n\n${context}`;

     setAiLoading(true); 
     setAiError(undefined); 
     setAiLastResult(undefined);
     console.log("Context for Creative FX:", context);

     try {
      // TODO: Implement actual AI call
       await new Promise(resolve => setTimeout(resolve, 1000)); 
       const aiResponseText = `[AI Sim FX Idea for ${formData.genre?.[0] || 'this genre'}]: Try a subtle delay on the main vocal in the chorus or a phaser on the electric guitar during the bridge.`;
       setAiLastResult(aiResponseText); 
       addAiHistory(promptForAI, aiResponseText);
       toast({ title: "IA: Idée d'effets créatifs générée!"});
     } catch (e: any) { 
       setAiError(e.message); 
       toast({ title: "IA: Erreur d'idée d'effets", description: e.message, variant: "destructive"});
     } finally { 
       setAiLoading(false); 
     }
  };

  // AI Action: Arrangement Advice
  const handleAiArrangementAdvice = async () => {
    console.log("Trigger AI Arrangement Advice");
    const formData = form.getValues();
    const currentLyricsText = stripHtml(lyricsContent);
    const context = `Provide arrangement advice for a song with:\nGenre: ${formData.genre?.join(', ')}\nMood: ${formData.moods?.join(', ')}\nLyrics Structure (from text): ${currentLyricsText.substring(0, 150)}...`;
    const promptForAI = `${aiGeneralPrompt}\n\n${context}`;

    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    console.log("Context for Arrangement Advice:", context);

    try {
      // TODO: Implement actual AI call
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Arrangement Advice]: Consider starting with a stripped-down verse, building intensity towards the chorus by adding more instruments.`;
      setAiLastResult(aiResponseText); 
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Conseil d'arrangement généré!"});
    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de conseil d'arrangement", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Tone Analysis
  const handleAiAnalyzeTone = async () => {
    console.log("Trigger AI Tone Analysis");
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);

    const currentLyricsText = stripHtml(lyricsContent);
    if (!currentLyricsText.trim()) {
      toast({ title: "Analyse de Ton", description: "Aucune parole à analyser.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    const promptForAI = `${aiGeneralPrompt}\n\nAnalyze the overall tone and mood of the following lyrics:\n\n${currentLyricsText}`;
    console.log("AI Tone Analysis Prompt:", promptForAI);

    try {
      // TODO: Replace with actual AI call
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Tone Analysis]: The tone appears to be generally ${form.getValues('moods')?.[0] || 'neutral'} with elements of reflection.`;
      setAiLastResult(aiResponseText); // Display analysis in the result area
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Analyse de ton terminée!"});
    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur d'analyse de ton", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Correct Text
  const handleAiCorrect = async () => {
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    
    let textToProcess = "";
    let promptForAI = "";
    const selection = currentSelectionRange; 
    const editor = quillRef.current?.getEditor();

    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\nCorrect the grammar and spelling of the following selected text:\n\n${textToProcess}`;
      console.log("AI Correct called on SELECTION. Text:", textToProcess, "Config:", aiConfig);
    } else {
      textToProcess = stripHtml(lyricsContent); // Use full content if no selection
      promptForAI = `${aiGeneralPrompt}\n\nCorrect the grammar and spelling of the following text:\n\n${textToProcess}`;
      console.log("AI Correct called on FULL DOCUMENT. Text:", textToProcess, "Config:", aiConfig);
    }

    if (!textToProcess.trim()) {
      toast({ title: "Correction IA", description: "Aucun texte à corriger.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    try {
      // TODO: Replace with actual AI call logic using promptForAI
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      const aiResponseText = `[AI Corrected Sim: ${textToProcess.substring(0, 30)}...]`; // Simulate response

      setAiLastResult(`Correction simulée pour: ${textToProcess.substring(0, 50)}...`);
      
      if (selection && selection.length > 0 && editor) {
        // Replace selection - use pasteHTML for potential formatting preservation/addition
        editor.deleteText(selection.index, selection.length, 'user');
        // Convert plain text response to simple HTML paragraph for insertion
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        // Update full content state after modification
        const newFullContent = editor.root.innerHTML; 
        handleLyricsChange(newFullContent); 
      } else {
        // Replace full content
        const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`;
        handleLyricsChange(newHtmlContent); 
      }

      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Texte corrigé!"});

    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de correction", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Translate Text
  const handleAiTranslate = async (lang: string) => {
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    
    let textToProcess = "";
    let promptForAI = "";
    const selection = currentSelectionRange; 
    const editor = quillRef.current?.getEditor();

    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\nTranslate the following selected text to ${lang}:\n\n${textToProcess}`;
      console.log("AI Translate called on SELECTION. Text:", textToProcess, "Lang:", lang, "Config:", aiConfig);
    } else {
      textToProcess = stripHtml(lyricsContent); // Use full content if no selection
      promptForAI = `${aiGeneralPrompt}\n\nTranslate the following text to ${lang}:\n\n${textToProcess}`;
      console.log("AI Translate called on FULL DOCUMENT. Text:", textToProcess, "Lang:", lang, "Config:", aiConfig);
    }

     if (!textToProcess.trim()) {
      toast({ title: "Traduction IA", description: "Aucun texte à traduire.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    try {
      // TODO: Replace with actual AI call logic using promptForAI
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      const aiResponseText = `[AI Translated Sim to ${lang}: ${textToProcess.substring(0, 30)}...]`; // Simulate response

      setAiLastResult(`Traduction simulée vers ${lang} pour: ${textToProcess.substring(0, 50)}...`);
      
      if (selection && selection.length > 0 && editor) {
        // Replace selection
        editor.deleteText(selection.index, selection.length, 'user');
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        const newFullContent = editor.root.innerHTML; 
        handleLyricsChange(newFullContent); 
      } else {
        // Replace full content
        const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`;
        handleLyricsChange(newHtmlContent); 
      }

      addAiHistory(promptForAI, aiResponseText);
      toast({ title: `IA: Texte traduit en ${lang}!`});

    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de traduction", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Edit General Prompt
  const handleAiEditGeneralPrompt = useCallback((newPrompt: string) => {
    console.log("AI General Prompt Edit:", newPrompt);
    setAiGeneralPrompt(newPrompt);
    toast({
      title: "Prompt général mis à jour",
    });
  }, []);

  // AI Action: Set Config
  const handleAiConfigChange = (newPartialConfig: Partial<AiConfig>) => {
    setAiConfig(prevConfig => ({ ...prevConfig, ...newPartialConfig }));
  };

  // File Upload Handler
  const handleFileUpload = async (file: File, type: 'audio' | 'image') => {
    if (!file) return;
    const fileExt = file.name.split('.').pop();
    const fileName = `${Math.random()}.${fileExt}`;
    const bucketName = type === 'audio' ? 'audio' : 'covers';
    const filePath = `${fileName}`;

    if (type === 'audio') setUploadingAudio(true);
    if (type === 'image') setUploadingImage(true);

    try {
      const { error: uploadError } = await supabaseClient.storage.from(bucketName).upload(filePath, file);
      if (uploadError) throw uploadError;
      const { data: publicURLData } = supabaseClient.storage.from(bucketName).getPublicUrl(filePath);
      if (!publicURLData || !publicURLData.publicUrl) throw new Error('Could not get public URL');

      if (type === 'audio') {
        form.setValue('audio_url', publicURLData.publicUrl, { shouldValidate: true, shouldDirty: true });
        setUploadedAudioUrl(publicURLData.publicUrl);
        toast({ title: "Fichier audio téléversé" });
      } else if (type === 'image') {
        form.setValue('image_path', publicURLData.publicUrl, { shouldValidate: true, shouldDirty: true }); // Modifié pour utiliser image_path
        setUploadedImageUrl(publicURLData.publicUrl);
        toast({ title: "Image de couverture téléversée" });
      }
    } catch (error: any) {
      toast({ title: `Erreur upload ${type}`, description: error.message, variant: "destructive" });
    } finally {
      if (type === 'audio') setUploadingAudio(false);
      if (type === 'image') setUploadingImage(false);
    }
  };

  // Form Submission Handler
  const onSubmitWithStatus = (status?: 'draft' | 'published') => {
    // Ensure status is set correctly in the form data before submission
    const currentStatus = status ?? form.getValues('status') ?? 'draft'; // Default to draft if undefined
    form.setValue('status', currentStatus, { shouldValidate: true, shouldDirty: true });
    
    // Log the data *just before* calling the passed onSubmit handler
    const dataToSubmit = form.getValues();
    console.log(`onSubmitWithStatus called. Mode: ${mode}, Status to set: ${currentStatus}, Data:`, dataToSubmit);
    
    form.handleSubmit((data) => {
        console.log("Data passed to onFormSubmit:", data); // Log data inside handleSubmit callback
        onFormSubmit(data, currentStatus);
    })();
  };
  
  // AI Action: Generate Text
  const handleAiGenerate = async (customPrompt?: string) => { 
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    
    let textToProcess = "";
    let promptForAI = "";
    const selection = currentSelectionRange; // Get current selection state
    const editor = quillRef.current?.getEditor();

    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\n${customPrompt || 'Improve or continue this selected text'}:\n\n${textToProcess}`;
      console.log("AI Generate called on SELECTION. Text:", textToProcess, "Custom Prompt:", customPrompt, "Config:", aiConfig);
    } else {
      textToProcess = stripHtml(lyricsContent); // Use full content if no selection
      promptForAI = `${aiGeneralPrompt}\n\n${customPrompt || 'Continue writing lyrics based on this text'}:\n\n${textToProcess}`;
      console.log("AI Generate called on FULL DOCUMENT. Text:", textToProcess, "Custom Prompt:", customPrompt, "Config:", aiConfig);
    }

    try {
      // TODO: Replace with actual AI call logic using promptForAI
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      const aiResponseText = ` [AI Sim Response for: ${textToProcess.substring(0, 30)}...]`; // Simulate response

      setAiLastResult(`Texte généré (simulation) pour: ${textToProcess.substring(0, 50)}...`);
      
      if (selection && selection.length > 0 && editor) {
        // Replace selection - use pasteHTML for potential formatting preservation/addition
        editor.deleteText(selection.index, selection.length, 'user');
        // Convert plain text response to simple HTML paragraph for insertion
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        // Update full content state after modification
        const newFullContent = editor.root.innerHTML; 
        handleLyricsChange(newFullContent); // Update state and form
      } else {
        // Replace or append full content (current logic appends - needs review based on desired behavior)
        // For now, let's replace the whole content for generation if no selection
        const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`; // Convert response to HTML
        handleLyricsChange(newHtmlContent); // Update editor and form
      }

      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Texte généré!"});

    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de génération", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  }

  // Effects
  useEffect(() => {
    if (mode === 'edit' && initialValues) {
      const valuesToReset = {
        ...initialValues,
        release_date: initialValues.release_date ? new Date(initialValues.release_date) : null,
      };
      form.reset(valuesToReset);
      setUploadedAudioUrl(initialValues.audio_url || null);
      setUploadedImageUrl(initialValues.image_path || null); // Modifié: utilise image_path
      setLyricsContent(initialValues.lyrics || "");
    }
  }, [mode, initialValues, form]);

  useEffect(() => {
    const savedProvider = typeof window !== 'undefined' ? localStorage.getItem(LOCAL_STORAGE_SELECTED_AI_PROVIDER_KEY) || 'ollama' : 'ollama';
    let savedModel = '';
    if (typeof window !== 'undefined'){
      if (savedProvider === 'ollama') savedModel = localStorage.getItem(LOCAL_STORAGE_SELECTED_OLLAMA_MODEL_KEY) || '';
      else if (savedProvider === 'openai') savedModel = localStorage.getItem(LOCAL_STORAGE_OPENAI_SELECTED_MODEL_KEY) || '';
      else if (savedProvider === 'openrouter') savedModel = localStorage.getItem(LOCAL_STORAGE_OPENROUTER_SELECTED_MODEL_KEY) || '';
      else if (savedProvider === 'anthropic') savedModel = localStorage.getItem(LOCAL_STORAGE_ANTHROPIC_SELECTED_MODEL_KEY) || '';
    }
    setAiConfig((prev: AiConfig) => ({ ...prev, provider: savedProvider as AiConfig['provider'], model: savedModel }));
  }, []);


  return (
    <FormProvider {...form}>
      <form className="h-full flex flex-col" onSubmit={form.handleSubmit(data => onFormSubmit(data))}>
        {/* Header Section - Copied from previous attempt */}
        {/* --- Nouveau HEADER HERO FULL WIDTH --- */}
        <div className="w-full bg-background/90 py-6 px-0 md:px-4 flex flex-col md:flex-row md:items-center gap-8 border-b mb-8 max-w-none">
          <div className="flex flex-col items-center md:items-start gap-4 w-full md:w-1/4">
            {/* Image de couverture */}
            <div className="w-full flex flex-col items-center">
              <FormField
                control={form.control}
                name="image_path"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm">Image de couverture</FormLabel>
                    <FormControl>
                      <div className="flex flex-col items-center gap-2">
                        <Input
                          type="file"
                          accept="image/*"
                          ref={imageFileInputRef}
                          onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'image')}
                          className="hidden"
                          id="image-upload"
                        />
                        <Button type="button" variant="outline" size="sm" onClick={() => imageFileInputRef.current?.click()} disabled={uploadingImage || isSubmitting}>
                          {uploadingImage ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                          {uploadedImageUrl ? 'Changer' : 'Choisir une image'}
                        </Button>
                        {uploadedImageUrl && uploadedImageUrl.startsWith('http') && (
                          <Image src={uploadedImageUrl} alt="Preview" width={180} height={180} className="rounded-md object-cover mt-2" />
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex-1 flex flex-col items-center md:items-start gap-4 w-full">
            {/* Titre du morceau */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="sr-only">Titre du morceau</FormLabel>
                  <FormControl>
                    <Input className="text-3xl md:text-4xl font-bold w-full bg-transparent border-none shadow-none px-0 mb-2" placeholder="Titre du morceau *" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Affiche l'artiste principal sous le titre */}
            {(form.watch('artist_name') ?? '').trim() !== '' && (
              <div className="flex items-center mt-1 mb-2">
                <span className="px-3 py-1 rounded-full bg-secondary text-base font-medium text-foreground/80 shadow-sm">
                  {form.watch('artist_name')}
                </span>
              </div>
            )}
            {/* Audio/Waveform */}
            <FormField
              control={form.control}
              name="audio_url"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="text-sm">Fichier audio</FormLabel>
                  <FormControl>
                    <div className="flex flex-col gap-2">
                      <Input
                        type="file"
                        accept="audio/*"
                        ref={audioFileInputRef}
                        onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'audio')}
                        className="hidden"
                        id="audio-upload"
                      />
                      <Button type="button" variant="outline" size="sm" onClick={() => audioFileInputRef.current?.click()} disabled={uploadingAudio || isSubmitting}>
                        {uploadingAudio ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                        {uploadedAudioUrl ? 'Changer' : 'Choisir un fichier audio'}
                      </Button>
                      {uploadedAudioUrl && uploadedAudioUrl.startsWith('http') && (
                        <AudioWaveformPreview audioUrl={uploadedAudioUrl} />
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Boutons action */}
            <div className="flex gap-2 mt-4">
              <Button variant="outline" onClick={() => router.back()} disabled={isSubmitting || uploadingAudio || uploadingImage}>Annuler</Button>
              {mode === 'create' ? (
                <>
                  <Button type="button" onClick={() => onSubmitWithStatus('draft')} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="secondary">
                    {(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}
                    Enregistrer en Brouillon
                  </Button>
                  <Button type="button" onClick={() => onSubmitWithStatus('published')} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg">
                    {(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}
                    Publier
                  </Button>
                </>
              ) : (
                <Button type="button" onClick={() => onSubmitWithStatus(form.getValues('status'))} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg">
                  {(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}
                  Sauvegarder
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* --- FIN HEADER HERO --- */}

        {/* Main Content Area, Full Width */}
        <ScrollArea className="flex-grow pb-6">
          <div className="w-full px-0 md:px-2 space-y-6">
            <Tabs defaultValue="general" className="w-full">
              <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 mb-4"> {/* Adjusted grid columns */}
                <TabsTrigger value="general">Général</TabsTrigger>
                <TabsTrigger value="media">Media</TabsTrigger>
                <TabsTrigger value="lyrics-ia">Paroles / IA</TabsTrigger>
                <TabsTrigger value="publication">Publication & Options</TabsTrigger> {/* Merged Tab */}
              </TabsList>

              {/* General Tab */}
                  <TabsContent value="general" className="w-full">
                <div className="space-y-4 w-full">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Titre du morceau *</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: Mon Super Hit" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="artist_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Artiste principal *</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: Nom de l'artiste" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="featured_artists"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Artistes en featuring</FormLabel>
                          <FormControl>
                            <Input placeholder="Ajouter des artistes en featuring (séparés par des virgules)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <FormField
                        control={form.control}
                        name="genre"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Genre</FormLabel>
                            <FormControl>
                              <MultiSelect
                                options={genreOptions} // Use expanded list
                                selected={field.value || []}
                                onChange={field.onChange}
                                placeholder="Ajouter un ou plusieurs genres"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    {/* Ajout du champ Instrumentation */}
                      <FormField
                        control={form.control}
                        name="instrumentation"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Instrumentation</FormLabel>
                            <FormControl>
                              <MultiSelect
                                options={instrumentationOptions} // Use expanded list
                                selected={field.value || []}
                                onChange={field.onChange}
                                placeholder="Ajouter des instruments"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <FormField
                        control={form.control}
                        name="moods"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Ambiances / Moods</FormLabel>
                            <FormControl>
                              <MultiSelect
                                options={moodOptions} // Use expanded list
                                selected={field.value || []}
                                onChange={field.onChange}
                                placeholder="Ajouter des ambiances (moods)"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                     <FormField
                        control={form.control}
                        name="tags"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tags</FormLabel>
                            <FormControl>
                              <MultiSelect
                                options={tagOptions} // Use expanded list
                                selected={field.value || []}
                                onChange={field.onChange}
                                placeholder="Ajouter des tags"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    {/* Ajout du champ Description sous les tags et moods */}
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Décrivez votre morceau, son histoire, ses inspirations..."
                              className="min-h-[100px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormField
                          control={form.control}
                          name="key"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Tonalité</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger><SelectValue placeholder="Sélectionner une tonalité" /></SelectTrigger>
                                </FormControl>
                                <SelectContent className="bg-background border shadow-md"> {/* Added background */}
                                  {musicalKeys.map(k => <SelectItem key={k} value={k}>{k}</SelectItem>)}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="bpm"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>BPM</FormLabel>
                              <FormControl>
                                <Input type="number" placeholder="120" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))} value={field.value ?? ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="time_signature"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Signature Rythmique</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger><SelectValue placeholder="Sélectionner une signature" /></SelectTrigger>
                                </FormControl>
                                <SelectContent className="bg-background border shadow-md"> {/* Added background */}
                                  {timeSignatures.map(ts => <SelectItem key={ts} value={ts}>{ts}</SelectItem>)}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="capo"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Capo</FormLabel>
                              <FormControl>
                                <Input type="number" placeholder="0" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))} value={field.value ?? ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="tuning_frequency"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Fréquence d'accordage (Hz)</FormLabel>
                              <FormControl>
                                <Input type="number" placeholder="440" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))} value={field.value ?? ''} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="composer_name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Nom du compositeur</FormLabel>
                              <FormControl>
                                <Input placeholder="Ex: Jean Dupont" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
              </TabsContent>

              {/* Media Tab */}
              <TabsContent value="media" className="w-full">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
                  <Card>
                    <CardHeader>
                      <CardTitle>Fichier Audio</CardTitle>
                      <CardDescription>Téléversez le fichier audio principal de votre morceau (MP3, WAV, etc.).</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="audio_url"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Fichier audio</FormLabel>
                            <FormControl>
                              <div>
                                <Input
                                  type="file"
                                  accept="audio/*"
                                  ref={audioFileInputRef}
                                  onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'audio')}
                                  className="hidden"
                                  id="audio-upload"
                                />
                                <Button type="button" variant="outline" onClick={() => audioFileInputRef.current?.click()} disabled={uploadingAudio || isSubmitting}>
                                  {uploadingAudio ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                                  Choisir un fichier audio
                                </Button>
                              </div>
                            </FormControl>
                            {uploadingAudio && <p className='text-sm text-muted-foreground'>Téléversement en cours...</p>}
                            {uploadedAudioUrl && (
                              <div className="mt-4">
                                <p className="text-sm font-medium">Fichier actuel:</p>
                                {/* <a href={uploadedAudioUrl} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-500 hover:underline">{uploadedAudioUrl.split('/').pop()}</a> */}
                                <AudioWaveformPreview audioUrl={uploadedAudioUrl} />
                              </div>
                            )}
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle>Image de Couverture</CardTitle>
                      <CardDescription>Ajoutez une image pour représenter votre morceau (JPEG, PNG, GIF).</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="image_path" // Corrected field name
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Image de couverture</FormLabel>
                            <FormControl>
                              <div>
                                <Input
                                  type="file"
                                  accept="image/*"
                                  ref={imageFileInputRef}
                                  onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'image')}
                                  className="hidden"
                                  id="image-upload"
                                />
                                <Button type="button" variant="outline" onClick={() => imageFileInputRef.current?.click()} disabled={uploadingImage || isSubmitting}>
                                  {uploadingImage ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                                  Choisir une image
                                </Button>
                              </div>
                            </FormControl>
                            {uploadingImage && <p className='text-sm text-muted-foreground'>Téléversement en cours...</p>}
                            {uploadedImageUrl && (
                              <div className="mt-4">
                                <p className="text-sm font-medium">Image actuelle:</p>
                                <Image src={uploadedImageUrl} alt="Preview" width={150} height={150} className="rounded-md object-cover" />
                              </div>
                            )}
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                         {/* Collapsible AI History Section */}
                         <Collapsible open={showAiHistory} onOpenChange={setShowAiHistory} className="mt-4">
                          <CollapsibleTrigger asChild>
                            <Button variant="ghost" size="sm" className="text-xs w-full justify-start text-muted-foreground hover:text-foreground">
                              <History className="mr-2 h-4 w-4" />
                              Historique Assistant IA ({Math.floor(aiHistory.length / 2)} interactions)
                              {showAiHistory ? <ChevronUp className="ml-auto h-4 w-4" /> : <ChevronDown className="ml-auto h-4 w-4" />}
                            </Button>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="mt-2">
                            <ScrollArea className="h-[200px] border rounded-md p-2 bg-muted/30">
                              {aiHistory.length === 0 ? (
                                <p className="text-xs text-muted-foreground text-center py-4">Aucun historique pour le moment.</p>
                              ) : (
                                <div className="space-y-3">
                                  {aiHistory.map((item, index) => (
                                    <div key={index} className={`text-xs p-2 rounded ${item.role === 'user' ? 'bg-blue-50 dark:bg-blue-900/30' : 'bg-gray-50 dark:bg-gray-800/30'}`}>
                                      <p className="font-semibold mb-1 capitalize">{item.role}:</p>
                                      <p className="whitespace-pre-wrap break-words">{item.content}</p>
                                      {item.timestamp && <p className="text-muted-foreground text-[10px] mt-1">{item.timestamp.toLocaleTimeString()}</p>}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </ScrollArea>
                          </CollapsibleContent>
                        </Collapsible>
                      </CardContent>
                    </Card>
                  </div>
              </TabsContent>

              {/* Onglet Paroles / Copo IA */}
              <TabsContent value="lyrics-ia" className="w-full p-0">
                {/* This tab will now be part of a two-column layout if AI panel is not collapsed */}
                {/* Or full width if AI panel is collapsed */}
                <div className={`grid grid-cols-1 ${!isAiPanelCollapsed ? 'lg:grid-cols-3 gap-6' : ''}`}>
                  <div className={!isAiPanelCollapsed ? 'lg:col-span-2' : 'lg:col-span-3'}>
                    <Card className="w-full">
                      <CardHeader>
                        <CardTitle>Paroles</CardTitle>
                        <CardDescription>Écrivez ou générez les paroles de votre morceau.</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <FormField
                          control={form.control}
                          name="lyrics"
                          render={({ field }) => (
                            <FormItem>
                              {/* <FormLabel>Paroles</FormLabel> No need, CardTitle serves this */}
                              <FormControl>
                                <RichLyricsEditor
                                  value={lyricsContent}
                                  onChange={handleLyricsChange}
                                  placeholder="Commencez à écrire les paroles ici..."
                                  className="min-h-[400px]" // Ensure enough height
                                  quillRef={quillRef} // Pass the ref
                                  onSelectionChange={handleEditorSelectionChange} // Add selection handler
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                         {/* Collapsible AI History Section */}
                         <Collapsible open={showAiHistory} onOpenChange={setShowAiHistory}>
                          <CollapsibleTrigger asChild>
                            <Button variant="ghost" size="sm" className="text-xs w-full justify-start text-muted-foreground hover:text-foreground">
                              <History className="mr-2 h-4 w-4" />
                              Historique IA ({Math.floor(aiHistory.length / 2)} interactions)
                              {showAiHistory ? <ChevronUp className="ml-auto h-4 w-4" /> : <ChevronDown className="ml-auto h-4 w-4" />}
                            </Button>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="mt-2">
                            <ScrollArea className="h-[200px] border rounded-md p-2 bg-muted/20">
                              {aiHistory.length === 0 ? (
                                <p className="text-xs text-muted-foreground text-center py-4">Aucun historique pour le moment.</p>
                              ) : (
                                <div className="space-y-3">
                                  {aiHistory.map((item, index) => (
                                    <div key={index} className={`text-xs p-2 rounded ${item.role === 'user' ? 'bg-blue-50 dark:bg-blue-900/30' : 'bg-gray-50 dark:bg-gray-800/30'}`}>
                                      <p className="font-semibold mb-1 capitalize">{item.role}:</p>
                                      <p className="whitespace-pre-wrap break-words">{item.content}</p>
                                      {item.timestamp && <p className="text-muted-foreground text-[10px] mt-1">{item.timestamp.toLocaleTimeString()}</p>}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </ScrollArea>
                          </CollapsibleContent>
                        </Collapsible>
                      </CardContent>
                    </Card>
                  </div>

                  {/* AI Assistant Column (Right) */}
                  {!isAiPanelCollapsed && (
                    <div className="lg:col-span-1 space-y-6">
                       <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                          <div className="space-y-1">
                            <CardTitle className="text-lg">
                              <Music2 className="mr-2 h-5 w-5 inline-block" />
                              Assistance IA
                            </CardTitle>
                            <CardDescription className="text-xs">
                              Suggestions, corrections, formatage.
                            </CardDescription>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button type="button" variant="ghost" size="icon" className="h-7 w-7" onClick={() => setShowAiConfigMenu(true)} title="Configurer l'IA">
                              <Cog className="h-4 w-4" />
                            </Button>
                            <Button type="button" variant="ghost" size="icon" className="h-7 w-7" onClick={() => setIsAiPanelCollapsed(true)} title="Réduire le panneau IA">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><line x1="9" y1="3" x2="9" y2="21"/></svg>
                            </Button>
                          </div>
                        </CardHeader>
                        {/* Remove CardContent padding to give AiQuickActions more space */}
                        <div className="p-0"> 
                          <AiQuickActions 
                            onGenerate={handleAiGenerate}
                            loading={aiLoading} 
                            lastResult={aiLastResult}
                            error={aiError}
                            iaHistory={aiHistory}
                            aiConfig={aiConfig}
                            setAiConfig={handleAiConfigChange}
                            onCorrect={handleAiCorrect}
                            onTranslate={handleAiTranslate}
                            generalPrompt={aiGeneralPrompt} 
                            onEditGeneralPrompt={handleAiEditGeneralPrompt}
                            onFormatLayout={handleAiFormatLayout} 
                            onGeneralSuggestions={handleAiGeneralSuggestions} 
                            onRhymeSuggestions={handleAiRhymeSuggestions} // Pass new handler
                            onAnalyzeTone={handleAiAnalyzeTone} // Pass new handler
                            onMelodySuggestion={handleAiMelodySuggestion} // Pass new handler
                            onRecordingAdvice={handleAiRecordingAdvice} // Pass new handler
                            onInstrumentationSuggestion={handleAiInstrumentationSuggestion} // Pass new handler
                            onCreativeFx={handleAiCreativeFx} // Pass new handler
                            onArrangementAdvice={handleAiArrangementAdvice} // Pass new handler
                            isTextSelected={currentSelectionRange && currentSelectionRange.length > 0} // Pass selection state based on range
                          />
                          <AiConfigMenu
                            isPopoverOpen={showAiConfigMenu}
                            setIsPopoverOpen={setShowAiConfigMenu}
                          />
                        </div> {/* Close the div wrapping AiQuickActions */}
                      </Card>
                    </div>
                  )}
                </div> {/* This closes the main grid div */}
                 {/* Button to re-open AI panel if collapsed - Placed outside the grid, maybe fixed or absolutely positioned relative to the tab content */}
                 {isAiPanelCollapsed && (
                    <div className="absolute top-0 right-0 mt-2 mr-2 z-20"> 
                       <Button
                        variant="outline"
                        size="icon" // Make it smaller
                        onClick={() => setIsAiPanelCollapsed(false)}
                        className="rounded-full shadow-lg bg-background hover:bg-muted h-8 w-8" // Smaller size
                        title="Ouvrir l'assistant IA"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><line x1="15" y1="3" x2="15" y2="21"/></svg>
                        <span className="sr-only">Ouvrir Assistant IA</span>
                      </Button>
                    </div>
                  )}
              </TabsContent>

              {/* Merged Publication & Options Tab */}
              <TabsContent value="publication"> 
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Card for Distribution Info */}
                  <Card>
                     <CardHeader>
                      <CardTitle>Distribution & Publication</CardTitle>
                      <CardDescription>Informations relatives à la distribution et aux droits.</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                     <FormField
                      control={form.control}
                      name="album_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Album</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value || NO_ALBUM_SELECTED_VALUE}>
                            <FormControl>
                              <SelectTrigger disabled={isLoadingAlbums}>
                                <SelectValue placeholder={isLoadingAlbums ? "Chargement des albums..." : "Sélectionner un album (optionnel)"} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="bg-background border shadow-md"> {/* Added background */}
                              <SelectItem value={NO_ALBUM_SELECTED_VALUE}>Aucun album</SelectItem>
                              {albums.map((album) => (
                                <SelectItem key={album.id} value={album.id}>
                                  {album.title}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="release_date"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Date de sortie</FormLabel>
                          <DatePicker date={field.value ?? undefined} onSelect={(date: Date | undefined) => field.onChange(date)} />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="record_label"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Label</FormLabel>
                          <FormControl>
                            <Input placeholder="Mon Label Indépendant" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="distributor"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Distributeur</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: DistroKid, TuneCore" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="isrc"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ISRC</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: US-S1Z-99-00001" {...field} />
                          </FormControl>
                          <FormDescription>Code ISRC pour l'identification de l'enregistrement.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="upc"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>UPC/EAN</FormLabel>
                          <FormControl>
                            <Input placeholder="Ex: 123456789012" {...field} />
                          </FormControl>
                          <FormDescription>Code-barres du produit.</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                    </CardContent>
                  </Card>
                  {/* Card for Publication Options */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Options de Publication</CardTitle> {/* Kept title */}
                      <CardDescription>Gérez les options de votre morceau.</CardDescription> {/* Kept description */}
                    </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Improved Switch styling */}
                    <FormField control={form.control} name="is_explicit" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Contenu Explicite</FormLabel> <FormDescription>Indique si le morceau contient des paroles ou thèmes explicites.</FormDescription> </div> <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl> </FormItem> )}/>
                    <FormField control={form.control} name="stems_available" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Stems Disponibles</FormLabel> <FormDescription>Pistes instrumentales séparées disponibles ?</FormDescription> </div> <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl> </FormItem> )}/>
                    <FormField control={form.control} name="allow_downloads" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Autoriser Téléchargements</FormLabel> <FormDescription>Permettre aux utilisateurs de télécharger le fichier audio.</FormDescription> </div> <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl> </FormItem> )}/>
                    <FormField control={form.control} name="allow_comments" render={({ field }) => ( <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm bg-card hover:bg-muted/50 transition-colors"> <div className="space-y-0.5 pr-4"> <FormLabel className="text-base">Autoriser Commentaires</FormLabel> <FormDescription>Permettre aux utilisateurs de commenter ce morceau.</FormDescription> </div> <FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl> </FormItem> )}/>
                  </CardContent>
                </Card>
                 {/* Card for Notepads */}
                <Card>
                  <CardHeader>
                    <CardTitle>Bloc-notes</CardTitle>
                    <CardDescription>Espace pour vos notes privées sur ce morceau.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="bloc_note"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bloc-notes principal</FormLabel>
                          <FormControl>
                            <ScrollArea className="h-40 w-full rounded-md border p-2">
                              <Textarea
                                placeholder="Notes sur la composition, l'enregistrement, idées..."
                                className="min-h-[120px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-1"
                                {...field}
                              />
                            </ScrollArea>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="right_column_notepad"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bloc-notes (colonne de droite)</FormLabel>
                          <FormControl>
                            <ScrollArea className="h-40 w-full rounded-md border p-2">
                              <Textarea
                                placeholder="Notes additionnelles, visibles dans la colonne de droite de la page du morceau."
                                className="min-h-[120px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-1"
                                {...field}
                              />
                            </ScrollArea>
                          </FormControl>
                          <FormDescription>
                            Ce bloc-notes sera affiché dans la colonne de droite sur la page de détail du morceau (si le thème le supporte).
                          </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                         {/* Collapsible AI History Section */}
                         <Collapsible open={showAiHistory} onOpenChange={setShowAiHistory} className="mt-4">
                          <CollapsibleTrigger asChild>
                            <Button variant="ghost" size="sm" className="text-xs w-full justify-start text-muted-foreground hover:text-foreground">
                              <History className="mr-2 h-4 w-4" />
                              Historique Assistant IA ({Math.floor(aiHistory.length / 2)} interactions)
                              {showAiHistory ? <ChevronUp className="ml-auto h-4 w-4" /> : <ChevronDown className="ml-auto h-4 w-4" />}
                            </Button>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="mt-2">
                            <ScrollArea className="h-[200px] border rounded-md p-2 bg-muted/30">
                              {aiHistory.length === 0 ? (
                                <p className="text-xs text-muted-foreground text-center py-4">Aucun historique pour le moment.</p>
                              ) : (
                                <div className="space-y-3">
                                  {aiHistory.map((item, index) => (
                                    <div key={index} className={`text-xs p-2 rounded ${item.role === 'user' ? 'bg-blue-50 dark:bg-blue-900/30' : 'bg-gray-50 dark:bg-gray-800/30'}`}>
                                      <p className="font-semibold mb-1 capitalize">{item.role}:</p>
                                      <p className="whitespace-pre-wrap break-words">{item.content}</p>
                                      {item.timestamp && <p className="text-muted-foreground text-[10px] mt-1">{item.timestamp.toLocaleTimeString()}</p>}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </ScrollArea>
                          </CollapsibleContent>
                        </Collapsible>
                      </CardContent>
                    </Card>
                  </div>
            </TabsContent> 
            </Tabs>
          </div>
        </ScrollArea>
      </form>
    </FormProvider>
  );
}

export default SongForm;
