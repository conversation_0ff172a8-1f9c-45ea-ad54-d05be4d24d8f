# Modèle de données MOUVIK

## Vue d'ensemble

Le modèle de données de MOUVIK est conçu pour gérer efficacement les ressources musicales, les utilisateurs, et les interactions sociales. Il est implémenté dans une base de données PostgreSQL via Supabase.

## Diagramme entité-relation

```mermaid
erDiagram
    USERS ||--o{ SONGS : creates
    USERS ||--o{ ALBUMS : creates
    USERS ||--o{ PLAYLISTS : creates
    USERS ||--o{ COMMENTS : writes
    USERS ||--o{ LIKES : gives
    USERS ||--o{ FOLLOWS : follows
    SONGS ||--o{ ALBUM_SONGS : included_in
    ALBUMS ||--o{ ALBUM_SONGS : contains
    SONGS ||--o{ PLAYLIST_SONGS : included_in
    PLAYLISTS ||--o{ PLAYLIST_SONGS : contains
    SONGS ||--o{ COMMENTS : has
    ALBUMS ||--o{ COMMENTS : has
    SONGS ||--o{ LIKES : receives
    ALBUMS ||--o{ LIKES : receives
    SONGS ||--o{ RESOURCE_TAGS : has
    ALBUMS ||--o{ RESOURCE_TAGS : has
    TAGS ||--o{ RESOURCE_TAGS : used_in
    USERS ||--o{ ACTIVITIES : generates
    USERS ||--o{ PROFILES : has
    USERS ||--o{ BANDS : creates
    BANDS ||--o{ BAND_MEMBERS : has
    USERS ||--o{ BAND_MEMBERS : joins
    BANDS ||--o{ PROJECTS : has
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ CONVERSATIONS : participates
```

## Tables principales

Pour les définitions détaillées et les plus récentes des schémas de table (notamment pour `profiles`, `songs`, `albums`, et le système de tagging), veuillez vous référer au document [Database Schema Documentation](./database-schema.md). Ce document (`DATABASE.MD`) fournit une vue d'ensemble et un diagramme ER, tandis que `database-schema.md` contient les `CREATE TABLE` SQL à jour.

Les récentes mises à jour incluent une expansion significative de la table `profiles` et l'utilisation de champs de type tableau (par exemple `TEXT[]`) pour une meilleure catégorisation (genres, moods, etc.) dans les tables `songs` et `albums`.

### users
Table gérée par Supabase Auth contenant les informations d'authentification des utilisateurs. Référencée par la table `profiles`.

### profiles
Contient les informations étendues des utilisateurs. Voir `database-schema.md` pour la définition complète.

### songs
Stocke les informations sur les morceaux de musique. Voir `database-schema.md` pour la définition complète.

### albums
Stocke les informations sur les albums. Voir `database-schema.md` pour la définition complète.

### album_songs
Table de liaison pour la relation plusieurs-à-plusieurs entre albums et chansons.
```sql
CREATE TABLE public.album_songs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    album_id UUID REFERENCES public.albums(id) NOT NULL,
    song_id UUID REFERENCES public.songs(id) NOT NULL,
    track_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(album_id, song_id)
);
```

### playlists
Stocke les informations sur les playlists créées par les utilisateurs.
```sql
CREATE TABLE public.playlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    cover_url TEXT,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

### playlist_songs
Table de liaison pour la relation plusieurs-à-plusieurs entre playlists et chansons.
```sql
CREATE TABLE public.playlist_songs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    playlist_id UUID REFERENCES public.playlists(id) NOT NULL,
    song_id UUID REFERENCES public.songs(id) NOT NULL,
    position INTEGER NOT NULL,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(playlist_id, song_id)
);
```

## Tables sociales

### follows
```sql
CREATE TABLE public.follows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    follower_id UUID REFERENCES auth.users(id) NOT NULL,
    following_id UUID REFERENCES auth.users(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(follower_id, following_id),
    CHECK (follower_id != following_id)
);
```

### likes
```sql
CREATE TABLE public.likes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id, resource_type, resource_id)
);
```

### comments
```sql
CREATE TABLE public.comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id UUID NOT NULL,
    content TEXT NOT NULL,
    parent_id UUID REFERENCES public.comments(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

### activities
```sql
CREATE TABLE public.activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    activity_type TEXT NOT NULL,
    resource_type TEXT,
    resource_id UUID,
    content TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

## Tables de métadonnées

### tags
```sql
CREATE TABLE public.tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

### resource_tags
```sql
CREATE TABLE public.resource_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_type TEXT NOT NULL,
    resource_id UUID NOT NULL,
    tag_id UUID REFERENCES public.tags(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(resource_type, resource_id, tag_id)
);
```

## Tables de statistiques

### plays
```sql
CREATE TABLE public.plays (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    song_id UUID REFERENCES public.songs(id) NOT NULL,
    played_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    play_duration INTEGER,
    source TEXT
);
```

### views
```sql
CREATE TABLE public.views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    resource_type TEXT NOT NULL,
    resource_id UUID NOT NULL,
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

## Tables de collaboration

### bands
```sql
CREATE TABLE public.bands (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    cover_url TEXT,
    creator_id UUID REFERENCES auth.users(id) NOT NULL,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

### band_members
```sql
CREATE TABLE public.band_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    band_id UUID REFERENCES public.bands(id) NOT NULL,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    role TEXT NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(band_id, user_id)
);
```

### projects
```sql
CREATE TABLE public.projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    band_id UUID REFERENCES public.bands(id) NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'in_progress',
    due_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

## Tables de messagerie

### conversations
```sql
CREATE TABLE public.conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT,
    is_group BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

### conversation_participants
```sql
CREATE TABLE public.conversation_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES public.conversations(id) NOT NULL,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(conversation_id, user_id)
);
```

### messages
```sql
CREATE TABLE public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID REFERENCES public.conversations(id) NOT NULL,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    content TEXT NOT NULL,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

## Politiques de sécurité

Les politiques de sécurité Row Level Security (RLS) sont configurées pour chaque table afin de garantir que les utilisateurs ne peuvent accéder qu'aux données auxquelles ils sont autorisés.

Exemple pour la table `songs`:

```sql
-- Politique pour la lecture des chansons
CREATE POLICY "Users can view their own songs and published songs" ON public.songs
    FOR SELECT USING (
        auth.uid() = user_id OR
        status = 'published'
    );

-- Politique pour l'insertion des chansons
CREATE POLICY "Users can insert their own songs" ON public.songs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Politique pour la mise à jour des chansons
CREATE POLICY "Users can update their own songs" ON public.songs
    FOR UPDATE USING (auth.uid() = user_id);

-- Politique pour la suppression des chansons
CREATE POLICY "Users can delete their own songs" ON public.songs
    FOR DELETE USING (auth.uid() = user_id);
