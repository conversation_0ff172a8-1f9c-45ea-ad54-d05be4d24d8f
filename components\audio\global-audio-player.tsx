"use client"

import { useState, useRef, useEffect } from "react"
import { 
  <PERSON>, Pause, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>or<PERSON>, PlusCircle, ListMusic, Minimize2, Maximize2, VolumeX, Volume2, Eye, Heart, ChevronDown, ChevronUp
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>lider } from "@/components/ui/slider"
import { useAudio } from "@/contexts/audio-context"
import { cn } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { LikeButton } from "@/components/social/like-button"

export function GlobalAudioPlayer() {
  const { 
    currentSong, 
    isPlaying, 
    queue, 
    pauseSong, 
    resumeSong, 
    nextSong, 
    previousSong 
  } = useAudio()

  const [currentTime, setCurrentTime] = useState(() => 0)
  const [duration, setDuration] = useState(() => 0)
  const [volume, setVolume] = useState(() => 0.8)
  const [isMuted, setIsMuted] = useState(() => false)
  const [expanded, setExpanded] = useState(() => false) // For 'full' vs 'compact'
  const [minimized, setMinimized] = useState(() => false) // For 'compact' vs 'super-minimal'
  const [userId, setUserId] = useState<string | undefined>(undefined)
  const audioRef = useRef<HTMLAudioElement>(null)
  const animationRef = useRef<number>(0)

  useEffect(() => {
    let isMounted = true;
    import("@/lib/supabase/client").then(({ createBrowserClient }) => {
      const supabase = createBrowserClient();
      supabase.auth.getUser().then(({ data }) => {
        if (isMounted) setUserId(data.user?.id);
      });
    });
    return () => { isMounted = false };
  }, []);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume
    }
  }, [volume])

  useEffect(() => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.play().catch((error) => {
          console.error("Error playing audio:", error)
          pauseSong()
        })
        animationRef.current = requestAnimationFrame(updateProgress)
      } else {
        audioRef.current.pause()
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current)
        }
      }
    }
  }, [isPlaying, currentSong, pauseSong])

  useEffect(() => {
    if (audioRef.current) {
      const handleLoadedMetadata = () => {
        setDuration(audioRef.current?.duration || 0)
      }

      audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata)

      return () => {
        audioRef.current?.removeEventListener("loadedmetadata", handleLoadedMetadata)
      }
    }
  }, [currentSong])

  const togglePlayPause = () => {
    if (isPlaying) {
      pauseSong()
    } else {
      resumeSong()
    }
  }

  const updateProgress = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
      animationRef.current = requestAnimationFrame(updateProgress)
    }
  }

  const handleTimeChange = (value: number[]) => {
    if (audioRef.current) {
      audioRef.current.currentTime = value[0]
      setCurrentTime(value[0])
    }
  }

  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0]
    setVolume(newVolume)
    if (audioRef.current) {
      audioRef.current.volume = newVolume
    }
    if (newVolume === 0) {
      setIsMuted(true)
    } else {
      setIsMuted(false)
    }
  }

  const toggleMute = () => {
    if (isMuted) {
      setVolume(0.8)
      if (audioRef.current) {
        audioRef.current.volume = 0.8
      }
    } else {
      setVolume(0)
      if (audioRef.current) {
        audioRef.current.volume = 0
      }
    }
    setIsMuted(!isMuted)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, "0")}`
  }

  const handleEnded = () => {
    nextSong()
  }

  const toggleExpanded = () => { // Toggles between 'full' and 'compact'
    setExpanded(!expanded);
    if (!expanded) { // If moving to full view, ensure it's not also super-minimized
      setMinimized(false);
    }
  }

  const toggleMinimized = () => { // Toggles between 'compact' and 'super-minimal'
    setMinimized(!minimized);
    if (!minimized) { // If moving to super-minimal, ensure it's not also full-expanded
      setExpanded(false);
    }
  }

  if (!currentSong) {
    return null;
  }

  return (
    <div
      className={cn(
        "fixed bottom-0 left-0 right-0 px-4 py-2 flex flex-col gap-0 transition-all duration-300 border-t border-white/10 z-50 shadow-2xl player-bg",
        typeof window !== 'undefined' && document.body.classList.contains('sidebar-collapsed') ? 'ml-[var(--sidebar-width-icon)]' : 'ml-[var(--sidebar-width)]',
        expanded ? "h-56" : (minimized ? "h-14" : "h-24"), // Adjusted height logic
      )}
    >
      {/* Super-minimal view content */}
      {minimized && !expanded && (
        <div className="flex items-center justify-between h-full w-full">
          <div className="flex items-center gap-2 overflow-hidden">
            <div className="relative h-8 w-8 overflow-hidden flex-shrink-0 rounded">
              <Image
                src={currentSong.cover_url || "/placeholder.svg?height=32&width=32&query=album"}
                alt={currentSong.title}
                fill
                className="object-cover"
              />
            </div>
            <div className="flex flex-col min-w-0">
              <span className="text-xs font-medium hover:underline truncate block leading-tight text-white">
                {currentSong.title}
              </span>
              <span className="text-[10px] text-muted-foreground truncate block">
                {currentSong.artist || "Unknown Artist"}
              </span>
            </div>
          </div>
          <div className="flex items-center">
            <Button size="icon" variant="ghost" onClick={togglePlayPause} aria-label={isPlaying ? "Pause" : "Lecture"} className="text-primary hover:bg-primary/10 h-8 w-8">
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button size="icon" variant="ghost" onClick={toggleMinimized} aria-label="Agrandir le lecteur" className="text-primary hover:bg-primary/10 h-8 w-8">
              <ChevronUp className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Compact and Full view content (hidden if minimized) */}
      {!minimized && (
      <>
      <audio
        ref={audioRef}
        src={currentSong.audio_url || ""}
        onEnded={handleEnded}
        onTimeUpdate={() => setCurrentTime(audioRef.current?.currentTime || 0)}
      />
      {/* Ligne principale: pochette + slider + actions */}
      <div className="grid grid-cols-[auto_1fr_auto] items-center w-full gap-6 min-h-[72px]">
        {/* Pochette réduite */}
        <div className="relative h-16 w-16 overflow-hidden flex-shrink-0 rounded-md shadow-md">
          <Image
            src={currentSong.cover_url || "/placeholder.svg?height=64&width=64&query=album"}
            alt={currentSong.title}
            fill
            className="object-cover"
          />
        </div>
        {/* Bloc central : titre, artiste, stats, contrôles */}
        <div className="flex flex-col justify-center min-w-0">
          <div className="flex flex-row items-center gap-4 w-full">
            <div className="flex flex-col min-w-0 max-w-[220px]">
              <Link href={`/songs/${currentSong.id}`} className="text-base font-bold hover:underline truncate block leading-tight">
                {currentSong.title}
              </Link>
              <Link href={`/artists/${currentSong.user_id}`} className="text-xs text-muted-foreground hover:underline truncate">
                {currentSong.artist || "Unknown Artist"}
              </Link>
              <div className="flex items-center gap-3 mt-1 text-xs text-zinc-400">
                <span className="flex items-center gap-1"><Play className="h-3 w-3" />{(currentSong as any).play_count ?? 0}</span>
                <span className="flex items-center gap-1"><Eye className="h-3 w-3" />{(currentSong as any).view_count ?? 0}</span>
              </div>
            </div>
            {/* Contrôles principaux */}
            <div className="flex flex-col items-center justify-center flex-1">
              <div className="flex items-center gap-2 mb-1">
                <Button size="icon" variant="ghost" onClick={previousSong} aria-label="Précédent" className="text-primary hover:bg-primary/10">
                  <SkipBack className="h-4 w-4" />
                </Button>
                <Button size="icon" variant="default" onClick={togglePlayPause} aria-label={isPlaying ? "Pause" : "Lecture"} className="bg-primary text-white shadow-lg hover:bg-primary/90">
                  {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
                </Button>
                <Button size="icon" variant="ghost" onClick={nextSong} aria-label="Suivant" className="text-primary hover:bg-primary/10">
                  <SkipForward className="h-4 w-4" />
                </Button>
                <Button size="icon" variant="ghost" aria-label="Ajouter à la playlist" className="text-primary hover:bg-primary/10">
                  <PlusCircle className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          {/* Playbar visible et accessible */}
          <div className="flex items-center w-full max-w-3xl mx-auto px-2 py-2 justify-center">
            <span className="text-xs text-zinc-400 w-10 text-right select-none">{formatTime(currentTime)}</span>
            <Slider
              value={[currentTime]}
              min={0}
              max={duration}
              step={1}
              onValueChange={handleTimeChange}
              className="flex-1 mx-6 player-slider cursor-pointer"
            />
            <span className="text-xs text-zinc-400 w-10 select-none">{formatTime(duration)}</span>
          </div>
        </div>
        {/* Droite : actions */}
        <div className="flex flex-row items-center gap-1 min-w-0">
          {!expanded && ( // Show minimize to super-small only in compact view
            <Button size="icon" variant="ghost" onClick={toggleMinimized} aria-label="Réduire davantage" className="text-primary hover:bg-primary/10">
              <ChevronDown className="h-4 w-4" />
            </Button>
          )}
          <LikeButton
            resourceId={currentSong.id}
            resourceType="song"
            initialLikes={(currentSong as any).likes || 0}
            userId={userId}
            size="sm"
          />
          <Button size="icon" variant="ghost" aria-label="Voir la queue" className="text-primary hover:bg-primary/10">
            <ListMusic className="h-4 w-4" />
          </Button>
          <Button size="icon" variant="ghost" onClick={toggleExpanded} aria-label={expanded ? "Réduire" : "Détails"} className="text-primary hover:bg-primary/10">
            {expanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
          <div className="hidden sm:flex items-center gap-1">
            <Button size="icon" variant="ghost" onClick={toggleMute} aria-label={isMuted ? "Activer le son" : "Couper le son"} className="text-primary hover:bg-primary/10">
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
            <Slider
              value={[Math.round(volume * 100)]}
              min={0}
              max={100}
              step={1}
              onValueChange={([v]) => handleVolumeChange([v/100])}
              className="w-14 player-slider cursor-pointer"
            />
          </div>
        </div>
      </div>
      
      {/* Vue étendue (only if not minimized) */}
      {expanded && !minimized && (
        <div className="mt-2 flex flex-col lg:flex-row gap-6 text-xs text-zinc-400">
          {/* Détails et crédits */}
          <div className="flex-1 min-w-0">
            <div className="font-semibold text-base text-white mb-1">Détails du morceau</div>
            <div className="mb-1">Album : <span className="text-white font-medium">{(currentSong as any).album || '—'}</span></div>
            <div className="mb-1">Année : <span className="text-white font-medium">{(currentSong as any).year || '—'}</span></div>
            <div className="mb-1">Genre : <span className="text-white font-medium">{(currentSong as any).genre || '—'}</span></div>
            <div className="mb-1">Crédits : <span className="text-white font-medium">{(currentSong as any).credits || '—'}</span></div>
          </div>
          {/* Waveform visuel si dispo */}
          {currentSong.waveform_url && (
            <div className="flex-1 flex items-center justify-center">
              <img src={currentSong.waveform_url} alt="Waveform" className="w-full h-16 object-contain opacity-80" />
            </div>
          )}
        </div>
      )}
      </>
      )} {/* End of !minimized conditional rendering block */}
    </div>
  );
}
