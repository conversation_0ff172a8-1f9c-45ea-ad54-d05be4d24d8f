@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* --- PLAYER OVERRIDES --- */
  .player-bg {
    background: #000 !important;
    background-color: #000 !important;
    box-shadow: 0 0 32px 0 #000d;
    border-top: 1px solid rgba(255,255,255,0.10);
    z-index: 99;
  }
  .player-slider {
    background: #181818 !important;
    border-radius: 9999px;
    height: 1.2rem !important;
  }
  .player-slider .slider-thumb {
    background: #00fff0 !important;
    border: 3px solid #fff !important;
    box-shadow: 0 0 12px 2px #00fff088 !important;
    width: 1.6rem !important;
    height: 1.6rem !important;
    transition: transform 0.18s;
  }
  .player-slider .slider-thumb:hover {
    transform: scale(1.18);
  }
  .player-slider .slider-track {
    background: #222 !important;
  }
  .text-balance {
    text-wrap: balance;
  }
  /* Classe utilitaire pour effet frosted glass */
  .frosted {
    background: rgba(255, 255, 255, 0.18); /* Ajuste selon la palette extraite */
    border-radius: 1rem;
    border: 1px solid rgba(255,255,255,0.25);
    box-shadow: 0 4px 32px 0 rgba(31, 38, 135, 0.10);
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    /* Optionnel : transition douce */
    transition: background 0.3s, box-shadow 0.3s;
  }
  /* --- MOUVIK DESIGN UTILITIES --- */
  :root {
    --primary-teal: #00CED1;
    --dark-teal: #004a4b;
    --darker-teal: #002a2b;
    --darkest-bg: #001214;
    --glow-effect: 0 0 8px rgba(0, 206, 209, 0.5);
    /* Variables de la Sidebar ajoutées */
    --sidebar-width: 16rem; /* valeur par défaut pour la sidebar étendue */
    --sidebar-width-icon: 3rem; /* valeur pour la sidebar réduite (icônes) */
  }
  .gradient-text {
    background: linear-gradient(90deg, var(--primary-teal), #4FFFB0);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .teal-glow {
    text-shadow: var(--glow-effect);
  }
  .teal-glow-box {
    box-shadow: var(--glow-effect);
  }
  .bg-glass {
    background: rgba(0, 18, 24, 0.7);
    border: 1px solid rgba(0, 206, 209, 0.2);
    backdrop-filter: blur(8px);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 180 100% 30%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 180 100% 30%;
    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 180 100% 40%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 180 100% 40%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    position: relative;
    min-height: 100vh;
    @apply bg-background text-foreground;
  }
  body::before {
    content: '';
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    z-index: -1;
    background: url('/bg1.png') center center/cover no-repeat;
    filter: blur(16px) brightness(1.08) saturate(1.1);
    opacity: 0.93;
    pointer-events: none;
  }
}

/* Classe utilitaire pour effet frosted glass */
.frosted {
  background: rgba(24, 28, 35, 0.35); /* Frost noir doux, + opacité */
  border-radius: 1rem;
  border: 1px solid rgba(255,255,255,0.13);
  box-shadow: 0 4px 32px 0 rgba(31, 38, 135, 0.07);
  backdrop-filter: blur(16px) saturate(120%);
  -webkit-backdrop-filter: blur(16px) saturate(120%);
  transition: background 0.3s, box-shadow 0.3s;
}

.card, .Card, .bg-background, .bg-card, .bg-popover, .bg-secondary, .bg-muted, .bg-accent, .bg-destructive { /* Removed .bg-primary from this rule */
  background: rgba(24, 28, 35, 0.38) !important; /* Noir frosté subtil */
  box-shadow: none !important;
  border-color: rgba(255,255,255,0.09) !important;
}

.frosted *[class*="bg-"],
.card *[class*="bg-"],
.Card *[class*="bg-"],
.bg-background *[class*="bg-"],
.bg-card *[class*="bg-"],
.bg-popover *[class*="bg-"],
.bg-secondary *[class*="bg-"],
.bg-muted *[class*="bg-"],
.bg-accent *[class*="bg-"],
.bg-primary *[class*="bg-"],
.bg-destructive *[class*="bg-"] {
  background: rgba(24, 28, 35, 0.22) !important; /* Un peu de noir, mais translucide */
}

/* React Quill Customization */
@layer components {
  /* Main wrapper for Quill (styled via className prop in the component) */
  /* This will already get the frosted background due to global .bg-white overrides */

  /* Toolbar */
  .ql-toolbar.ql-snow {
    @apply border-b-0 rounded-t-md p-2;
    border-color: rgba(255,255,255,0.09) !important; 
  }

  /* Toolbar buttons and icons */
  .ql-snow .ql-formats button {
    @apply text-foreground hover:bg-[hsla(var(--foreground)/0.05)] active:bg-[hsla(var(--foreground)/0.1)] rounded-sm;
    transition: background-color 0.2s;
    width: 28px;
    height: 28px;
    padding: 4px;
  }
  .ql-snow .ql-formats button svg {
    @apply w-full h-full;
  }

  .ql-snow .ql-stroke,
  .ql-snow .ql-picker-label .ql-stroke {
    stroke: hsl(var(--foreground));
  }
  .ql-snow .ql-fill,
  .ql-snow .ql-picker-label .ql-fill {
    fill: hsl(var(--foreground));
  }
  
  .ql-snow button.ql-active,
  .ql-snow .ql-picker-label.ql-active,
  .ql-snow .ql-picker-item.ql-selected {
    @apply bg-primary text-primary-foreground;
  }
  .ql-snow button.ql-active .ql-stroke,
  .ql-snow .ql-picker-label.ql-active .ql-stroke,
  .ql-snow .ql-picker-item.ql-selected .ql-stroke {
    stroke: hsl(var(--primary-foreground));
  }
   .ql-snow button.ql-active .ql-fill,
  .ql-snow .ql-picker-label.ql-active .ql-fill,
  .ql-snow .ql-picker-item.ql-selected .ql-fill {
    fill: hsl(var(--primary-foreground));
  }

  /* Dropdowns in Toolbar (Font, Size, Header etc.) */
  .ql-snow .ql-picker-label {
    @apply text-foreground hover:bg-[hsla(var(--foreground)/0.05)] rounded-sm pl-2 pr-1;
  }
  .ql-snow .ql-picker-options {
    @apply rounded-md shadow-lg border-none p-1;
    background-color: hsl(var(--popover)) !important; 
    border-color: rgba(255,255,255,0.09) !important; 
  }
  .ql-snow .ql-picker-item {
    @apply text-foreground hover:bg-[hsla(var(--foreground)/0.05)] rounded-sm p-1.5;
  }
  
  /* Editor Container (holds the .ql-editor) */
  .ql-container.ql-snow {
    @apply border-t-0 rounded-b-md;
    border-color: rgba(255,255,255,0.09) !important;
  }

  /* Editor Area (where text is typed) */
  .ql-editor {
    @apply text-foreground min-h-[180px] p-4;
    caret-color: hsl(var(--primary));
    line-height: 1.6;
  }
  .ql-editor.ql-blank::before {
    color: hsl(var(--muted-foreground));
    font-style: normal !important;
    left: 1rem;
    top: 1rem;
  }
  .ql-editor p, .ql-editor ol, .ql-editor ul, .ql-editor pre, .ql-editor blockquote, .ql-editor h1, .ql-editor h2, .ql-editor h3 {
    @apply mb-3;
  }

  /* Link Tooltip */
  .ql-snow .ql-tooltip {
    @apply rounded-md shadow-lg p-2 border-none;
    background-color: hsl(var(--popover)) !important;
    color: hsl(var(--popover-foreground));
    backdrop-filter: blur(12px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.2) !important;
  }
  .ql-snow .ql-tooltip input[type="text"] {
    @apply bg-input text-foreground border-border rounded-sm p-1.5 w-full;
  }
  .ql-snow .ql-tooltip a.ql-preview {
    @apply text-primary underline;
  }
  .ql-snow .ql-tooltip .ql-action, .ql-snow .ql-tooltip .ql-remove {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 px-3 py-1.5 ml-2;
  }
  .ql-snow .ql-tooltip .ql-action {
    @apply bg-primary text-primary-foreground;
  }
  .ql-snow .ql-tooltip .ql-remove {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90;
  }
}

/* Custom style for AI Config Popover Content */
.ai-config-popover-content {
  /* More opaque background than default frosted popovers */
  background-color: rgba(30, 35, 45, 0.85) !important; /* Darker, more opaque base */
  /* Ensure text color has good contrast */
  color: hsl(var(--popover-foreground)) !important; 
  /* Slightly more defined border to help it stand out */
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  /* Keep existing backdrop filter for consistency */
  backdrop-filter: blur(12px) saturate(100%); /* Slightly less saturate/blur if needed to reduce transparency impact */
  -webkit-backdrop-filter: blur(12px) saturate(100%);
  box-shadow: 0 6px 24px rgba(0,0,0,0.25) !important; /* Slightly stronger shadow */
}
