"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { createBrowserClient } from '@supabase/ssr'; 
import { SupabaseClient } from '@supabase/supabase-js'; 
import { useToast } from "@/hooks/use-toast"; 

// Using relative path to rule out alias issues
import Link from 'next/link'; // Import Link
import SongForm, { SongFormValues } from '../../../../../components/songs/SongForm'; 
import { Button } from "@/components/ui/button";
import { Loader2 as LoadingSpinner, ExternalLink } from 'lucide-react'; // Import ExternalLink

interface Album {
  id: string;
  title: string;
}

export default function EditSongPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { toast } = useToast();

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const [songData, setSongData] = useState<Partial<SongFormValues> | null>(null);
  const [isLoadingSongData, setIsLoadingSongData] = useState(true);
  const [userAlbums, setUserAlbums] = useState<Album[]>([]);
  const [isLoadingAlbums, setIsLoadingAlbums] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchSong = async () => {
      if (!params?.id) {
        toast({ title: 'Erreur', description: 'ID de la chanson manquant.', variant: 'destructive' });
        setIsLoadingSongData(false);
        return;
      }
      setIsLoadingSongData(true);
      const { data, error } = await supabase.from('songs').select('*').eq('id', params.id).single();
      if (error) {
        toast({ title: 'Erreur de chargement', description: `Impossible de charger la chanson: ${error.message}`, variant: 'destructive' });
        setSongData(null);
      } else if (data) {
        // Map DB data to SongFormValues for initialValues
        setSongData({
          id: data.id,
          title: data.title,
          artist: data.artist,
          duration_ms: data.duration_ms,
          genre: data.genre, // single string
          subgenre: data.subgenre,
          mood: data.mood, // single string
          theme: data.theme,
          instrumentation: data.instrumentation || [], // ensure array
          bpm: data.bpm,
          key: data.key,
          audio_url: data.audio_url,
          cover_art_url: data.cover_art_url,
          lyrics: data.lyrics,
          credits: data.credits || {}, // ensure object
          tags: data.tags || [], // ensure array
          release_date: data.release_date ? new Date(data.release_date) : null,
          is_public: !!data.is_public,
          is_explicit: !!data.is_explicit,
          visibility: data.visibility || 'private',
          editor_data: data.editor_data || {}, // ensure object
          is_archived: !!data.is_archived,
          album_id: data.album_id,
          // The following fields from the old SongForm are not in the new schema or are handled by credits/editor_data:
          // tempo, capo, tuning_frequency, featured_artists (string), genres (array), moods (array), 
          // writers (string), producers (string), language, lyrics_language, recording_date, 
          // is_ai_generated, stems_available, allow_downloads, allow_comments, status, bloc_note, right_column_notepad, etc.
          // These should be populated from `data.credits` or `data.editor_data` within SongForm's useEffect
        });
      }
      setIsLoadingSongData(false);
    };
    fetchSong();
  }, [params.id, supabase, toast]);

  useEffect(() => {
    const fetchAlbums = async () => {
      setIsLoadingAlbums(true);
      const { data: userSessionData, error: sessionError } = await supabase.auth.getUser();
      if (sessionError || !userSessionData?.user) {
        setIsLoadingAlbums(false);
        return;
      }
      const userId = userSessionData.user.id;
      const { data, error } = await supabase.from('albums').select('id, title').eq('user_id', userId);
      if (error) {
        console.error('Error fetching user albums:', error);
        toast({ title: "Erreur chargement albums", description: error.message, variant: "destructive" });
      } else {
        setUserAlbums(data || []);
      }
      setIsLoadingAlbums(false);
    };
    fetchAlbums();
  }, [supabase, toast]);
  
  const handleFormSubmit = async (data: SongFormValues, status?: 'draft' | 'published') => {
    if (!params?.id) return;
    setIsSubmitting(true);

    // The 'data' object from SongForm already has the new structure.
    // Specific credit fields like featured_artists, writers, etc., are now part of data.credits object if populated from SongForm.

    const {
      // Destructure individual credit fields that were part of SongFormValues for building credits JSON
      featured_artists, composer_name, writers, producers, record_label, isrc, upc,
      // Destructure the main 'credits' field which is the advanced JSON input
      credits: advancedCreditsInput,
      // Destructure other fields
      ...restOfData
    } = data;

    let combinedCredits: Record<string, any> = {};

    // Populate from individual string fields that were part of SongForm, converting comma-separated to arrays if applicable
    if (featured_artists) combinedCredits.featured_artists = featured_artists.split(',').map(s => s.trim()).filter(s => s);
    if (composer_name) combinedCredits.composer_name = composer_name;
    if (writers) combinedCredits.writers = writers.split(',').map(s => s.trim()).filter(s => s);
    if (producers) combinedCredits.producers = producers.split(',').map(s => s.trim()).filter(s => s);
    if (record_label) combinedCredits.record_label = record_label;
    if (isrc) combinedCredits.isrc = isrc;
    if (upc) combinedCredits.upc = upc;

    // Merge with advanced credits input (assuming it's an object from SongForm)
    if (advancedCreditsInput && typeof advancedCreditsInput === 'object') {
      combinedCredits = { ...combinedCredits, ...advancedCreditsInput };
    }
    
    let editorDataObject = null;
    if (restOfData.editor_data) {
      if (typeof restOfData.editor_data === 'string') {
        try {
          if (restOfData.editor_data.trim() === "") editorDataObject = null;
          else editorDataObject = JSON.parse(restOfData.editor_data);
        } catch (e) {
          console.error("Error parsing editor_data JSON for update:", e);
          toast({ title: "Erreur JSON Données Éditeur", description: "Format JSON invalide.", variant: "destructive" });
          setIsSubmitting(false); return;
        }
      } else if (typeof restOfData.editor_data === 'object') {
        editorDataObject = restOfData.editor_data;
      }
    }

    const songDataToUpdate = {
      title: restOfData.title,
      artist: restOfData.artist,
      duration_ms: restOfData.duration_ms,
      genre: restOfData.genre,
      subgenre: restOfData.subgenre,
      mood: restOfData.mood,
      theme: restOfData.theme,
      instrumentation: restOfData.instrumentation,
      bpm: restOfData.bpm ? Number(restOfData.bpm) : null,
      key: restOfData.key,
      audio_url: restOfData.audio_url,
      cover_art_url: restOfData.cover_art_url,
      lyrics: restOfData.lyrics,
      credits: Object.keys(combinedCredits).length > 0 ? combinedCredits : null,
      tags: restOfData.tags,
      release_date: restOfData.release_date ? new Date(restOfData.release_date).toISOString().split('T')[0] : null,
      is_public: restOfData.is_public,
      is_explicit: restOfData.is_explicit,
      visibility: restOfData.visibility,
      editor_data: editorDataObject,
      is_archived: restOfData.is_archived,
      album_id: restOfData.album_id === "__NO_ALBUM__" ? null : restOfData.album_id,
      description: restOfData.description, // Added description
      // creator_user_id and id should not be in the update payload.
      // updated_at is handled by the database.
    };
    
    // Whitelist of columns from the live public.songs table
    const SONGS_TABLE_COLUMNS = [
      'album_id', 'band_id', 'title', 'artist', 'duration_ms', 'genre', 'subgenre', 
      'mood', 'theme', 'instrumentation', 'bpm', 'key', 'audio_url', 'cover_art_url', 
      'lyrics', 'credits', 'tags', 'release_date', 'is_public', 'is_explicit', 
      'visibility', 'editor_data', 'is_archived', 'description' // 'id', 'creator_user_id', 'created_at', 'updated_at' are usually not updated directly.
    ];
     
    // Filter the songDataToUpdate based on the whitelist
     const filteredData = Object.fromEntries(
       Object.entries(songDataToUpdate).filter(([key]) => SONGS_TABLE_COLUMNS.includes(key))
     );

    const filteredData = Object.fromEntries(
      Object.entries(songDataToUpdate).filter(([key]) => SONGS_TABLE_COLUMNS.includes(key))
    );

    // Remove only undefined values. Null is a valid value for update.
    const cleanedPayload = Object.fromEntries(
      Object.entries(filteredData).filter(([_, value]) => value !== undefined)
    );
    
    console.log("[Edit Submit] Final Cleaned Payload for Update:", cleanedPayload); 
    
    try {
      const { data: updatedSong, error } = await supabase
        .from('songs')
        .update(cleanedPayload) 
        .eq('id', params.id)
        .select()
        .single();

      if (error) {
        console.error('[Edit Submit] Error updating song in DB:', error);
        toast({ title: "Erreur DB Mise à Jour", description: error.message, variant: "destructive" });
      } else {
        console.log("[Edit Submit] Song updated successfully in DB:", updatedSong);
        toast({ title: "Chanson Mise à Jour!", description: `"${updatedSong.title}" a été modifiée avec succès.` });
        // Optionally, refetch song data to update the form with potentially processed/validated data from DB
        // Or rely on SongForm's internal state if it's sufficient
      }
    } catch (e: any) {
      console.error('[Edit Submit] Unexpected error during DB update:', e);
      toast({ title: "Erreur Inattendue Mise à Jour", description: e.message, variant: "destructive" });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingSongData || !songData && params.id) { 
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner className="h-8 w-8 animate-spin" />
        <p className="ml-2">Chargement des données de la chanson...</p>
      </div>
    );
  }

  if (!songData && !params.id) { 
    return <div className="text-center py-10">ID de la chanson non spécifié.</div>;
  }

  if (!songData) { 
     return <div className="text-center py-10">Chanson non trouvée ou erreur de chargement.</div>;
  }

  return (
    // Changed container to w-full and updated padding for full-width responsive layout
    <div className="w-full px-2 sm:px-6 lg:px-12 py-8">
      <header className="mb-8">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white">
          Modifier: {songData?.title || 'le morceau'}
        </h1>
        <p className="mt-2 text-lg text-gray-600 dark:text-gray-400">
          Modifiez les informations de votre création.
        </p>
         {/* Add Preview Link */}
         <div className="mb-6">
           <Link href={`/songs/${params.id}`} target="_blank" rel="noopener noreferrer">
             <Button variant="outline" size="sm">
               Prévisualiser la page publique
               <ExternalLink className="ml-2 h-4 w-4" />
             </Button>
           </Link>
         </div>
      </header>
      <SongForm
        mode="edit"
        initialValues={songData} 
        onFormSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
        albums={userAlbums}
        isLoadingAlbums={isLoadingAlbums}
        supabaseClient={supabase as SupabaseClient} 
      />
    </div>
  );
}
