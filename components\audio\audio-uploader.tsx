"use client"

import type React from "react"

import { useState, useRef } from "react"
import { Upload, X, Music } from "lucide-react"
import { getSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"

interface AudioUploaderProps {
  onAudioUploaded: (url: string, waveformUrl: string, duration: number) => void
  existingAudioUrl?: string
  bucketName?: string
}

export function AudioUploader({ onAudioUploaded, existingAudioUrl, bucketName = "audio" }: AudioUploaderProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [audioUrl, setAudioUrl] = useState(existingAudioUrl || "")
  const [fileName, setFileName] = useState("")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  const { toast } = useToast()

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Vérifier le type de fichier
    if (!file.type.startsWith("audio/")) {
      toast({
        title: "Type de fichier non pris en charge",
        description: "Veuillez sélectionner un fichier audio (MP3, WAV, etc.)",
        variant: "destructive",
      })
      return
    }

    // Vérifier la taille du fichier (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      toast({
        title: "Fichier trop volumineux",
        description: "La taille du fichier ne doit pas dépasser 50MB",
        variant: "destructive",
      })
      return
    }

    setIsUploading(true)
    setFileName(file.name)
    setUploadProgress(0)

    try {
      const supabase = getSupabaseClient()
      const fileExt = file.name.split(".").pop()
      const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`
      const filePath = `${fileName}`

      // Créer un objet URL pour le fichier
      const objectUrl = URL.createObjectURL(file)

      // Obtenir la durée du fichier audio
      const audio = new Audio(objectUrl)

      audio.addEventListener("loadedmetadata", async () => {
        const duration = audio.duration

        // Télécharger le fichier audio
        const { data, error } = await supabase.storage.from(bucketName).upload(filePath, file, {
          cacheControl: "3600",
          upsert: true,
        })

        if (error) {
          throw error
        }

        // Obtenir l'URL publique
        const { data: publicUrlData } = supabase.storage.from(bucketName).getPublicUrl(filePath)
        const audioUrl = publicUrlData.publicUrl

        // Générer une URL de forme d'onde (simulée pour l'instant)
        const waveformUrl = ""

        setAudioUrl(audioUrl)
        onAudioUploaded(audioUrl, waveformUrl, duration)

        toast({
          title: "Fichier audio téléchargé",
          description: "Votre fichier audio a été téléchargé avec succès",
        })
      })

      audio.addEventListener("error", () => {
        toast({
          title: "Erreur de lecture du fichier",
          description: "Impossible de lire le fichier audio",
          variant: "destructive",
        })
        setIsUploading(false)
      })
    } catch (error: any) {
      toast({
        title: "Erreur de téléchargement",
        description: error.message || "Une erreur s'est produite lors du téléchargement du fichier",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveAudio = () => {
    setAudioUrl("")
    setFileName("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
    onAudioUploaded("", "", 0)
  }

  return (
    <div className="space-y-4">
      {!audioUrl ? (
        <div
          className={`border-2 border-dashed rounded-md p-6 text-center ${
            isUploading ? "border-primary" : "border-muted-foreground/25"
          }`}
        >
          <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" accept="audio/*" />
          <div className="flex flex-col items-center justify-center space-y-2">
            <div className="rounded-full bg-primary/10 p-3">
              <Upload className="h-6 w-6 text-primary" />
            </div>
            <div className="text-sm font-medium">
              {isUploading ? (
                <div className="flex flex-col items-center">
                  <div className="mb-2">Téléchargement en cours...</div>
                  <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: `${uploadProgress}%` }}></div>
                  </div>
                  <div className="mt-1 text-xs text-muted-foreground">{uploadProgress}%</div>
                </div>
              ) : (
                <>
                  <span className="text-primary">Cliquez pour télécharger</span> ou glissez-déposez
                  <p className="text-xs text-muted-foreground mt-1">MP3, WAV, FLAC (max 50MB)</p>
                </>
              )}
            </div>
          </div>
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="mt-4"
          >
            Sélectionner un fichier
          </Button>
        </div>
      ) : (
        <div className="border rounded-md p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                <Music className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="font-medium">{fileName || "Fichier audio"}</div>
                <audio ref={audioRef} src={audioUrl} className="hidden" />
                <div className="text-xs text-muted-foreground">
                  <a href={audioUrl} target="_blank" rel="noopener noreferrer" className="hover:underline">
                    Écouter
                  </a>
                </div>
              </div>
            </div>
            <Button variant="ghost" size="icon" onClick={handleRemoveAudio}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
