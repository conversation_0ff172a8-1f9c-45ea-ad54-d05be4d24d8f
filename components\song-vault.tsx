'use client'

import React, { useCallback, useState, useEffect, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
// import WaveSurfer from 'wavesurfer.js' // Keep commented for now
import { SupabaseClient } from '@supabase/supabase-js'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { FileAudio, Trash2, UploadCloud, Eye, EyeOff } from 'lucide-react'
import { AudioSliderPlayer, randomColorFromString } from './audio-slider-player' // Ensure randomColorFromString is imported
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from '@/components/ui/collapsible'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'

// Local randomColorFromString removed to use the imported one

interface VaultItem {
  id: string;
  db_id?: string;
  file?: File;
  fileName: string;
  storage_path?: string;
  previewUrl: string;
  annotations: string;
  color?: string;
  is_public: boolean;
  file_size?: number;
  mime_type?: string;
  isPlaying?: boolean;
  waveformError?: string;
  isUploading?: boolean;
}

export interface SongVaultActions {
  savePendingItems: (songIdToLink: string, userIdToLink: string) => Promise<void>;
}

interface SongVaultProps {
  songId?: string;
  userId?: string;
  supabaseClient: SupabaseClient;
  onReady?: (actions: SongVaultActions) => void;
}

const MAX_FILES_GRATUIT = 5;
const MAX_TOTAL_SIZE_GRATUIT = 50 * 1024 * 1024; // 50MB

export default function SongVault({ songId, userId, supabaseClient, onReady }: SongVaultProps) {
  const [vaultItems, setVaultItems] = useState<VaultItem[]>([]);
  const [totalSize, setTotalSize] = useState(0);

  const savePendingItems = useCallback(async (songIdToLink: string, userIdToLink: string) => {
    const pendingItems = vaultItems.filter(item => item.file && !item.db_id);
    if (pendingItems.length === 0) return;

    toast({ title: "Sauvegarde du vault...", description: `Téléversement de ${pendingItems.length} fichier(s) en cours.`});

    for (const item of pendingItems) {
      if (item.file && !item.waveformError) { 
        setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, isUploading: true } : i));
        const filePathInStorage = `public/${userIdToLink}/${songIdToLink}/${crypto.randomUUID()}-${item.fileName}`;
        try {
          const { error: uploadError } = await supabaseClient.storage
            .from('song-audio-vault')
            .upload(filePathInStorage, item.file, { cacheControl: '3600', upsert: false });
          if (uploadError) throw uploadError;

          const { data: publicUrlData } = supabaseClient.storage.from('song-audio-vault').getPublicUrl(filePathInStorage);
          const newDbRecordPayload = {
            song_id: songIdToLink, user_id: userIdToLink, file_name: item.fileName, storage_path: filePathInStorage,
            annotations: item.annotations, color: item.color, is_public: item.is_public,
            file_size: item.file_size, mime_type: item.mime_type,
          };
          const { data: insertedRecord, error: insertError } = await supabaseClient
            .from('song_vault_items').insert(newDbRecordPayload).select().single();
          if (insertError) throw insertError;

          setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, db_id: insertedRecord.id, storage_path: filePathInStorage, previewUrl: publicUrlData.publicUrl || i.previewUrl, isUploading: false, file: undefined } : i));
        } catch (error: any) {
          console.error(`Error saving pending vault item ${item.fileName}:`, error);
          toast({ title: `Erreur avec ${item.fileName}`, description: error.message, variant: "destructive" });
          setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, isUploading: false, waveformError: "Échec du téléversement différé" } : i));
        }
      }
    }
    toast({ title: "Vault sauvegardé!", description: "Tous les fichiers en attente ont été traités."});
  }, [vaultItems, supabaseClient]); 

  useEffect(() => {
    if (onReady) {
      onReady({ savePendingItems });
    }
  }, [onReady, savePendingItems]);

  useEffect(() => {
    const fetchVaultItems = async () => {
      if (!songId || !userId) {
        setVaultItems([]); // Clear if no songId, for fresh create state
        setTotalSize(0);
        return;
      }
      try {
        const { data, error } = await supabaseClient
          .from('song_vault_items')
          .select('*')
          .eq('song_id', songId)
          .eq('user_id', userId)
          .order('created_at', { ascending: true });

        if (error) {
          console.error('Error fetching vault items:', error);
          toast({ title: "Erreur de chargement du vault", description: error.message, variant: "destructive" });
          setVaultItems([]);
          setTotalSize(0);
          return;
        }
        if (data) {
          const fetchedItems: VaultItem[] = data.map(dbItem => {
            const { data: publicUrlData } = supabaseClient.storage
              .from('song-audio-vault')
              .getPublicUrl(dbItem.storage_path);
            return {
              id: dbItem.id, 
              db_id: dbItem.id,
              fileName: dbItem.file_name,
              storage_path: dbItem.storage_path,
              previewUrl: publicUrlData?.publicUrl || '',
              annotations: dbItem.annotations || '',
              color: dbItem.color,
              is_public: dbItem.is_public || false,
              file_size: dbItem.file_size,
              mime_type: dbItem.mime_type,
              isPlaying: false,
              isUploading: false,
            };
          });
          setVaultItems(fetchedItems);
          const currentTotalSize = fetchedItems.reduce((acc, item) => acc + (item.file_size || 0), 0);
          setTotalSize(currentTotalSize);
        }
      } catch (e: any) {
        console.error('Unexpected error fetching vault items:', e);
        toast({ title: "Erreur inattendue", description: "Impossible de charger les fichiers du vault.", variant: "destructive" });
        setVaultItems([]);
        setTotalSize(0);
      }
    };
    fetchVaultItems();
  }, [songId, userId, supabaseClient]);

  const handlePlayPauseSingle = (id: string) => {
    setVaultItems(prev => prev.map(item => ({ ...item, isPlaying: item.id === id ? !item.isPlaying : false })));
  };

  const updateVaultItemInDb = async (itemId: string, updates: Partial<VaultItem>) => {
    const item = vaultItems.find(i => i.id === itemId);
    if (!item?.db_id || !supabaseClient) {
      return;
    }
    const dbUpdates: { [key: string]: any } = {};
    if (updates.annotations !== undefined) dbUpdates.annotations = updates.annotations;
    if (updates.color !== undefined) dbUpdates.color = updates.color;
    if (updates.is_public !== undefined) dbUpdates.is_public = updates.is_public;
    if (Object.keys(dbUpdates).length === 0) return;
    dbUpdates.updated_at = new Date().toISOString();
    try {
      const { error } = await supabaseClient.from('song_vault_items').update(dbUpdates).eq('id', item.db_id);
      if (error) throw error;
    } catch (error: any) {
      console.error('Error updating vault item in DB:', error);
      toast({ title: "Erreur de mise à jour", description: `Le champ n'a pas pu être sauvegardé: ${error.message}`, variant: "destructive" });
    }
  };

  const handleColorChange = (id: string, color: string) => {
    setVaultItems(prev => prev.map(item => item.id === id ? { ...item, color } : item));
    updateVaultItemInDb(id, { color });
  };

  const handleIsPublicChange = (id: string, isPublic: boolean) => {
    setVaultItems(prev => prev.map(item => item.id === id ? { ...item, is_public: isPublic } : item));
    updateVaultItemInDb(id, { is_public: isPublic });
  };
  
  const handleAnnotationChange = (id: string, value: string) => {
    setVaultItems(prev => prev.map(item => item.id === id ? { ...item, annotations: value } : item));
    updateVaultItemInDb(id, { annotations: value });
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    let currentTotalSize = totalSize;
    const newLocalItems: VaultItem[] = []; 

    acceptedFiles.forEach(file => {
      if (vaultItems.length + newLocalItems.length >= MAX_FILES_GRATUIT) {
        toast({ title: 'Limite atteinte', description: 'Nombre maximum de fichiers atteint.', variant: 'destructive' });
        return;
      }
      if (currentTotalSize + file.size > MAX_TOTAL_SIZE_GRATUIT) {
        toast({ title: 'Limite atteinte', description: 'Espace de stockage maximum atteint.', variant: 'destructive' });
        return;
      }
      const isMp3 = file.type === 'audio/mpeg' || file.name.toLowerCase().endsWith('.mp3');
      const newItemId = `${file.name}-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;

      if (!isMp3) {
        newLocalItems.push({
          id: newItemId, file, fileName: file.name, previewUrl: '', annotations: '', is_public: false,
          file_size: file.size, mime_type: file.type, waveformError: 'Fichier non supporté (MP3 uniquement)', isUploading: false,
        });
      } else {
        currentTotalSize += file.size;
        newLocalItems.push({
          id: newItemId, file, fileName: file.name, previewUrl: URL.createObjectURL(file), annotations: '',
          color: randomColorFromString(file.name), is_public: false, file_size: file.size, mime_type: file.type, 
          isUploading: true, 
        });
      }
    });

    setVaultItems(prevItems => [...prevItems, ...newLocalItems]);
    setTotalSize(currentTotalSize);

    if (songId && userId) { 
      newLocalItems.forEach(async (item) => {
        if (item.file && !item.waveformError) {
          setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, isUploading: true } : i));
          const filePathInStorage = `public/${userId}/${songId}/${crypto.randomUUID()}-${item.fileName}`;
          try {
            const { error: uploadError } = await supabaseClient.storage
              .from('song-audio-vault')
              .upload(filePathInStorage, item.file, { cacheControl: '3600', upsert: false });
            if (uploadError) throw uploadError;

            const { data: publicUrlData } = supabaseClient.storage.from('song-audio-vault').getPublicUrl(filePathInStorage);
            const newDbRecordPayload = {
              song_id: songId, user_id: userId, file_name: item.fileName, storage_path: filePathInStorage,
              annotations: item.annotations, color: item.color, is_public: item.is_public,
              file_size: item.file_size, mime_type: item.mime_type,
            };
            const { data: insertedRecord, error: insertError } = await supabaseClient
              .from('song_vault_items').insert(newDbRecordPayload).select().single();
            if (insertError) throw insertError;

            setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, db_id: insertedRecord.id, storage_path: filePathInStorage, previewUrl: publicUrlData.publicUrl || i.previewUrl, isUploading: false, file: undefined } : i));
            toast({ title: "Fichier téléversé!", description: `${item.fileName} a été ajouté au vault.` });
          } catch (error: any) {
            console.error(`Error processing vault item ${item.fileName}:`, error);
            toast({ title: `Erreur avec ${item.fileName}`, description: error.message, variant: "destructive" });
            setVaultItems(prev => prev.map(i => i.id === item.id ? { ...i, isUploading: false, waveformError: "Échec du téléversement" } : i));
          }
        } else if (item.waveformError) {
           setVaultItems(prev => prev.map(i => i.id === item.id ? {...i, isUploading: false} : i));
        }
      });
    } else { 
      newLocalItems.forEach(item => {
        if (!item.waveformError) console.log(`Item ${item.fileName} added locally, isUploading: ${item.isUploading}, pending song save.`);
      });
    }
  }, [vaultItems, totalSize, supabaseClient, songId, userId]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop, accept: { 'audio/mpeg': ['.mp3'] }, multiple: true });

  const handleRemoveFile = async (id: string) => {
    const itemToRemove = vaultItems.find(item => item.id === id);
    if (!itemToRemove) return;

    const originalItems = [...vaultItems]; 
    const originalTotalSize = totalSize;

    if (itemToRemove.previewUrl && itemToRemove.file) URL.revokeObjectURL(itemToRemove.previewUrl);
    if (itemToRemove.file_size) setTotalSize(prev => prev - (itemToRemove.file_size || 0));
    setVaultItems(prev => prev.filter(item => item.id !== id));

    if (itemToRemove.db_id && itemToRemove.storage_path && supabaseClient) {
      try {
        if (itemToRemove.storage_path) { 
          const { error: storageError } = await supabaseClient.storage
            .from('song-audio-vault')
            .remove([itemToRemove.storage_path]);
          if (storageError) console.warn('Error deleting from storage (might be already deleted or non-existent):', storageError.message); 
        }

        const { error: dbError } = await supabaseClient
          .from('song_vault_items')
          .delete()
          .eq('id', itemToRemove.db_id);
        if (dbError) throw dbError;

        toast({ title: "Fichier supprimé", description: `${itemToRemove.fileName} a été retiré du vault.` });
      } catch (error: any) {
        console.error('Error deleting vault item from DB/Storage:', error);
        toast({ title: "Erreur de suppression", description: `Impossible de supprimer ${itemToRemove.fileName}: ${error.message}`, variant: "destructive" });
        setVaultItems(originalItems); 
        setTotalSize(originalTotalSize);
      }
    } else {
      toast({ title: "Fichier local retiré", description: `${itemToRemove.fileName} a été retiré.` });
    }
  };
  
  const storagePercentage = totalSize > 0 ? (totalSize / MAX_TOTAL_SIZE_GRATUIT) * 100 : 0;

  return (
    <Collapsible defaultOpen className="w-full border rounded-lg shadow-sm bg-card dark:bg-zinc-900">
      <CollapsibleTrigger asChild>
        <div className="flex items-center justify-between px-4 py-3 border-b sticky top-0 bg-card z-10 cursor-pointer">
          <h3 className="font-semibold text-base">Song Vault</h3>
          <Button variant="ghost" size="icon" className="rounded-full h-7 w-7" aria-label="Toggle Vault Content">
            <svg width="18" height="18" fill="none" viewBox="0 0 24 24" className="transition-transform duration-200 data-[state=open]:rotate-0 data-[state=closed]:-rotate-180">
              <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Button>
        </div>
      </CollapsibleTrigger>
      <CollapsibleContent>
        <div className="flex flex-col flex-grow p-4 gap-3">
          <div className="flex flex-row flex-wrap gap-2 items-center text-xs text-muted-foreground">
            <span>Fichiers: {vaultItems.length}/{MAX_FILES_GRATUIT}</span>
            <span>|</span>
            <span>Taille: {(totalSize / (1024*1024)).toFixed(1)}Mo / {(MAX_TOTAL_SIZE_GRATUIT / (1024*1024)).toFixed(0)}Mo</span>
          </div>
          <Progress value={storagePercentage} className="w-full h-2" />
          
          <div className="flex-grow overflow-y-auto space-y-3 mt-2 max-h-[calc(100vh-350px)] pr-1">
            {vaultItems.length === 0 && (
              <p className="text-sm text-muted-foreground text-center py-4">Aucun fichier dans le vault.</p>
            )}
            {vaultItems.map((item) => (
              <div key={item.id} className="border rounded-md p-3 flex flex-col gap-2 bg-background shadow-sm">
                <div className="flex items-center justify-between gap-2">
                  <div className="flex items-center gap-2 overflow-hidden flex-1">
                    <FileAudio className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <label style={{marginRight: 4, display: 'inline-flex', alignItems: 'center', cursor: 'pointer'}} title="Changer la couleur">
                      <input
                        type="color"
                        defaultValue={item.color || '#cccccc'} 
                        onChange={e => handleColorChange(item.id, e.target.value)}
                        className="w-4 h-4 p-0 border-0 bg-transparent cursor-pointer"
                        style={{ width: 16, height: 16, border: '1px solid gray', borderRadius: '50%' }}
                      />
                    </label>
                    <span className="text-xs font-medium truncate" title={item.fileName}>{item.fileName}</span>
                    {item.file_size && <span className="text-xs text-muted-foreground">({(item.file_size / (1024*1024)).toFixed(2)} MB)</span>}
                  </div>
                  <Button variant="ghost" size="icon" className="h-7 w-7" onClick={() => handleRemoveFile(item.id)} disabled={item.isUploading}>
                    {item.isUploading ? <UploadCloud className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
                  </Button>
                </div>
                
                <AudioSliderPlayer 
                  file={item.file}
                  audioSrc={item.previewUrl}
                  isPlaying={!!item.isPlaying} 
                  error={item.waveformError} 
                  onPlayPause={() => handlePlayPauseSingle(item.id)} 
                  color={item.color} 
                />
                
                <Input
                  type="text"
                  placeholder="Annotation..."
                  value={item.annotations}
                  onChange={(e) => handleAnnotationChange(item.id, e.target.value)}
                  className="text-xs h-8"
                  disabled={item.isUploading}
                />
                <div className="flex items-center space-x-2 pt-1">
                  <Switch
                    id={`is-public-${item.id}`}
                    checked={item.is_public}
                    onCheckedChange={(checked) => handleIsPublicChange(item.id, checked)}
                    disabled={item.isUploading}
                  />
                  <Label htmlFor={`is-public-${item.id}`} className="text-xs text-muted-foreground cursor-pointer flex items-center">
                    {item.is_public ? <Eye className="h-3 w-3 mr-1" /> : <EyeOff className="h-3 w-3 mr-1" />}
                    {item.is_public ? 'Public' : 'Privé'}
                  </Label>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-auto pt-3">
            <div
              {...getRootProps()}
              className={`p-6 border-2 border-dashed rounded-md text-center cursor-pointer transition-colors
                          ${isDragActive ? 'border-primary bg-primary/10' : 'border-muted-foreground/30 hover:border-primary/70 hover:bg-muted/50'}`}
            >
              <input {...getInputProps()} />
              <UploadCloud className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
              {isDragActive ? (
                <p className="text-sm">Relâchez pour ajouter...</p>
              ) : (
                <p className="text-sm">Glissez des MP3 ici, ou cliquez.</p>
              )}
            </div>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
