"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { getSupabaseClient } from "@/lib/supabase/client"
import { Disc, Save, X, Plus, Info, Upload, Music } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { ImageUploader } from "@/components/ui/image-uploader"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MultiSelect } from "@/components/ui/multi-select"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"; // Added Select
// Using options from song-options for consistency
import { genreOptions, moodOptions, instrumentationOptions, albumTypeOptions } from '@/lib/constants/song-options'; 

export default function CreateAlbumPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("info")

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    genres: [] as string[],
    moods: [] as string[], 
    instrumentation: [] as string[], 
    album_type: "", // Added album_type
    releaseDate: "",
    isExplicit: false,
    status: "draft",
    coverUrl: "",
    tags: [] as string[],
    songs: [] as string[],
    author: "", 
  })

  const [currentTag, setCurrentTag] = useState("")
  const [availableSongs, setAvailableSongs] = useState<any[]>([])
  const [selectedSongs, setSelectedSongs] = useState<any[]>([])

  useEffect(() => {
    const fetchUser = async () => {
      const supabase = getSupabaseClient();
      const { data: userData } = await supabase.auth.getUser();
      if (userData?.user) {
        const { data: profile } = await supabase
          .from("profiles")
          .select("username")
          .eq("id", userData.user.id)
          .single();
        if (profile?.username) {
          setFormData(prev => ({ ...prev, author: profile.username }));
        }
      }
    };
    fetchUser();
    loadAvailableSongs();
  }, []);

  const loadAvailableSongs = async () => {
    try {
      const supabase = getSupabaseClient()
      const { data: userData } = await supabase.auth.getUser();
      if (!userData?.user) return

      const { data, error } = await supabase
        .from("songs")
        .select("id, title, cover_url, duration, status")
        .eq("user_id", userData.user.id)
        .eq("status", "published")
        .order("title", { ascending: true })

      if (error) throw error
      setAvailableSongs(data || [])
    } catch (error) {
      console.error("Erreur lors du chargement des morceaux:", error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleCoverUpload = (url: string) => {
    setFormData((prev) => ({ ...prev, coverUrl: url }))
  }

  const addTag = () => {
    if (currentTag && !formData.tags.includes(currentTag)) {
      setFormData((prev) => ({ ...prev, tags: [...prev.tags, currentTag] }))
      setCurrentTag("")
    }
  }

  const removeTag = (tag: string) => {
    setFormData((prev) => ({ ...prev, tags: prev.tags.filter((t) => t !== tag) }))
  }

  const addSongToAlbum = (songId: string) => {
    const song = availableSongs.find((s) => s.id === songId)
    if (song && !selectedSongs.some((s) => s.id === songId)) {
      setSelectedSongs((prev) => [...prev, song])
      setFormData((prev) => ({ ...prev, songs: [...prev.songs, songId] }))
    }
  }

  const removeSongFromAlbum = (songId: string) => {
    setSelectedSongs((prev) => prev.filter((s) => s.id !== songId))
    setFormData((prev) => ({ ...prev, songs: prev.songs.filter((id) => id !== songId) }))
  }

  const handleSave = async (status: "draft" | "published") => {
    setIsLoading(true);
    // Validations
    if (!formData.title) {
      toast({ title: "Erreur", description: "Le titre est obligatoire", variant: "destructive" });
      setIsLoading(false); return;
    }
    if (status === "published" && !formData.coverUrl) {
      toast({ title: "Erreur", description: "Vous devez ajouter une pochette pour publier l'album", variant: "destructive" });
      setIsLoading(false); return;
    }
    if (status === "published" && formData.songs.length === 0) {
      toast({ title: "Erreur", description: "Vous devez ajouter au moins un morceau à l'album pour le publier", variant: "destructive" });
      setIsLoading(false); return;
    }

    const supabase = getSupabaseClient();
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      toast({ title: "Erreur d'authentification", description: "Vous devez être connecté pour créer un album", variant: "destructive" });
      setIsLoading(false); return;
    }
    const user = userData.user;

    try {
      const { data: albumData, error: albumError } = await supabase
        .from("albums")
        .insert({
          user_id: user.id,
          author: formData.author,
          title: formData.title,
          description: formData.description,
          genre: formData.genres.length > 0 ? formData.genres : null, 
          moods: formData.moods.length > 0 ? formData.moods : null, 
          instrumentation: formData.instrumentation.length > 0 ? formData.instrumentation : null, 
          album_type: formData.album_type || null, // Save album_type
          release_date: formData.releaseDate || null,
          cover_url: formData.coverUrl,
          status: status,
          is_explicit: formData.isExplicit,
          total_duration: 0, // Add default total_duration
        })
        .select()
        .single(); // Expecting a single record

      if (albumError) throw albumError;
      if (!albumData) throw new Error("La création de l'album a échoué et n'a retourné aucune donnée.");
      
      const albumId = albumData.id;

      if (formData.songs.length > 0) {
        const albumSongs = formData.songs.map((songId, index) => ({
          album_id: albumId,
          song_id: songId,
          track_number: index + 1,
        }));
        const { error: songsError } = await supabase.from("album_songs").insert(albumSongs);
        if (songsError) {
          console.error("Erreur critique lors de l'ajout des morceaux à l'album:", songsError);
          toast({ title: "Erreur partielle", description: `L'album a été créé, mais une erreur est survenue lors de l'ajout des morceaux: ${songsError.message}. Veuillez modifier l'album pour corriger.`, variant: "destructive", duration: 10000 });
        }
      }

      if (formData.tags.length > 0) {
        let tagErrors: string[] = [];
        for (const tagName of formData.tags) {
          try {
            const { data: existingTag } = await supabase.from("tags").select("id").eq("name", tagName).single();
            let tagId;
            if (existingTag) {
              tagId = existingTag.id;
            } else {
              const { data: newTag, error: tagError } = await supabase.from("tags").insert({ name: tagName }).select().single();
              if (tagError) throw tagError;
              tagId = newTag?.id;
            }
            if (tagId) {
              const { error: resourceTagError } = await supabase.from("resource_tags").insert({ tag_id: tagId, resource_type: "album", resource_id: albumId });
              if (resourceTagError) throw resourceTagError;
            }
          } catch (tagProcessingError: any) {
            console.error(`Erreur lors du traitement du tag "${tagName}":`, tagProcessingError);
            tagErrors.push(`Erreur pour tag "${tagName}": ${tagProcessingError.message}`);
          }
        }
        if (tagErrors.length > 0) {
          toast({ title: "Erreurs partielles lors de l'ajout des tags", description: tagErrors.join("; "), duration: 10000 });
        }
      }

      await supabase.from("activities").insert({ 
        user_id: user.id, 
        type: "album_created", // Changed from activity_type
        target_type: "album",   // Changed from resource_type
        target_id: albumId,     // Changed from resource_id
        content: `a créé un nouvel album: ${formData.title}` 
      });
      toast({ title: status === "published" ? "Album publié" : "Brouillon enregistré", description: status === "published" ? "Votre album a été publié avec succès" : "Votre brouillon a été enregistré" });
      router.push(status === "published" ? `/albums/${albumId}` : "/albums");

    } catch (error: any) {
      toast({ title: "Erreur", description: error.message || "Une erreur s'est produite lors de la création de l'album", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }

  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Créer un nouvel album</h1>
          <p className="text-muted-foreground">Ajoutez les détails de votre album et sélectionnez vos morceaux</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.back()}>Annuler</Button>
          <Button variant="outline" onClick={() => handleSave("draft")} disabled={isLoading}>
            <Save className="mr-2 h-4 w-4" />Enregistrer comme brouillon
          </Button>
          <Button onClick={() => handleSave("published")} disabled={isLoading}>Publier</Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="info">Informations</TabsTrigger>
          <TabsTrigger value="songs">Morceaux</TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center"><Info className="mr-2 h-5 w-5" />Informations de l'album</CardTitle>
                  <CardDescription>Ajoutez les informations de base de votre album</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Titre de l'album *</Label>
                    <Input id="title" name="title" value={formData.title} onChange={handleInputChange} placeholder="Entrez le titre de votre album" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="author">Auteur</Label>
                    <Input id="author" name="author" value={formData.author} disabled />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" name="description" value={formData.description} onChange={handleInputChange} placeholder="Décrivez votre album" rows={4} />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="genres">Genres</Label>
                      <MultiSelect options={genreOptions} selected={formData.genres} onChange={(selected) => setFormData((prev) => ({ ...prev, genres: selected }))} placeholder="Sélectionnez des genres" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="moods">Ambiances / Moods</Label>
                      <MultiSelect options={moodOptions} selected={formData.moods} onChange={(selected) => setFormData((prev) => ({ ...prev, moods: selected }))} placeholder="Sélectionnez des ambiances" />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="instrumentation">Instrumentation</Label>
                      <MultiSelect options={instrumentationOptions} selected={formData.instrumentation} onChange={(selected) => setFormData((prev) => ({ ...prev, instrumentation: selected }))} placeholder="Sélectionnez des instruments" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="album_type">Type d'album</Label>
                      <Select value={formData.album_type} onValueChange={(value) => setFormData(prev => ({ ...prev, album_type: value }))}>
                        <SelectTrigger id="album_type">
                          <SelectValue placeholder="Sélectionnez un type" />
                        </SelectTrigger>
                        <SelectContent>
                          {albumTypeOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="releaseDate">Date de sortie</Label>
                      <Input id="releaseDate" name="releaseDate" type="date" value={formData.releaseDate} onChange={handleInputChange} />
                    </div>
                    <div className="flex items-center space-x-2 pt-5"> {/* Adjusted for alignment */}
                      <Switch id="isExplicit" checked={formData.isExplicit} onCheckedChange={(checked) => handleSwitchChange("isExplicit", checked)} />
                      <Label htmlFor="isExplicit" className="cursor-pointer">Contenu explicite</Label>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Tags</Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {formData.tags.map((tag) => ( <Badge key={tag} variant="secondary" className="flex items-center gap-1">{tag}<X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} /></Badge> ))}
                    </div>
                    <div className="flex gap-2">
                      <Input value={currentTag} onChange={(e) => setCurrentTag(e.target.value)} placeholder="Ajouter un tag" onKeyDown={(e) => { if (e.key === "Enter") { e.preventDefault(); addTag(); }}} />
                      <Button type="button" variant="outline" onClick={addTag}><Plus className="h-4 w-4" /></Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center"><Upload className="mr-2 h-5 w-5" />Pochette de l'album</CardTitle>
                  <CardDescription>Téléchargez une image pour votre album</CardDescription>
                </CardHeader>
                <CardContent>
                  <ImageUploader onImageUploaded={handleCoverUpload} existingImageUrl={formData.coverUrl} aspectRatio="square" maxWidth={1000} maxHeight={1000} />
                </CardContent>
              </Card>
            </div>
            <div className="space-y-6">
              <Card>
                <CardHeader><CardTitle>Prévisualisation</CardTitle><CardDescription>Aperçu de votre album</CardDescription></CardHeader>
                <CardContent className="flex flex-col items-center">
                  <div className="w-full max-w-[240px] aspect-square rounded-md overflow-hidden bg-muted mb-4">
                    {formData.coverUrl ? <img src={formData.coverUrl} alt="Pochette" className="w-full h-full object-cover" /> : <div className="w-full h-full flex items-center justify-center bg-primary/10"><Disc className="h-16 w-16 text-primary/40" /></div>}
                  </div>
                  <h3 className="text-xl font-bold">{formData.title || "Titre de l'album"}</h3>
                  <p className="text-muted-foreground text-sm text-center">
                    Genres: {formData.genres.length > 0 ? formData.genres.map((g) => genreOptions.find((opt) => opt.value === g)?.label || g).join(", ") : "N/A"}
                  </p>
                  <p className="text-muted-foreground text-sm text-center">
                    Moods: {formData.moods.length > 0 ? formData.moods.map((m) => moodOptions.find((opt) => opt.value === m)?.label || m).join(", ") : "N/A"}
                  </p>
                  <p className="text-muted-foreground text-xs text-center">
                    Instruments: {formData.instrumentation.length > 0 ? formData.instrumentation.map((i) => instrumentationOptions.find((opt) => opt.value === i)?.label || i).join(", ") : "N/A"}
                  </p>
                  {formData.isExplicit && <Badge variant="outline" className="mt-2">Explicite</Badge>}
                  <div className="flex flex-wrap gap-1 mt-4 justify-center">
                    {formData.tags.map((tag) => <Badge key={tag} variant="secondary">{tag}</Badge>)}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader><CardTitle>Conseils</CardTitle></CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2"><h4 className="font-medium">Pochette</h4><p className="text-sm text-muted-foreground">Utilisez une image carrée de haute qualité (minimum 1000x1000 pixels).</p></div>
                  <div className="space-y-2"><h4 className="font-medium">Morceaux</h4><p className="text-sm text-muted-foreground">Ajoutez au moins un morceau à votre album avant de le publier.</p></div>
                  <div className="space-y-2"><h4 className="font-medium">Tags</h4><p className="text-sm text-muted-foreground">Ajoutez des tags pertinents pour améliorer la découvrabilité de votre album.</p></div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="songs" className="space-y-6">
          {/* Content for songs tab remains the same as before */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Music className="mr-2 h-5 w-5" />
                  Morceaux disponibles
                </CardTitle>
                <CardDescription>Sélectionnez les morceaux à ajouter à votre album</CardDescription>
              </CardHeader>
              <CardContent className="h-[400px] overflow-y-auto">
                {availableSongs.length > 0 ? (
                  <div className="space-y-2">
                    {availableSongs
                      .filter((song) => !formData.songs.includes(song.id))
                      .map((song) => (
                        <div
                          key={song.id}
                          className="flex items-center justify-between p-2 rounded-md hover:bg-accent/50 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                              {song.cover_url ? (
                                <img
                                  src={song.cover_url || "/placeholder.svg"}
                                  alt={song.title}
                                  className="h-10 w-10 rounded-md object-cover"
                                />
                              ) : (
                                <Music className="h-5 w-5 text-primary" />
                              )}
                            </div>
                            <div>
                              <div className="font-medium">{song.title}</div>
                              <div className="text-xs text-muted-foreground">
                                {song.duration ? formatDuration(song.duration) : ""}
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => addSongToAlbum(song.id)}
                            className="h-8 w-8 p-0"
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full">
                    <p className="text-muted-foreground">Aucun morceau disponible</p>
                    <Button variant="outline" className="mt-4" asChild>
                      <a href="/songs/create">Créer un morceau</a>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Disc className="mr-2 h-5 w-5" />
                  Morceaux de l'album
                </CardTitle>
                <CardDescription>Organisez l'ordre des morceaux dans votre album</CardDescription>
              </CardHeader>
              <CardContent className="h-[400px] overflow-y-auto">
                {selectedSongs.length > 0 ? (
                  <div className="space-y-2">
                    {selectedSongs.map((song, index) => (
                      <div
                        key={song.id}
                        className="flex items-center justify-between p-2 rounded-md hover:bg-accent/50 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-6 text-center font-medium text-muted-foreground">{index + 1}</div>
                          <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                            {song.cover_url ? (
                              <img
                                src={song.cover_url || "/placeholder.svg"}
                                alt={song.title}
                                className="h-10 w-10 rounded-md object-cover"
                              />
                            ) : (
                              <Music className="h-5 w-5 text-primary" />
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{song.title}</div>
                            <div className="text-xs text-muted-foreground">
                              {song.duration ? formatDuration(song.duration) : ""}
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeSongFromAlbum(song.id)}
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full">
                    <p className="text-muted-foreground">Aucun morceau ajouté à l'album</p>
                    <p className="text-xs text-muted-foreground mt-2">
                      Ajoutez des morceaux depuis la liste des morceaux disponibles
                    </p>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <div className="w-full flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    {selectedSongs.length} morceau{selectedSongs.length !== 1 ? "x" : ""}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Durée totale: {formatDuration(selectedSongs.reduce((acc, song) => acc + (song.duration || 0), 0))}
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
