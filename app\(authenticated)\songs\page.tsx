"use client"

import { useEffect, useState } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import type { Song } from "@/types"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"
import { PlayButton } from "@/components/audio/play-button"
import { usePlaySong } from "@/hooks/use-play-song"
import { MoreHorizontal, Music, Plus } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function SongsPage() {
  const [songs, setSongs] = useState<(Song & { artist?: string })[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createBrowserClient()
  const { playCollection } = usePlaySong()

  useEffect(() => {
    async function fetchSongs() {
      try {
        const { data, error } = await supabase
          .from("songs")
          .select("*, profiles(username, display_name)")
          .order("created_at", { ascending: false })

        if (error) {
          throw error
        }

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const songsWithArtist = await Promise.all(data.map(async (song: any) => {
          return {
            ...song,
            artist: song.profiles?.display_name || song.profiles?.username || "Unknown Artist",
          }
        }))

        setSongs(songsWithArtist)
      } catch (error) {
        console.error("Error fetching songs:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchSongs()
  }, [supabase])

  const playAllSongs = () => {
    if (songs.length > 0) {
      playCollection(songs)
    }
  }

  return (
    <div className="w-full">
      <div className="frosted p-6 rounded-xl w-full flex flex-col gap-6">
        <div className="flex justify-between items-center w-full">
          <h1 className="text-3xl font-bold">Your Songs</h1>
          <div className="flex gap-2 w-full max-w-xs justify-end">
            <Button
              asChild
              size="lg"
              className="bg-primary text-white rounded-xl font-semibold shadow-md hover:bg-primary/90 transition-colors w-full"
            >
              <Link href="/songs/create">
                <Plus className="mr-2 h-5 w-5" />
                Créer un morceau
              </Link>
            </Button>
            <Button onClick={playAllSongs} disabled={songs.length === 0} className="w-full">
              Play All
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse w-full">
                <CardContent className="p-4 h-32"></CardContent>
              </Card>
            ))}
          </div>
        ) : songs.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
            {songs.map((song) => (
              <Card key={song.id} className="overflow-hidden w-full">
                <CardContent className="p-0 w-full">
                  <div className="flex items-center p-4 w-full">
                    <div className="relative mr-4 w-full">
                      <div className="w-16 h-16 rounded bg-muted flex items-center justify-center overflow-hidden">
                        {song.cover_url ? (
                          <img
                            src={song.cover_url || "/placeholder.svg"}
                            alt={song.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Music className="h-8 w-8 text-muted-foreground" />
                        )}
                      </div>
                      <div className="absolute -bottom-2 -right-2">
                        <PlayButton song={song} size="sm" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0 w-full">
                      <Link href={`/songs/${song.id}`} className="hover:underline w-full">
                        <h3 className="font-medium truncate">{song.title}</h3>
                      </Link>
                      <p className="text-sm text-muted-foreground truncate">{song.artist}</p>
                      {song.genre && (
                        <span className="inline-block bg-primary/10 text-primary text-xs px-2 py-0.5 rounded-full mt-1">
                          {song.genre}
                        </span>
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/songs/${song.id}/edit`}>Edit</Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {}}>Add to Queue</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {}}>Add to Playlist</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 w-full">
            <Music className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">No songs yet</h3>
            <p className="text-muted-foreground">Create your first song to get started</p>
            <Button asChild className="mt-4 w-full">
              <Link href="/songs/create">
                <Plus className="mr-2 h-4 w-4" /> Create Song
              </Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
