"use client"

import Link from "next/link"
import { Music, Disc, ListTodo, Plus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function QuickActions() {
  const actions = [
    {
      title: "Créer un morceau",
      description: "Composez et publiez un nouveau morceau",
      href: "/dashboard/songs/create",
      icon: Music,
      color: "bg-blue-500/10 text-blue-500",
    },
    {
      title: "Créer un album",
      description: "Regroupez vos morceaux dans un album",
      href: "/dashboard/albums/create",
      icon: Disc,
      color: "bg-purple-500/10 text-purple-500",
    },
    {
      title: "Ajouter une tâche",
      description: "Ajoutez une tâche à votre liste",
      href: "/dashboard/todos",
      icon: ListTodo,
      color: "bg-green-500/10 text-green-500",
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Actions rapides</CardTitle>
        <CardDescription>Accédez rapidement aux fonctionnalités principales</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-3">
          {actions.map((action) => (
            <Button
              key={action.href}
              variant="outline"
              className="h-auto flex-col items-start gap-2 p-4 text-left"
              asChild
            >
              <Link href={action.href}>
                <div className={`rounded-full p-2 ${action.color}`}>
                  <action.icon className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-medium">{action.title}</div>
                  <div className="text-xs text-muted-foreground mt-1">{action.description}</div>
                </div>
                <div className="ml-auto mt-2">
                  <Plus className="h-4 w-4" />
                </div>
              </Link>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
