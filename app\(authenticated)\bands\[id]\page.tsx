import Link from "next/link"
import { notFound } from "next/navigation"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { Edit, Users, Calendar, MessageSquare, Plus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { BandProjects } from "@/components/bands/band-projects"
import { BandMembers } from "@/components/bands/band-members"
import { BandActivity } from "@/components/bands/band-activity"
import { BandAnalytics } from "@/components/bands/band-analytics"
import { BandCommunication } from "@/components/bands/band-communication"

export default async function BandPage({ params }: { params: { id: string } }) {
  const supabase = createSupabaseServerClient()

  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Fetch band details
  const { data: band } = await supabase.from("bands").select("*").eq("id", params.id).single()

  if (!band) {
    notFound()
  }

  // Check if user is a member of this band
  const { data: membership } = await supabase
    .from("band_members")
    .select("role, permissions")
    .eq("band_id", params.id)
    .eq("user_id", session?.user.id)
    .single()

  const isAdmin = membership?.role === "admin" || membership?.role === "owner"

  // Fetch band members
  const { data: members } = await supabase
    .from("band_members")
    .select(`
      user_id,
      role,
      joined_at,
      profiles (
        id,
        name,
        avatar_url
      )
    `)
    .eq("band_id", params.id)

  // Fetch band projects
  const { data: projects } = await supabase
    .from("band_projects")
    .select("*")
    .eq("band_id", params.id)
    .order("updated_at", { ascending: false })

  return (
    <div className="flex flex-col">
      {/* Band Header */}
      <div className="relative h-48 md:h-64 bg-gradient-to-r from-primary/20 to-primary/5">
        {band.cover_url && (
          <img
            src={band.cover_url || "/placeholder.svg"}
            alt={band.name}
            className="w-full h-full object-cover opacity-50"
          />
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-background to-transparent" />
      </div>

      <div className="container px-4 md:px-6">
        <div className="flex flex-col md:flex-row items-start md:items-end gap-4 -mt-16 md:-mt-20 mb-6 relative z-10">
          <Avatar className="h-32 w-32 border-4 border-background">
            <AvatarImage src={band.avatar_url || "/placeholder.svg?height=128&width=128&query=band logo"} />
            <AvatarFallback className="text-4xl">{band.name.charAt(0)}</AvatarFallback>
          </Avatar>

          <div className="flex-1">
            <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
              <h1 className="text-3xl font-bold">{band.name}</h1>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{band.genre}</Badge>
                <Badge variant="outline">{band.location}</Badge>
              </div>
            </div>
            <p className="text-muted-foreground mt-1">{members?.length || 0} membres actifs</p>
            <p className="mt-2 max-w-2xl">{band.description}</p>
          </div>

          <div className="flex gap-2 mt-4 md:mt-0">
            {isAdmin && (
              <Button variant="outline" asChild>
                <Link href={`/bands/${params.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Éditer le groupe
                </Link>
              </Button>
            )}
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Nouveau projet
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-card rounded-lg p-4 flex flex-col items-center">
            <h3 className="text-lg font-medium mb-1">Projets</h3>
            <p className="text-3xl font-bold">{projects?.length || 0}</p>
          </div>
          <div className="bg-card rounded-lg p-4 flex flex-col items-center">
            <h3 className="text-lg font-medium mb-1">Écoutes</h3>
            <p className="text-3xl font-bold">12.5k</p>
          </div>
          <div className="bg-card rounded-lg p-4 flex flex-col items-center">
            <h3 className="text-lg font-medium mb-1">Collaborations</h3>
            <p className="text-3xl font-bold">86</p>
          </div>
          <div className="bg-card rounded-lg p-4 flex flex-col items-center">
            <h3 className="text-lg font-medium mb-1">Rating</h3>
            <p className="text-3xl font-bold">4.7</p>
          </div>
        </div>

        <Tabs defaultValue="overview" className="mb-6">
          <TabsList>
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="projects">Projets</TabsTrigger>
            <TabsTrigger value="album">Album en cours</TabsTrigger>
            <TabsTrigger value="calendar">Calendrier</TabsTrigger>
            <TabsTrigger value="resources">Ressources</TabsTrigger>
            <TabsTrigger value="settings">Paramètres</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2 space-y-6">
                <BandProjects projects={projects || []} bandId={params.id} />
                <BandActivity bandId={params.id} />
              </div>
              <div className="space-y-6">
                <BandMembers members={members || []} bandId={params.id} isAdmin={isAdmin} />
                <BandAnalytics bandId={params.id} />
                <BandCommunication bandId={params.id} />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="projects" className="mt-6">
            <BandProjects projects={projects || []} bandId={params.id} showAll />
          </TabsContent>

          <TabsContent value="album" className="mt-6">
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Aucun album en cours</h3>
              <p className="mt-2 text-muted-foreground">Créez un nouvel album pour commencer à travailler dessus</p>
              <Button className="mt-4">
                <Plus className="mr-2 h-4 w-4" />
                Créer un album
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="calendar" className="mt-6">
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Calendrier à venir</h3>
              <p className="mt-2 text-muted-foreground">Cette fonctionnalité sera disponible prochainement</p>
            </div>
          </TabsContent>

          <TabsContent value="resources" className="mt-6">
            <div className="text-center py-12">
              <MessageSquare className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Ressources à venir</h3>
              <p className="mt-2 text-muted-foreground">Cette fonctionnalité sera disponible prochainement</p>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="mt-6">
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Paramètres à venir</h3>
              <p className="mt-2 text-muted-foreground">Cette fonctionnalité sera disponible prochainement</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
