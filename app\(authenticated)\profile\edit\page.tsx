"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, ChangeEvent } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { type User } from "@supabase/supabase-js";
import { getSupabaseClient } from "@/lib/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Save, ArrowLeft, EyeIcon as ViewPublicProfileIcon, UserCircle2, Music, Link2 as LinkIcon, Settings2 } from "lucide-react";
import { MultiSelect } from "@/components/ui/multi-select";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { 
  instrumentationOptions, 
  tagOptions as profileTagOptions,
  genreOptions as globalGenreOptions, // Renaming to avoid conflict if local one was different
  influenceOptions as globalInfluenceOptions,
  languageOptions as globalLanguageOptions,
  rolePrimaryOptions as globalRolePrimaryOptions,
  roleSecondaryOptions,
  dawOptions as globalDawOptions,
  osOptions,
  countryOptions
} from '@/lib/constants/song-options';

// Define more structured types
export interface InstrumentPlayed {
  name: string;
  experience_years: number;
}

export interface SocialLink {
  platform: string;
  url: string;
}

export type MultiSelectOption = {
  value: string;
  label: string;
};

const socialPlatformOptionsForLogic: MultiSelectOption[] = [
  { value: "spotify", label: "Spotify" },
  { value: "youtube", label: "YouTube" },
  { value: "soundcloud", label: "SoundCloud" },
  { value: "tiktok", label: "TikTok" },
  { value: "instagram", label: "Instagram" },
  { value: "facebook", label: "Facebook" },
  { value: "twitter", label: "Twitter / X" },
  { value: "bandcamp", label: "Bandcamp" },
  { value: "udio", label: "Udio" },
  { value: "suno", label: "Suno" },
  { value: "autre", label: "Autre site web" },
];

// const genreOptions, influenceOptions, languageOptions, rolePrimaryOptions, dawOptions
// are now imported from '@/lib/constants/song-options'
// For clarity, they are aliased if names might conflict (e.g., globalGenreOptions)

const aiUsageLevelOptions: MultiSelectOption[] = [
  { value: "none", label: "Aucune" },
  { value: "light", label: "Légère" },
  { value: "moderate", label: "Modérée" },
  { value: "heavy", label: "Intensive" },
];

const monetizationGoalOptions: MultiSelectOption[] = [
  { value: "hobby", label: "Hobby / Passion" },
  { value: "streaming", label: "Revenus de streaming" },
  { value: "sync", label: "Synchronisation (films, pubs)" },
  { value: "teaching", label: "Enseignement / Coaching" },
];

// Allow 'other' custom values by using string type, or a more complex union if specific enum values must be enforced alongside 'other'
export type RolePrimaryEnum = string; // Changed to string to accommodate 'other' custom text
// export type RolePrimaryEnum = 'musician_pro' | 'musician_amateur' | 'producer_pro' | ... | 'other'; // More specific if needed

export type UserRoleEnum = 'user' | 'moderator' | 'admin';
export type SubscriptionEnum = 'free' | 'pro' | 'studio';
export type AiUsageEnum = 'none' | 'light' | 'moderate' | 'heavy';
export type MonetizationEnum = 'hobby' | 'streaming' | 'sync' | 'teaching';
export type DawEnum = 
  'Ableton Live' | 'FL Studio' | 'Logic Pro X' | 'Pro Tools' | 
  'Cubase' | 'Studio One' | 'Reaper' | 'Bitwig Studio' | 
  'GarageBand' | 'Reason' | 'MPC Software / Akai Force' | 
  'Digital Performer (MOTU)' | 'Samplitude Pro X' | 
  'Cakewalk by BandLab' | 'Ardour' | 'LMMS' | 
  'DAW en ligne (Soundtrap, BandLab, etc.)' | 
  'DAW Mobile (GarageBand iOS, FLSM, etc.)' | 
  'Aucun / Pas applicable' | 'Autre' | 'Other'; // Added 'Other' for compatibility if old data used it

interface Profile {
  id: string;
  email: string;
  username: string;
  full_name: string;
  display_name: string;
  avatar_url: string | null;
  header_url?: string | null;
  bio: string;
  website: string;
  // location?: string; // Old field, to be removed
  location_city?: string; 
  location_country?: string; 
  social_links: SocialLink[];
  genres: string[];
  influences: string[];
  tags: string[];
  is_artist: boolean; 
  record_label: string;
  equipment: string;
  spoken_languages: string[];
  instruments_played: InstrumentPlayed[]; 
  is_profile_public: boolean;
  show_stats_publicly: boolean;
  allow_collaboration_requests: boolean;
  receive_email_notifications: boolean;
  updated_at?: string;
  role_primary: RolePrimaryEnum;
  roles_secondary: string[];
  // years_active?: number | null; // To be removed
  operating_systems?: string[]; 
  subscription_tier: SubscriptionEnum;
  user_role: UserRoleEnum;
  status_badges: string[];
  main_instruments: string[];
  monetization_goals: MonetizationEnum;
  open_to_collab: boolean;
  primary_daw: DawEnum | null;
  other_daws: string[];
  ai_usage_level: AiUsageEnum;
  ai_usage_percent: number | null;
  ai_tools: string[];
  coins_balance: number;
  ia_credits: number;
  marketing_opt_in: boolean;
}

const initialProfileState: Profile = {
  id: "", email: "", username: "", full_name: "", display_name: "", avatar_url: null, header_url: null,
  bio: "", website: "", /*location: "",*/ location_city: "", location_country: "", social_links: [], genres: [], influences: [], tags: [],
  is_artist: false, record_label: "", equipment: "", spoken_languages: [], instruments_played: [],
  is_profile_public: true, show_stats_publicly: true, allow_collaboration_requests: true, receive_email_notifications: true,
  role_primary: 'listener', roles_secondary: [], /*years_active: null,*/ operating_systems: [], subscription_tier: 'free',
  user_role: 'user', status_badges: [], main_instruments: [], monetization_goals: 'hobby',
  open_to_collab: false, primary_daw: null, other_daws: [], ai_usage_level: 'none',
  ai_usage_percent: null, ai_tools: [], coins_balance: 0, ia_credits: 0, marketing_opt_in: false,
};

export default function EditProfilePage() {
  const router = useRouter();
  const supabase = getSupabaseClient();
  const [user, setUser] = useState<User | null>(null);
  const [userLoading, setUserLoading] = useState(true);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<Profile>(initialProfileState);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [headerPreview, setHeaderPreview] = useState<string | null>(null);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [headerFile, setHeaderFile] = useState<File | null>(null);
  const [customRolePrimary, setCustomRolePrimary] = useState<string>(""); // For 'other' primary role
  const avatarInputRef = React.useRef<HTMLInputElement>(null);
  const headerInputRef = React.useRef<HTMLInputElement>(null);

  useEffect(() => {
    const fetchUserSession = async () => {
      setUserLoading(true);
      const { data: { session } } = await supabase.auth.getSession();
      const activeUser = session?.user ?? null;
      setUser(activeUser);
      if (activeUser && profile.id === "") {
        setProfile(prev => ({ ...prev, id: activeUser.id, email: activeUser.email || '' }));
      }
      setUserLoading(false);
    };
    fetchUserSession();
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      const activeUser = session?.user ?? null;
      setUser(activeUser);
      if (activeUser && profile.id === "") {
         setProfile(prev => ({ ...prev, id: activeUser.id, email: activeUser.email || '' }));
      }
      if (event === 'SIGNED_OUT') router.push('/login');
    });
    return () => { authListener?.subscription.unsubscribe(); };
  }, [supabase, router, profile.id]);

  const fetchProfile = useCallback(async () => {
    if (!user) {
      if (!userLoading) setLoading(false);
      return;
    }
    setLoading(true);
    try {
      const { data, error: fetchError } = await supabase.from("profiles").select("*").eq("id", user.id).single();
      if (fetchError || !data) {
        if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116: "exact" row not found
             console.error("Error fetching profile:", fetchError);
             toast.error(`Erreur chargement profil: ${fetchError.message}`);
        } else {
            // This is a new user or profile was deleted, initialize with defaults
            toast.info("Bienvenue ! Complétez votre profil pour commencer ou sauvegardez pour créer.");
        }
        // Ensure basic user info is set if profile is new/missing
        setProfile(prev => ({ ...initialProfileState, id: user.id, email: user.email || '' }));
      } else {
        // Profile exists, populate form
        let parsedSocialLinks: SocialLink[] = [];
        if (Array.isArray(data.social_links)) {
            // Filter out any malformed entries, ensure platform and url are strings
            parsedSocialLinks = data.social_links.filter( (sl: any) => sl && typeof sl.platform === 'string' && typeof sl.url === 'string');
        } else if (typeof data.social_links === 'object' && data.social_links !== null) {
            // Handle old object format if necessary, convert to array
            Object.entries(data.social_links).forEach(([platform, url]) => {
                if (typeof url === 'string' && url.trim() !== '') { // Ensure URL is a non-empty string
                    parsedSocialLinks.push({ platform: platform.toLowerCase(), url });
                }
            });
        }

        setProfile({
          id: data.id || user.id, // Ensure ID is always set
          email: user.email || data.email || '', // Prioritize current auth user email
          username: data.username || '',
          full_name: data.full_name || '',
          display_name: data.display_name || '',
          avatar_url: data.avatar_url || null,
          header_url: data.header_url || null,
          bio: data.bio || '',
          website: data.website || '',
          // location: data.location || '', // Old field
          location_city: data.location_city || '',
          location_country: data.location_country || '',
          social_links: parsedSocialLinks,
          genres: Array.isArray(data.genres) ? data.genres : [],
          influences: Array.isArray(data.influences) ? data.influences : [],
          tags: Array.isArray(data.tags) ? data.tags : [], // Added tags
          is_artist: typeof data.is_artist === 'boolean' ? data.is_artist : false,
          record_label: data.record_label || '',
          equipment: data.equipment || '',
          spoken_languages: Array.isArray(data.spoken_languages) ? data.spoken_languages : [],
          // Ensure instruments_played is an array of valid objects
          instruments_played: Array.isArray(data.instruments_played) ? data.instruments_played.filter( (inst: any) => typeof inst === 'object' && inst !== null && typeof inst.name === 'string' && typeof inst.experience_years === 'number') : [],
          is_profile_public: typeof data.is_profile_public === 'boolean' ? data.is_profile_public : true,
          show_stats_publicly: typeof data.show_stats_publicly === 'boolean' ? data.show_stats_publicly : true,
          allow_collaboration_requests: typeof data.allow_collaboration_requests === 'boolean' ? data.allow_collaboration_requests : true,
          receive_email_notifications: typeof data.receive_email_notifications === 'boolean' ? data.receive_email_notifications : true,
          updated_at: data.updated_at || undefined,
          // Fields from the extended Profile type
          role_primary: data.role_primary || 'listener',
          roles_secondary: Array.isArray(data.roles_secondary) ? data.roles_secondary : [],
          // years_active: typeof data.years_active === 'number' ? data.years_active : null, // Old field
          operating_systems: Array.isArray(data.operating_systems) ? data.operating_systems : [],
          subscription_tier: data.subscription_tier || 'free',
          user_role: data.user_role || 'user',
          status_badges: Array.isArray(data.status_badges) ? data.status_badges : [],
          main_instruments: Array.isArray(data.main_instruments) ? data.main_instruments : [],
          monetization_goals: data.monetization_goals || 'hobby',
          open_to_collab: typeof data.open_to_collab === 'boolean' ? data.open_to_collab : false,
          primary_daw: data.primary_daw || null,
          other_daws: Array.isArray(data.other_daws) ? data.other_daws : [],
          ai_usage_level: data.ai_usage_level || 'none',
          ai_usage_percent: typeof data.ai_usage_percent === 'number' ? data.ai_usage_percent : null,
          ai_tools: Array.isArray(data.ai_tools) ? data.ai_tools : [],
          coins_balance: typeof data.coins_balance === 'number' ? data.coins_balance : 0,
          ia_credits: typeof data.ia_credits === 'number' ? data.ia_credits : 0,
          marketing_opt_in: typeof data.marketing_opt_in === 'boolean' ? data.marketing_opt_in : false,
        });
      }
    } catch (error) {
      console.error("Unexpected error in fetchProfile:", error);
      toast.error("Une erreur inattendue est survenue lors du chargement du profil.");
    } finally {
      setLoading(false);
    }
  }, [user, userLoading, supabase]); // Removed profile from dependencies to avoid re-fetch on setProfile

  useEffect(() => {
    if (!userLoading && user) {
      fetchProfile();
    } else if (!userLoading && !user) {
      // Only redirect if not loading and no user
      router.push('/login');
      setLoading(false); // Ensure loading is false if redirecting
    }
  }, [user, userLoading, fetchProfile, router]);

  const handleAvatarChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) { // 2MB limit
        toast.error("L'image de l'avatar ne doit pas dépasser 2Mo.");
        setAvatarFile(null);
        if (avatarInputRef.current) avatarInputRef.current.value = "";
        return;
      }
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setAvatarFile(null);
      // setAvatarPreview(profile.avatar_url); // Optionally revert to original if no file selected
    }
  };

  const handleRemoveAvatar = () => {
    setAvatarFile(null);
    setAvatarPreview(null);
    setProfile(prev => ({ ...prev, avatar_url: null }));
    if (avatarInputRef.current) {
      avatarInputRef.current.value = ""; // Reset file input
    }
  };
  
  const handleHeaderChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error("L'image de bannière ne doit pas dépasser 5Mo.");
        setHeaderFile(null);
        if (headerInputRef.current) headerInputRef.current.value = "";
        return;
      }
      setHeaderFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setHeaderPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setHeaderFile(null);
      // setHeaderPreview(profile.header_url); // Optionally revert
    }
  };

  const handleRemoveHeader = () => {
    setHeaderFile(null);
    setHeaderPreview(null);
    setProfile(prev => ({ ...prev, header_url: null }));
    if (headerInputRef.current) {
      headerInputRef.current.value = "";
    }
  };

  const handleSubmit = async (event?: React.FormEvent<HTMLFormElement>) => {
    if (event) event.preventDefault();
    if (!user || !user.id) {
      toast.error("Erreur : Session utilisateur invalide.");
      return;
    }
    setSaving(true);

    let newAvatarUrl = profile.avatar_url; // Start with current or null
    let newHeaderUrl = profile.header_url; // Start with current or null

    try { // Outer try for the whole operation
      // 1. Handle Avatar Upload
      if (avatarFile) {
        const avatarPath = `${user.id}/avatar-${Date.now()}.${avatarFile.name.split('.').pop()}`;
        const { error: avatarUploadError } = await supabase.storage
          .from('avatars')
          .upload(avatarPath, avatarFile, { upsert: true });
        if (avatarUploadError) {
          console.error("Avatar upload error:", avatarUploadError);
          throw new Error(`Erreur upload avatar: ${avatarUploadError.message}`);
        }
        const { data: avatarPublicUrlData } = supabase.storage.from('avatars').getPublicUrl(avatarPath);
        newAvatarUrl = avatarPublicUrlData?.publicUrl || null;
      } else if (profile.avatar_url === null && avatarPreview === null) { 
        // This means user explicitly removed it via UI and didn't select a new one
        newAvatarUrl = null;
      }
      // If no avatarFile and avatar_url was not set to null by remove action, newAvatarUrl retains profile.avatar_url

      // 2. Handle Header Upload
      if (headerFile) {
        const headerPath = `${user.id}/header-${Date.now()}.${headerFile.name.split('.').pop()}`;
        const { error: headerUploadError } = await supabase.storage
          .from('profile-headers') // Using dash as per user's last confirmation
          .upload(headerPath, headerFile, { upsert: true });
        if (headerUploadError) {
          console.error("Header upload error:", headerUploadError);
          throw new Error(`Erreur upload bannière: ${headerUploadError.message}`);
        }
        const { data: headerPublicUrlData } = supabase.storage.from('profile-headers').getPublicUrl(headerPath);
        newHeaderUrl = headerPublicUrlData?.publicUrl || null;
      } else if (profile.header_url === null && headerPreview === null) {
        newHeaderUrl = null;
      }
      // If no headerFile and header_url was not set to null by remove action, newHeaderUrl retains profile.header_url
      
      let finalRolePrimary = profile.role_primary;
      if (profile.role_primary === 'other' && customRolePrimary.trim() !== "") {
        finalRolePrimary = customRolePrimary.trim();
      }

      const profileDataToSave = { 
        ...profile, 
        id: user.id, 
        avatar_url: newAvatarUrl, 
        header_url: newHeaderUrl,
        role_primary: finalRolePrimary, // Use custom role if 'other' was selected and filled
      };
      
      const { id, email, ...updateData } = profileDataToSave; // email and id are not updated directly
      
      const payloadToSave = {
        ...updateData,
        updated_at: new Date().toISOString(),
      };

      // Database operation (upsert)
      const { data: existingProfile, error: checkError } = await supabase
        .from("profiles")
        .select("id")
        .eq("id", user.id)
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 means no rows found
        throw checkError;
      }

      let upsertError;
      if (existingProfile) {
        const { error } = await supabase
          .from("profiles")
          .update(payloadToSave)
          .eq("id", user.id);
        upsertError = error;
      } else {
        const { error } = await supabase
          .from("profiles")
          .insert({ ...payloadToSave, id: user.id }); // Ensure id is included for new profile
        upsertError = error;
      }

      if (upsertError) {
        throw upsertError;
      }

      toast.success("Profil sauvegardé avec succès !");
      fetchProfile(); // Re-fetch to get the latest data
      if (avatarFile) setAvatarFile(null); // Clear file state after successful upload
      if (headerFile) setHeaderFile(null); // Clear file state

    } catch (error: any) {
      console.error("Error saving profile:", error);
      toast.error(`Erreur sauvegarde profil: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  if (loading || userLoading) {
    return <div className="flex items-center justify-center min-h-screen"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }
  if (!user && !userLoading) {
     // This case should ideally be handled by a higher-level auth guard or redirect
    return <div className="flex items-center justify-center min-h-screen"><p>Redirection vers la page de connexion...</p></div>;
  }

  // Ensure currentProfile is always fully initialized to prevent undefined access
  const currentProfile = profile.id ? profile : {...initialProfileState, id: user?.id || "", email: user?.email || ""};


  return (
    <div className="container max-w-5xl mx-auto px-4 py-8">
      {/* Sticky Header */}
      <div className="flex items-center justify-between gap-2 mb-8 sticky top-0 bg-background/80 backdrop-blur-sm py-4 px-2 -mx-2 z-30 border-b">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold">Modifier votre profil</h1>
        </div>
        <div className="flex items-center gap-2">
        {currentProfile.username && (
            <Link href={`/artists/${currentProfile.username}`} target="_blank" rel="noopener noreferrer" passHref legacyBehavior>
                <Button variant="outline" size="sm" asChild><a><ViewPublicProfileIcon className="mr-2 h-4 w-4" />Voir profil public</a></Button>
            </Link>
        )}
        <Button variant="default" type="button" onClick={() => handleSubmit()} disabled={saving} className="bg-primary text-primary-foreground hover:bg-primary/90 shadow-md">
          {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          Enregistrer
        </Button>
        </div>
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit} id="profile-edit-form">
        {/* Profile Header Section */}
        <div className="mb-8 p-6 bg-card dark:bg-slate-800 rounded-2xl shadow-lg space-y-6">
          <div className="flex flex-col md:flex-row gap-6 items-start">
            {/* Avatar */}
            <div className="space-y-2 flex-shrink-0">
              <Label>Avatar</Label>
              <div className="flex flex-col gap-2 items-center md:items-start">
                {(avatarPreview || currentProfile.avatar_url) ? (
                  <img src={avatarPreview || currentProfile.avatar_url || undefined} alt="Aperçu Avatar" className="w-32 h-32 md:w-40 md:h-40 rounded-full object-cover bg-muted ring-2 ring-primary/50" />
                ) : (
                  <div className="w-32 h-32 md:w-40 md:h-40 rounded-full bg-muted flex items-center justify-center text-muted-foreground ring-1 ring-border">
                    <UserCircle2 className="h-16 w-16 md:h-20 md:w-20" />
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <input type="file" accept="image/*" onChange={handleAvatarChange} className="hidden" ref={avatarInputRef} id="avatarUploadHeader" />
                  <Button type="button" size="sm" variant="outline" onClick={() => avatarInputRef.current?.click()}>Changer</Button>
                  {(avatarPreview || currentProfile.avatar_url) && (
                    <Button type="button" variant="ghost" size="sm" onClick={handleRemoveAvatar} className="text-destructive hover:text-destructive-foreground">Supprimer</Button>
                  )}
                </div>
              </div>
            </div>

            {/* Banner & Basic Info */}
            <div className="space-y-4 flex-grow">
              <div>
                <Label>Image de Bannière</Label>
                <div className="mt-1 aspect-[3/1] w-full rounded-lg bg-muted ring-1 ring-border relative overflow-hidden">
                  {(headerPreview || currentProfile.header_url) ? (
                    <img src={headerPreview || currentProfile.header_url || undefined} alt="Aperçu Bannière" className="w-full h-full object-cover" />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                      <span>Pas de bannière (Ratio 3:1 recommandé)</span>
                    </div>
                  )}
                </div>
                 <div className="flex items-center gap-2 mt-2">
                    <input type="file" accept="image/*" onChange={handleHeaderChange} className="hidden" ref={headerInputRef} id="headerUploadHeader" />
                    <Button type="button" size="sm" variant="outline" onClick={() => headerInputRef.current?.click()}>Changer la bannière</Button>
                    {(headerPreview || currentProfile.header_url) && (
                      <Button type="button" variant="ghost" size="sm" onClick={handleRemoveHeader} className="text-destructive hover:text-destructive-foreground">Supprimer</Button>
                    )}
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <Label htmlFor="header_display_name">Nom d'affichage</Label>
                  <div className="flex items-center gap-2">
                    <Input id="header_display_name" value={currentProfile.display_name} onChange={(e) => setProfile({ ...profile, display_name: e.target.value })} placeholder="Votre nom public" className="text-lg flex-grow" />
                    <Badge variant={ currentProfile.subscription_tier === 'pro' || currentProfile.subscription_tier === 'studio' ? 'default' : 'secondary'} className="capitalize whitespace-nowrap">
                      {currentProfile.subscription_tier}
                      {currentProfile.user_role === 'admin' && ' Admin'}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-1">
                  <Label htmlFor="header_username">Nom d'utilisateur (non modifiable)</Label>
                  <Input id="header_username" value={currentProfile.username} disabled className="text-lg bg-muted/50" />
                  {currentProfile.username && <p className="text-xs text-muted-foreground pt-1">Utilisé pour votre URL de profil public : /artists/{currentProfile.username}</p>}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs for different profile sections */}
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 mb-6 border-b rounded-none"> {/* Adjusted to 3 cols */}
            <TabsTrigger value="general" className="pb-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary"><UserCircle2 className="mr-2 h-4 w-4"/>Général</TabsTrigger>
            <TabsTrigger value="artist" className="pb-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary"><Music className="mr-2 h-4 w-4"/>Détails Artiste</TabsTrigger>
            <TabsTrigger value="account" className="pb-3 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary"><Settings2 className="mr-2 h-4 w-4"/>Compte & Préférences</TabsTrigger>
          </TabsList>

          {/* General Tab */}
          <TabsContent value="general" className="mt-6 space-y-6 p-4 border dark:border-slate-800 rounded-2xl">
            <div className="space-y-2"><Label htmlFor="bio_tab">Bio</Label><Textarea id="bio_tab" value={currentProfile.bio} onChange={(e) => setProfile({ ...profile, bio: e.target.value })} placeholder="Parlez un peu de vous..." rows={5} /></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="location_city_tab">Ville</Label>
                <Input id="location_city_tab" value={currentProfile.location_city || ""} onChange={(e) => setProfile({ ...profile, location_city: e.target.value })} placeholder="Votre ville" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location_country_tab">Pays</Label>
                <Select value={currentProfile.location_country || ""} onValueChange={(value) => setProfile({ ...profile, location_country: value })}>
                  <SelectTrigger id="location_country_tab">
                    <SelectValue placeholder="Sélectionnez votre pays" />
                  </SelectTrigger>
                  <SelectContent>
                    {countryOptions.map(option => (<SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2"><Label htmlFor="website_tab">Site web</Label><Input id="website_tab" type="url" value={currentProfile.website} onChange={(e) => setProfile({ ...profile, website: e.target.value })} placeholder="https://votresite.com" /></div>
             <div className="space-y-2"><Label htmlFor="genres_tab">Genres musicaux</Label><MultiSelect options={globalGenreOptions} selected={profile.genres} onChange={(values) => setProfile({ ...profile, genres: values })} placeholder="Sélectionnez ou ajoutez vos genres" /></div>
             <div className="space-y-2"><Label htmlFor="influences_tab">Influences</Label><MultiSelect options={globalInfluenceOptions} selected={profile.influences} onChange={(values) => setProfile({ ...profile, influences: values })} placeholder="Sélectionnez ou ajoutez une influence" /></div>
             <div className="space-y-2">
                <Label htmlFor="profile_tags_tab">Tags / Mots-clés du Profil</Label>
                <MultiSelect options={profileTagOptions} selected={profile.tags} onChange={(values) => setProfile({ ...profile, tags: values })} placeholder="Ajoutez des tags à votre profil" />
             </div>
            {/* Social Links Section */}
            <div className="space-y-2">
              <Label>Réseaux sociaux</Label>
              <div className="space-y-4">
                {currentProfile.social_links.map((link, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Select value={link.platform} onValueChange={(value) => {
                      const updatedLinks = [...currentProfile.social_links];
                      updatedLinks[index].platform = value;
                      setProfile({ ...profile, social_links: updatedLinks });
                    }}>
                      <SelectTrigger className="w-[180px]"><SelectValue placeholder="Plateforme" /></SelectTrigger>
                      <SelectContent>{socialPlatformOptionsForLogic.map((option) => (<SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>))}</SelectContent>
                    </Select>
                    <Input value={link.url} onChange={(e) => {
                      const updatedLinks = [...currentProfile.social_links];
                      updatedLinks[index].url = e.target.value;
                      setProfile({ ...profile, social_links: updatedLinks });
                    }} placeholder="URL du profil" />
                    <Button variant="destructive" size="sm" onClick={() => {
                      const updatedLinks = [...currentProfile.social_links];
                      updatedLinks.splice(index, 1);
                      setProfile({ ...profile, social_links: updatedLinks });
                    }}>Supprimer</Button>
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={() => {
                  setProfile({ ...profile, social_links: [...currentProfile.social_links, { platform: "", url: "" }] });
                }}>Ajouter un lien social</Button>
              </div>
            </div>
          </TabsContent>

          {/* Artist Details Tab */}
          <TabsContent value="artist" className="mt-6 space-y-6 p-4 border dark:border-slate-800 rounded-2xl">
            <div className="space-y-2">
              <Label htmlFor="role_primary_tab">Rôle principal</Label>
              <Select 
                value={currentProfile.role_primary} 
                onValueChange={(value) => {
                  setProfile({ ...profile, role_primary: value as RolePrimaryEnum });
                  if (value !== 'other') {
                    setCustomRolePrimary(""); // Clear custom input if not 'other'
                  }
                }}
              >
                <SelectTrigger id="role_primary_tab">
                  <SelectValue placeholder="Sélectionnez votre rôle principal" />
                </SelectTrigger>
                <SelectContent>
                  {globalRolePrimaryOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {profile.role_primary === 'other' && (
                <Input 
                  value={customRolePrimary}
                  onChange={(e) => setCustomRolePrimary(e.target.value)}
                  placeholder="Précisez votre rôle principal"
                  className="mt-2"
                />
              )}
            </div>
            <div className="space-y-2"><Label htmlFor="roles_secondary_tab">Rôles secondaires</Label><MultiSelect options={roleSecondaryOptions} selected={profile.roles_secondary || []} onChange={(values) => setProfile({ ...profile, roles_secondary: values})} placeholder="Ajoutez des rôles secondaires (vous pouvez taper pour ajouter)" /></div>
            
            <div className="space-y-2">
              <Label>Instruments Joués</Label>
              {profile.instruments_played.map((instrument, index) => (
                <div key={index} className="flex items-center gap-2 p-2 border rounded-md">
                  <Input 
                    value={instrument.name} 
                    onChange={(e) => {
                      const updated = [...profile.instruments_played];
                      updated[index].name = e.target.value;
                      setProfile({...profile, instruments_played: updated});
                    }}
                    placeholder="Instrument (ex: Guitare)"
                    className="flex-grow"
                  />
                  <Input 
                    type="number" 
                    value={instrument.experience_years} 
                    onChange={(e) => {
                      const updated = [...profile.instruments_played];
                      updated[index].experience_years = parseInt(e.target.value, 10) || 0;
                      setProfile({...profile, instruments_played: updated});
                    }}
                    placeholder="Années d'expérience"
                    className="w-32" // Slightly wider for longer placeholder
                    min="0"
                  />
                  <Button variant="ghost" size="sm" onClick={() => {
                    const updated = profile.instruments_played.filter((_, i) => i !== index);
                    setProfile({...profile, instruments_played: updated});
                  }} className="text-destructive hover:text-destructive-foreground">X</Button>
                </div>
              ))}
              <Button type="button" variant="outline" size="sm" onClick={() => {
                 setProfile({ ...profile, instruments_played: [...profile.instruments_played, { name: "", experience_years: 0 }] });
              }}>Ajouter un instrument</Button>
            </div>

            <div className="space-y-2">
              <Label htmlFor="main_instruments_tab">Instruments principaux (Tags)</Label>
               <MultiSelect options={instrumentationOptions} selected={profile.main_instruments || []} onChange={(values) => setProfile({ ...profile, main_instruments: values})} placeholder="Sélectionnez vos instruments principaux" />
            </div>
            <div className="space-y-2"><Label htmlFor="primary_daw_tab">DAW Principal</Label><Select value={currentProfile.primary_daw ?? ""} onValueChange={(value: DawEnum) => setProfile({ ...profile, primary_daw: value })}><SelectTrigger id="primary_daw_tab"><SelectValue placeholder="Sélectionnez votre DAW principal" /></SelectTrigger><SelectContent>{globalDawOptions.map(option => (<SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>))}</SelectContent></Select></div>
            <div className="space-y-2"><Label htmlFor="other_daws_tab">Autres DAWs utilisés</Label><MultiSelect options={globalDawOptions.map(d => ({value: d.label, label: d.label}))} selected={profile.other_daws} onChange={(values) => setProfile({ ...profile, other_daws: values})} placeholder="Ajoutez d'autres DAWs" /></div>
            <div className="space-y-2">
              <Label htmlFor="os_tab">Systèmes d'exploitation utilisés</Label>
              <MultiSelect options={osOptions} selected={profile.operating_systems || []} onChange={(values) => setProfile({ ...profile, operating_systems: values})} placeholder="Sélectionnez vos OS" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="equipment_tab">Équipement notable</Label>
              <Textarea id="equipment_tab" value={profile.equipment || ""} onChange={(e) => setProfile({ ...profile, equipment: e.target.value })} placeholder="Listez votre matériel principal (synthés, guitares, micros, etc.)" rows={4} />
            </div>
            <div className="space-y-2"><Label htmlFor="ai_usage_level_tab">Utilisation de l'IA dans votre musique</Label><Select value={profile.ai_usage_level} onValueChange={(value: AiUsageEnum) => setProfile({ ...profile, ai_usage_level: value })}><SelectTrigger id="ai_usage_level_tab"><SelectValue placeholder="Niveau d'utilisation de l'IA" /></SelectTrigger><SelectContent>{aiUsageLevelOptions.map(option => (<SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>))}</SelectContent></Select></div>
            {profile.ai_usage_level !== 'none' && ( <> <div className="space-y-2"><Label htmlFor="ai_usage_percent_tab">Pourcentage d'utilisation de l'IA (0-100)</Label><Input id="ai_usage_percent_tab" type="number" value={profile.ai_usage_percent ?? ""} onChange={(e) => { const val = e.target.value === "" ? null : parseFloat(e.target.value); if (val === null || (val >= 0 && val <= 100)) {setProfile({ ...profile, ai_usage_percent: val });}}} placeholder="%" min="0" max="100" step="0.01"/></div> <div className="space-y-2"><Label htmlFor="ai_tools_tab">Outils IA utilisés</Label><MultiSelect options={[]} selected={profile.ai_tools} onChange={(values) => setProfile({ ...profile, ai_tools: values})} placeholder="Ajoutez des outils IA" /></div> </> )}
            {/* Monetization goals field is now hidden */}
            {/* <div className="space-y-2"><Label htmlFor="monetization_goals_tab">Objectifs de monétisation</Label><Select value={profile.monetization_goals} onValueChange={(value: MonetizationEnum) => setProfile({ ...profile, monetization_goals: value })}><SelectTrigger id="monetization_goals_tab"><SelectValue placeholder="Vos objectifs" /></SelectTrigger><SelectContent>{monetizationGoalOptions.map(option => (<SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>))}</SelectContent></Select></div> */}
            <div className="flex items-center space-x-2 pt-2"><Checkbox id="open_to_collab_tab" checked={profile.open_to_collab} onCheckedChange={(checked) => setProfile({ ...profile, open_to_collab: !!checked })} /><Label htmlFor="open_to_collab_tab" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Ouvert(e) aux collaborations</Label></div>
          </TabsContent>

          {/* Account & Preferences Tab */}
          <TabsContent value="account" className="mt-6 space-y-6 p-4 border dark:border-slate-800 rounded-2xl">
            <div className="space-y-2"><Label>Niveau d'abonnement</Label><div><Badge variant={ profile.subscription_tier === 'pro' ? 'default' : profile.subscription_tier === 'studio' ? 'secondary' : 'outline'} className="text-sm capitalize">{profile.subscription_tier}</Badge></div>{profile.subscription_tier === 'free' && (<p className="text-xs text-muted-foreground"><Link href="/settings/subscription" className="underline hover:text-primary">Découvrez nos offres Pro et Studio.</Link></p>)}</div>
            <div className="flex items-center space-x-2 pt-2"><Checkbox id="marketing_opt_in_tab" checked={profile.marketing_opt_in} onCheckedChange={(checked) => setProfile({ ...profile, marketing_opt_in: !!checked })} /><Label htmlFor="marketing_opt_in_tab" className="text-sm font-medium">J'accepte de recevoir des communications marketing.</Label></div>
            <div className="flex items-center space-x-2 pt-2"><Checkbox id="is_profile_public_tab" checked={profile.is_profile_public} onCheckedChange={(checked) => setProfile({ ...profile, is_profile_public: !!checked })} /><Label htmlFor="is_profile_public_tab" className="text-sm font-medium">Profil public</Label></div>
            <div className="flex items-center space-x-2 pt-2"><Checkbox id="show_stats_publicly_tab" checked={profile.show_stats_publicly} onCheckedChange={(checked) => setProfile({ ...profile, show_stats_publicly: !!checked })} /><Label htmlFor="show_stats_publicly_tab" className="text-sm font-medium">Afficher les statistiques publiquement</Label></div>
            <div className="flex items-center space-x-2 pt-2"><Checkbox id="allow_collaboration_requests_tab" checked={profile.allow_collaboration_requests} onCheckedChange={(checked) => setProfile({ ...profile, allow_collaboration_requests: !!checked })} /><Label htmlFor="allow_collaboration_requests_tab" className="text-sm font-medium">Autoriser les demandes de collaboration (via profil public)</Label></div>
            <div className="flex items-center space-x-2 pt-2"><Checkbox id="receive_email_notifications_tab" checked={profile.receive_email_notifications} onCheckedChange={(checked) => setProfile({ ...profile, receive_email_notifications: !!checked })} /><Label htmlFor="receive_email_notifications_tab" className="text-sm font-medium">Recevoir les notifications par email</Label></div>
          </TabsContent>
        </Tabs>
        <div className="flex justify-end gap-4 mt-8">
          <Button variant="outline" type="button" onClick={() => router.back()} disabled={saving}>Annuler</Button>
          <Button variant="default" type="submit" disabled={saving} className="bg-primary text-primary-foreground hover:bg-primary/90 shadow-md"> {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}<Save className="mr-2 h-4 w-4" />Enregistrer les modifications</Button>
        </div>
      </form>
    </div>
  );
}
