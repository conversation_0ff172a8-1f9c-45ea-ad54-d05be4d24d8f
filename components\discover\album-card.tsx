import Link from "next/link"
import { Play } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface AlbumCardProps {
  album: any
}

export function AlbumCard({ album }: AlbumCardProps) {
  return (
    <Link href={`/albums/${album.id}`}>
      <Card className="overflow-hidden group">
        <div className="aspect-square relative">
          <img
            src={album.cover_url || "/placeholder.svg?height=300&width=300&query=album cover"}
            alt={album.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <Button size="icon" className="h-12 w-12 rounded-full">
              <Play className="h-6 w-6" />
            </Button>
          </div>
          {album.release_type && (
            <Badge className="absolute top-2 left-2 bg-black/60 text-white">{album.release_type}</Badge>
          )}
        </div>
        <CardContent className="p-3">
          <h3 className="font-medium line-clamp-1">{album.title}</h3>
          <div className="text-xs text-muted-foreground mb-1">
            Auteur : {album.author || album.profiles?.username || "Artiste"}
          </div>
          <p className="text-sm text-muted-foreground line-clamp-1">
            {album.artist_name || "Artiste"} • {album.release_date ? new Date(album.release_date).getFullYear() : ""}
          </p>
        </CardContent>
      </Card>
    </Link>
  )
}
