import React from "react";
import { AppSidebar } from "@/components/sidebar";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { UserProfileForSidebar } from "@/components/sidebar"; // Assuming UserProfileForSidebar is exported

export default async function ArtistPublicLayout({ children }: { children: React.ReactNode }) {
  const supabase = createSupabaseServerClient();
  const { data: { user: authUser } } = await supabase.auth.getUser();

  let userProfileForSidebar: UserProfileForSidebar | null = null;

  if (authUser) {
    const { data: profileData } = await supabase
      .from("profiles")
      .select("id, display_name, username, avatar_url, email") // Fetch necessary fields
      .eq("id", authUser.id)
      .single();

    if (profileData) {
      userProfileForSidebar = {
        id: profileData.id,
        name: profileData.display_name, // Assuming 'name' in UserProfileForSidebar maps to display_name
        username: profileData.username,
        avatar_url: profileData.avatar_url,
        email: profileData.email, // Add email if it's part of UserProfileForSidebar and needed
      };
    } else {
      // Fallback if profile not found for an authenticated user (should ideally not happen)
      userProfileForSidebar = {
        id: authUser.id,
        email: authUser.email,
        name: authUser.email, // Fallback name
      };
    }
  }

  return (
    <div className="flex min-h-screen bg-black">
      <AppSidebar user={userProfileForSidebar} />
      <div className="flex-1 flex flex-col overflow-hidden">
        {children}
      </div>
    </div>
  );
}
