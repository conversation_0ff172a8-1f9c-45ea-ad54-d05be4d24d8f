"use client"

import { useAudio } from "@/contexts/audio-context"
import type { Song } from "@/types"
import { useCallback } from "react"

export function usePlaySong() {
  const { playSong, addToQueue, setQueue } = useAudio()

  const play = useCallback(
    (song: Song & { artist?: string }) => {
      playSong(song)
    },
    [playSong],
  )

  const playCollection = useCallback(
    (songs: (Song & { artist?: string })[], startIndex = 0) => {
      if (songs.length === 0) return

      const firstSong = songs[startIndex]
      const remainingSongs = [...songs.slice(startIndex + 1), ...songs.slice(0, startIndex)]

      playSong(firstSong)
      setQueue(remainingSongs)
    },
    [playSong, setQueue],
  )

  const addSongToQueue = useCallback(
    (song: Song & { artist?: string }) => {
      addToQueue(song)
    },
    [addToQueue],
  )

  return { play, playCollection, addSongToQueue }
}
