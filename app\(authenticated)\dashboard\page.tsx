import { createSupabaseServerClient } from "@/lib/supabase/server"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import Link from "next/link"
import { Music, Disc, Play, MoreHorizontal, MessageSquare, Headphones } from "lucide-react"

import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { LineChart } from "@/components/charts/line-chart"
import { QuickActions } from "@/components/quick-actions"

// Données simulées pour le graphique d'écoutes
const listeningData = [
  { date: "2024-05-01", écoutes: 1200 },
  { date: "2024-05-05", écoutes: 1500 },
  { date: "2024-05-10", écoutes: 1350 },
  { date: "2024-05-15", écoutes: 2000 },
  { date: "2024-05-20", écoutes: 1800 },
  { date: "2024-05-25", écoutes: 2200 },
  { date: "2024-05-30", écoutes: 2500 },
  { date: "2024-06-05", écoutes: 2800 },
  { date: "2024-06-10", écoutes: 3000 },
  { date: "2024-06-15", écoutes: 3200 },
]

const activityData = listeningData;
const playsData = listeningData;

export default async function DashboardPage() {
  const supabase = createSupabaseServerClient()

  // Récupérer les statistiques
  const {
    data: { user },
  } = await supabase.auth.getUser()
  const { data: songs } = await supabase.from("songs").select("*").eq("user_id", user?.id)
  const { data: albums } = await supabase.from("albums").select("*").eq("user_id", user?.id)
  const { data: todos } = await supabase.from("todos").select("*").eq("user_id", user?.id)

  // Récupérer les morceaux récents
  const { data: recentSongs } = await supabase
    .from("songs")
    .select("id, title, cover_art_url, genre, created_at") // Changed cover_url to cover_art_url, removed plays
    .eq("user_id", user?.id)
    .order("created_at", { ascending: false })
    .limit(4)

  // Récupérer les albums récents
  const { data: recentAlbums } = await supabase
    .from("albums")
    .select("id, title, cover_art_url, genre, created_at") // Changed cover_url to cover_art_url
    .eq("user_id", user?.id)
    .order("created_at", { ascending: false })
    .limit(4)

  // Récupérer les morceaux les plus écoutés (temporarily not by plays)
  const { data: topSongs } = await supabase
    .from("songs")
    .select("id, title, cover_art_url, genre") // Changed cover_url to cover_art_url, removed plays
    .eq("user_id", user?.id)
    // .order("plays", { ascending: false }) // Removed order by plays
    .order("created_at", { ascending: false }) // Example: order by creation date instead for now
    .limit(4)

  // Récupérer les commentaires récents
  const { data: recentComments } = await supabase
    .from("comments")
    .select(`
      id,
      content,
      created_at,
      resource_type,
      resource_id,
      profiles:user_id (username, avatar_url)
    `)
    .order("created_at", { ascending: false })
    .limit(5)

  const formatDate = (date: string) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true, locale: fr })
  }

  return (
    <div className="min-h-screen w-full bg-transparent">
      <div className="frosted p-6 rounded-xl w-full">
        <div className="flex flex-col gap-6 w-full">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Tableau de bord</h1>
            <p className="text-muted-foreground">Bienvenue sur votre espace de création musicale</p>
          </div>

          <QuickActions />

          <div className="grid gap-6 md:grid-cols-4 w-full">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Morceaux</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{songs?.length || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {songs?.filter((s) => s.status === "published").length || 0} publiés
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Albums</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{albums?.length || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {albums?.filter((a) => a.status === "published").length || 0} publiés
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Écoutes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{songs?.reduce((acc, song) => acc + (song.plays || 0), 0) || 0}</div>
                <p className="text-xs text-muted-foreground">+12% ce mois-ci</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Tâches</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{todos?.length || 0}</div>
                <p className="text-xs text-muted-foreground">{todos?.filter((t) => t.completed).length || 0} terminées</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2 w-full">
            <Card>
              <CardHeader>
                <CardTitle>Activité récente</CardTitle>
                <CardDescription>Votre activité de création sur les 7 derniers jours</CardDescription>
              </CardHeader>
              <CardContent>
                <LineChart data={activityData} />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Écoutes</CardTitle>
                <CardDescription>Nombre d'écoutes sur les 7 derniers jours</CardDescription>
              </CardHeader>
              <CardContent>
                <LineChart data={playsData} />
              </CardContent>
            </Card>
          </div>

          {/* Morceaux récents */}
          <div className="grid gap-6 md:grid-cols-2 w-full">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Morceaux récents</CardTitle>
                  <CardDescription>Vos dernières créations</CardDescription>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard/songs">Voir tout</Link>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentSongs && recentSongs.length > 0 ? (
                    recentSongs.map((song) => (
                      <div key={song.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-12 w-12 rounded-md overflow-hidden bg-muted">
                            {song.cover_url ? (
                              <img
                                src={song.cover_url || "/placeholder.svg"}
                                alt={song.title}
                                className="h-full w-full object-cover"
                              />
                            ) : (
                              <div className="h-full w-full flex items-center justify-center bg-primary/10">
                                <Music className="h-6 w-6 text-primary/40" />
                              </div>
                            )}
                          </div>
                          <div>
                            <Link href={`/dashboard/songs/${song.id}`} className="font-medium hover:underline">
                              {song.title}
                            </Link>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <span>{song.genre || "Aucun genre"}</span>
                              <span>•</span>
                              <span>{formatDate(song.created_at)}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center text-xs text-muted-foreground">
                            <Headphones className="mr-1 h-3 w-3" />
                            <span>{song.plays || 0}</span>
                          </div>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Play className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <p>Vous n'avez pas encore créé de morceaux</p>
                      <Button variant="outline" className="mt-2" asChild>
                        <Link href="/dashboard/songs/create">Créer un morceau</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Albums récents */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Albums récents</CardTitle>
                  <CardDescription>Vos collections de morceaux</CardDescription>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard/albums">Voir tout</Link>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentAlbums && recentAlbums.length > 0 ? (
                    recentAlbums.map((album) => (
                      <div key={album.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-12 w-12 rounded-md overflow-hidden bg-muted">
                            {album.cover_url ? (
                              <img
                                src={album.cover_url || "/placeholder.svg"}
                                alt={album.title}
                                className="h-full w-full object-cover"
                              />
                            ) : (
                              <div className="h-full w-full flex items-center justify-center bg-primary/10">
                                <Disc className="h-6 w-6 text-primary/40" />
                              </div>
                            )}
                          </div>
                          <div>
                            <Link href={`/dashboard/albums/${album.id}`} className="font-medium hover:underline">
                              {album.title}
                            </Link>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <span>{album.genre || "Aucun genre"}</span>
                              <span>•</span>
                              <span>{formatDate(album.created_at)}</span>
                            </div>
                          </div>
                        </div>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <p>Vous n'avez pas encore créé d'albums</p>
                      <Button variant="outline" className="mt-2" asChild>
                        <Link href="/dashboard/albums/create">Créer un album</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Morceaux les plus écoutés et commentaires récents */}
          <div className="grid gap-6 md:grid-cols-2 w-full">
            <Card>
              <CardHeader>
                <CardTitle>Morceaux les plus écoutés</CardTitle>
                <CardDescription>Vos morceaux les plus populaires</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topSongs && topSongs.length > 0 ? (
                    topSongs.map((song) => (
                      <div key={song.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="h-12 w-12 rounded-md overflow-hidden bg-muted">
                            {song.cover_url ? (
                              <img
                                src={song.cover_url || "/placeholder.svg"}
                                alt={song.title}
                                className="h-full w-full object-cover"
                              />
                            ) : (
                              <div className="h-full w-full flex items-center justify-center bg-primary/10">
                                <Music className="h-6 w-6 text-primary/40" />
                              </div>
                            )}
                          </div>
                          <div>
                            <Link href={`/dashboard/songs/${song.id}`} className="font-medium hover:underline">
                              {song.title}
                            </Link>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <span>{song.genre || "Aucun genre"}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="flex items-center">
                            <Headphones className="mr-1 h-3 w-3" />
                            <span>{song.plays || 0}</span>
                          </Badge>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Play className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <p>Aucune donnée d'écoute disponible</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Commentaires récents</CardTitle>
                <CardDescription>Les derniers retours sur vos créations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentComments && recentComments.length > 0 ? (
                    recentComments.map((comment) => (
                      <div key={comment.id} className="flex gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={Array.isArray(comment.profiles) && comment.profiles.length > 0 && comment.profiles[0] && typeof comment.profiles[0] === 'object' && 'avatar_url' in comment.profiles[0] ? (comment.profiles[0].avatar_url || "/placeholder.svg") : (comment.profiles && typeof comment.profiles === 'object' && 'avatar_url' in comment.profiles ? comment.profiles.avatar_url || "/placeholder.svg" : "/placeholder.svg")}
                          />
                          <AvatarFallback>{Array.isArray(comment.profiles) && comment.profiles.length > 0 && comment.profiles[0] && typeof comment.profiles[0] === 'object' && 'username' in comment.profiles[0] && typeof comment.profiles[0].username === 'string' ? (comment.profiles[0].username.charAt(0).toUpperCase() || "U") : (comment.profiles && typeof comment.profiles === 'object' && 'username' in comment.profiles && typeof comment.profiles.username === 'string' ? comment.profiles.username.charAt(0).toUpperCase() || "U" : "U")}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">{Array.isArray(comment.profiles) && comment.profiles.length > 0 && comment.profiles[0] && typeof comment.profiles[0] === 'object' && 'username' in comment.profiles[0] ? (comment.profiles[0].username || "Utilisateur") : (comment.profiles && typeof comment.profiles === 'object' && 'username' in comment.profiles ? comment.profiles.username || "Utilisateur" : "Utilisateur")}</span>
                            <span className="text-xs text-muted-foreground">{formatDate(comment.created_at)}</span>
                          </div>
                          <p className="text-sm mt-1">{comment.content}</p>
                          <div className="mt-1 text-xs text-muted-foreground">
                            Sur {comment.resource_type === "song" ? "le morceau" : "l'album"} #{comment.resource_id}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <p>Aucun commentaire pour le moment</p>
                      <div className="flex items-center justify-center mt-2">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        <span className="text-sm">Partagez votre musique pour recevoir des retours</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" className="w-full">
                  Voir tous les commentaires
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
