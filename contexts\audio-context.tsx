"use client"

import { createContext, useContext, useState, type ReactNode, useEffect } from "react"
import type { Song } from "@/types"
import { getSupabaseClient } from "@/lib/supabase/client"

interface AudioContextType {
  currentSong: (Song & { artist?: string }) | null
  isPlaying: boolean
  queue: (Song & { artist?: string })[]
  playSong: (song: Song & { artist?: string }) => void
  pauseSong: () => void
  resumeSong: () => void
  nextSong: () => void
  previousSong: () => void
  addToQueue: (song: Song & { artist?: string }) => void
  clearQueue: () => void
  setQueue: (songs: (Song & { artist?: string })[]) => void
}

const AudioContext = createContext<AudioContextType | undefined>(undefined)

export function AudioProvider({ children }: { children: ReactNode }) {
  const [currentSong, setCurrentSong] = useState<(Song & { artist?: string }) | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [queue, setQueue] = useState<(Song & { artist?: string })[]>([])
  const supabase = getSupabaseClient()

  useEffect(() => {
    const loadInitialSong = async () => {
      const lastPlayedSongId = localStorage.getItem("lastPlayedSongId");
      let songLoaded = false;

      if (lastPlayedSongId) {
        try {
          const { data: songData, error } = await supabase
            .from("songs")
            .select(`
              *,
              profiles ( id, username, display_name )
            `)
            .eq("id", lastPlayedSongId)
            .single();

          if (error) throw error;

          if (songData) {
            const profileData = Array.isArray(songData.profiles) ? songData.profiles[0] : songData.profiles;
            setCurrentSong({
              ...songData,
              artist: profileData?.display_name || profileData?.username || "Unknown Artist",
              profiles: undefined 
            });
            songLoaded = true;
          }
        } catch (error) {
          console.error("Error loading last played song:", error);
          localStorage.removeItem("lastPlayedSongId"); // Clear invalid ID
        }
      }

      if (!songLoaded) {
        // Fetch the most recently added song as a default
        try {
          const { data: defaultSongData, error: defaultError } = await supabase
            .from("songs")
            .select(`
              *,
              profiles ( id, username, display_name )
            `)
            .order("created_at", { ascending: false })
            .limit(1)
            .single();

          if (defaultError) throw defaultError;

          if (defaultSongData) {
            const profileData = Array.isArray(defaultSongData.profiles) ? defaultSongData.profiles[0] : defaultSongData.profiles;
            setCurrentSong({
              ...defaultSongData,
              artist: profileData?.display_name || profileData?.username || "Unknown Artist",
              profiles: undefined
            });
          }
        } catch (error) {
          console.error("Error loading default song:", error);
        }
      }
    };

    loadInitialSong();
  }, [supabase]);

  const playSong = (song: Song & { artist?: string }) => {
    setCurrentSong(song)
    setIsPlaying(true)
    if (song && song.id) {
      localStorage.setItem("lastPlayedSongId", song.id.toString());
    }
  }

  const pauseSong = () => {
    setIsPlaying(false)
  }

  const resumeSong = () => {
    setIsPlaying(true)
  }

  const nextSong = () => {
    if (queue.length > 0) {
      const nextSong = queue[0]
      const newQueue = queue.slice(1)
      setCurrentSong(nextSong)
      setQueue(newQueue)
      setIsPlaying(true)
    } else {
      setCurrentSong(null)
      setIsPlaying(false)
    }
  }

  const previousSong = () => {
    // This would require keeping track of play history
    // For simplicity, we'll just restart the current song
    if (currentSong) {
      setIsPlaying(true)
    }
  }

  const addToQueue = (song: Song & { artist?: string }) => {
    setQueue([...queue, song])
  }

  const clearQueue = () => {
    setQueue([])
  }

  return (
    <AudioContext.Provider
      value={{
        currentSong,
        isPlaying,
        queue,
        playSong,
        pauseSong,
        resumeSong,
        nextSong,
        previousSong,
        addToQueue,
        clearQueue,
        setQueue,
      }}
    >
      {children}
    </AudioContext.Provider>
  )
}

export function useAudio() {
  const context = useContext(AudioContext)
  if (context === undefined) {
    throw new Error("useAudio must be used within an AudioProvider")
  }
  return context
}
