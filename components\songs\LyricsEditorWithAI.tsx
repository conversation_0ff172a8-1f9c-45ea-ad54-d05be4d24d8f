"use client";

import { useState, useRef } from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormControl, FormMessage } from "@/components/ui/form"; // Keep FormControl import for potential future use if needed elsewhere, but remove it from around RichLyricsEditor
import { RichLyricsEditor } from "@/components/ui/rich-lyrics-editor";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
// Import Select components
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"; 
import { History, ChevronDown, ChevronUp, Wand2, RefreshCcw, Languages, AlignLeft, Lightbulb, Palette } from 'lucide-react';
import type Quill from 'quill';
import { toast } from "@/hooks/use-toast";

// Assuming types are exported from SongForm or a shared types file
// It might be better to move these to a dedicated types file later (e.g., types/ai.ts, types/song.ts)
import { SongFormValues, AiHistoryItem, AiConfig } from './SongForm'; 

interface LyricsEditorWithAIProps {
  // Editor State & Handlers (from parent)
  lyricsContent: string;
  handleLyricsChange: (newContent: string) => void;
  quillRef: React.RefObject<any>; // Or specific Quill type
  
  // Form Control (from parent)
  formControl: Control<SongFormValues>; // Still needed for FormField

  // Shared AI State & Handlers (from parent)
  aiConfig: AiConfig;
  aiGeneralPrompt: string;
  addAiHistory: (userPrompt: string, assistantResponse: string) => void;
  aiHistory: AiHistoryItem[]; // For display in Collapsible
  showAiHistory: boolean; // For display in Collapsible
  setShowAiHistory: (show: boolean) => void; // For display in Collapsible
}

export function LyricsEditorWithAI({
  lyricsContent,
  handleLyricsChange,
  quillRef,
  formControl,
  aiConfig,
  aiGeneralPrompt,
  addAiHistory,
  aiHistory, // Receive for display
  showAiHistory, // Receive for display
  setShowAiHistory, // Receive for display
}: LyricsEditorWithAIProps) {
  // State specific to AI actions within this component
  const [aiLoading, setAiLoading] = useState(false);
  const [aiLastResult, setAiLastResult] = useState<string | undefined>(undefined);
  const [aiError, setAiError] = useState<string | undefined>(undefined);
  const [currentSelectionRange, setCurrentSelectionRange] = useState<any>(null); // State for selection

  // Utility to strip HTML tags (needed for prompts)
  const stripHtml = (html: string): string => {
    if (typeof document !== "undefined") {
      const doc = new DOMParser().parseFromString(html, 'text/html');
      return doc.body.textContent || "";
    }
    return html.replace(/<[^>]+>/g, ''); 
  };

  // Handler for editor selection changes (now local to this component)
  const handleEditorSelectionChange = (range: any, source: any, editor: any) => {
    setCurrentSelectionRange(range); 
  };

  // --- AI Action Handlers (Moved from SongForm) ---

  // AI Action: Format Lyrics and Chords
  const handleAiFormatLayout = async () => {
    console.log("Trigger AI Format Layout (in LyricsEditorWithAI)");
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);

    const currentHtmlContent = lyricsContent; 
    if (!currentHtmlContent.trim()) {
      toast({ title: "Formatage IA", description: "Aucun contenu à formater.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    const promptForAI = `
      You are a text formatting assistant for musicians.
      Analyze the following HTML content which contains song lyrics and chords. Chords are marked with bold tags (<strong> or <b>).
      Your task is to reformat this content into a standard chord-over-lyrics format.
      - Identify section headers like [VERSE], [CHORUS], [BRIDGE], [INTRO], [OUTRO], etc., and preserve them on their own lines.
      - Place the identified chords (text within bold tags) on the line directly above the corresponding lyrics.
      - Align chords properly above the syllable or word they correspond to. Use spaces for alignment.
      - Each line of lyrics should be on its own line.
      - Preserve empty lines between sections or verses where appropriate.
      - Output the result as plain text, suitable for a text editor or display. Do not include HTML tags in the output.

      Input HTML:
      \`\`\`html
      ${currentHtmlContent}
      \`\`\`

      Formatted Output:
    `;

    console.log("AI Format Layout Prompt:", promptForAI);

    try {
      // TODO: Replace with actual AI call logic using promptForAI and aiConfig
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call
      const aiResponseText = `[VERSE]\nC          G\nLyrics line one.\nAm           F\nAnother line here.`; // Simulate AI plain text response

      setAiLastResult("Formatage simulé appliqué.");
      const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`;
      handleLyricsChange(newHtmlContent); // Call parent handler to update state
      addAiHistory(promptForAI, aiResponseText); // Call parent handler to update history
      toast({ title: "IA: Formatage appliqué!"});

    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de formatage", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Rhyme Suggestions
  const handleAiRhymeSuggestions = async () => {
    console.log("Trigger AI Rhyme Suggestions (in LyricsEditorWithAI)");
    const selection = currentSelectionRange; 
    const editor = quillRef.current?.getEditor();
    let selectedText = '';

    if (selection && selection.length > 0 && editor) {
      selectedText = editor.getText(selection.index, selection.length);
    } else {
       toast({ title: "Suggestions de Rimes", description: "Veuillez sélectionner du texte pour obtenir des rimes.", variant: "destructive" });
       return;
    }

    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);

    const promptForAI = `${aiGeneralPrompt}\n\nSuggest rhymes for the last word of the selected text: "${selectedText}"`;
    console.log("AI Rhyme Prompt:", promptForAI);

    try {
      // TODO: Replace with actual AI call using promptForAI and aiConfig
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      const aiResponseText = `[AI Sim Rhymes for "${selectedText}"]: suggestion1, suggestion2, suggestion3`;
      setAiLastResult(aiResponseText); // Display rhymes locally or pass up if needed elsewhere
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Suggestions de rimes générées!"});
    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de suggestion de rimes", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Tone Analysis
  const handleAiAnalyzeTone = async () => {
    console.log("Trigger AI Tone Analysis (in LyricsEditorWithAI)");
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);

    const currentLyricsText = stripHtml(lyricsContent);
    if (!currentLyricsText.trim()) {
      toast({ title: "Analyse de Ton", description: "Aucune parole à analyser.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    const promptForAI = `${aiGeneralPrompt}\n\nAnalyze the overall tone and mood of the following lyrics:\n\n${currentLyricsText}`;
    console.log("AI Tone Analysis Prompt:", promptForAI);

    try {
      // TODO: Replace with actual AI call using promptForAI and aiConfig
      await new Promise(resolve => setTimeout(resolve, 1000)); 
      // This result doesn't modify lyrics, so just display it.
      // We might need a dedicated display area in this component or pass it up.
      const aiResponseText = `[AI Sim Tone Analysis]: The tone appears to be generally reflective.`; 
      setAiLastResult(aiResponseText); 
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Analyse de ton terminée!"});
    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur d'analyse de ton", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Correct Text
  const handleAiCorrect = async () => {
    console.log("Trigger AI Correct (in LyricsEditorWithAI)");
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    
    let textToProcess = "";
    let promptForAI = "";
    const selection = currentSelectionRange; 
    const editor = quillRef.current?.getEditor();

    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\nCorrect the grammar and spelling of the following selected text:\n\n${textToProcess}`;
    } else {
      textToProcess = stripHtml(lyricsContent); // Use full content if no selection
      promptForAI = `${aiGeneralPrompt}\n\nCorrect the grammar and spelling of the following text:\n\n${textToProcess}`;
    }

    if (!textToProcess.trim()) {
      toast({ title: "Correction IA", description: "Aucun texte à corriger.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    try {
      // TODO: Replace with actual AI call logic using promptForAI and aiConfig
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      const aiResponseText = `[AI Corrected Sim: ${textToProcess.substring(0, 30)}...]`; // Simulate response

      setAiLastResult(`Correction simulée pour: ${textToProcess.substring(0, 50)}...`);
      
      if (selection && selection.length > 0 && editor) {
        editor.deleteText(selection.index, selection.length, 'user');
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        const newFullContent = editor.root.innerHTML; 
        handleLyricsChange(newFullContent); // Update parent state
      } else {
        const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`;
        handleLyricsChange(newHtmlContent); // Update parent state
      }

      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Texte corrigé!"});

    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de correction", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };

  // AI Action: Translate Text
  const handleAiTranslate = async (lang: string) => {
    console.log("Trigger AI Translate (in LyricsEditorWithAI)");
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    
    let textToProcess = "";
    let promptForAI = "";
    const selection = currentSelectionRange; 
    const editor = quillRef.current?.getEditor();

    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\nTranslate the following selected text to ${lang}:\n\n${textToProcess}`;
    } else {
      textToProcess = stripHtml(lyricsContent); // Use full content if no selection
      promptForAI = `${aiGeneralPrompt}\n\nTranslate the following text to ${lang}:\n\n${textToProcess}`;
    }

     if (!textToProcess.trim()) {
      toast({ title: "Traduction IA", description: "Aucun texte à traduire.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    try {
      // TODO: Replace with actual AI call logic using promptForAI and aiConfig
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      const aiResponseText = `[AI Translated Sim to ${lang}: ${textToProcess.substring(0, 30)}...]`; // Simulate response

      setAiLastResult(`Traduction simulée vers ${lang} pour: ${textToProcess.substring(0, 50)}...`);
      
      if (selection && selection.length > 0 && editor) {
        editor.deleteText(selection.index, selection.length, 'user');
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        const newFullContent = editor.root.innerHTML; 
        handleLyricsChange(newFullContent); // Update parent state
      } else {
        const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`;
        handleLyricsChange(newHtmlContent); // Update parent state
      }

      addAiHistory(promptForAI, aiResponseText);
      toast({ title: `IA: Texte traduit en ${lang}!`});

    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de traduction", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  };
  
  // AI Action: Generate Text
  const handleAiGenerate = async (customPrompt?: string) => { 
    console.log("Trigger AI Generate (in LyricsEditorWithAI)");
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    
    let textToProcess = "";
    let promptForAI = "";
    const selection = currentSelectionRange; 
    const editor = quillRef.current?.getEditor();

    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\n${customPrompt || 'Improve or continue this selected text'}:\n\n${textToProcess}`;
    } else {
      textToProcess = stripHtml(lyricsContent); // Use full content if no selection
      promptForAI = `${aiGeneralPrompt}\n\n${customPrompt || 'Continue writing lyrics based on this text'}:\n\n${textToProcess}`;
    }

    try {
      // TODO: Replace with actual AI call logic using promptForAI and aiConfig
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      const aiResponseText = ` [AI Sim Response for: ${textToProcess.substring(0, 30)}...]`; // Simulate response

      setAiLastResult(`Texte généré (simulation) pour: ${textToProcess.substring(0, 50)}...`);
      
      if (selection && selection.length > 0 && editor) {
        editor.deleteText(selection.index, selection.length, 'user');
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        const newFullContent = editor.root.innerHTML; 
        handleLyricsChange(newFullContent); // Update parent state
      } else {
        // Replace full content if no selection
        const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`; 
        handleLyricsChange(newHtmlContent); // Update parent state
      }

      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Texte généré!"});

    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de génération", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  }

  // TODO: Need to pass these handlers down to AiQuickActions, likely via AiAssistantPanel
  // For now, they are defined here but not connected to the UI buttons yet.
  // We will connect them when updating AiAssistantPanel and SongForm props.

  // TODO: Add language selection state if needed for translate button
  const [translateLang, setTranslateLang] = useState('en'); // Default to English

  return (
    <>
      {/* AI Actions Toolbar for Lyrics */}
      <div className="flex flex-wrap gap-2 mb-2 p-2 border rounded-md bg-muted/30">
         {/* Wrap handleAiGenerate in an arrow function for onClick */}
         <Button size="sm" variant="outline" onClick={() => handleAiGenerate()} disabled={aiLoading} className="text-xs" title="Générer/continuer le texte (utilise la sélection si présente)">
           <Wand2 className="w-4 h-4 mr-1" /> Générer
         </Button>
         <Button size="sm" variant="outline" onClick={handleAiCorrect} disabled={aiLoading} className="text-xs" title="Corriger la grammaire/orthographe (utilise la sélection si présente)">
           <RefreshCcw className="w-4 h-4 mr-1" /> Corriger
         </Button>
         <Button size="sm" variant="outline" onClick={handleAiFormatLayout} disabled={aiLoading} className="text-xs" title="Mettre en forme paroles/accords">
           <AlignLeft className="w-4 h-4 mr-1" /> Format Auto
         </Button>
         <Button size="sm" variant="outline" onClick={handleAiRhymeSuggestions} disabled={aiLoading || !(currentSelectionRange && currentSelectionRange.length > 0)} className="text-xs" title="Suggérer des rimes (sélection requise)">
           <Lightbulb className="w-4 h-4 mr-1" /> Rimes
         </Button>
         <Button size="sm" variant="outline" onClick={handleAiAnalyzeTone} disabled={aiLoading} className="text-xs" title="Analyser le ton des paroles">
           <Palette className="w-4 h-4 mr-1" /> Analyser Ton
         </Button>
         <div className="flex gap-1 items-center">
            {/* Basic language select for translate - can be improved */}
            <Select value={translateLang} onValueChange={setTranslateLang}>
                <SelectTrigger className="w-[90px] h-8 text-xs">
                    <SelectValue placeholder="Langue" />
                </SelectTrigger>
                <SelectContent className="text-xs">
                    <SelectItem value="en">EN</SelectItem>
                    <SelectItem value="fr">FR</SelectItem>
                    <SelectItem value="es">ES</SelectItem>
                    {/* Add more common languages */}
                </SelectContent>
            </Select>
            <Button size="sm" variant="outline" onClick={() => handleAiTranslate(translateLang)} disabled={aiLoading} className="text-xs h-8" title={`Traduire en ${translateLang.toUpperCase()} (utilise la sélection si présente)`}>
              <Languages className="w-4 h-4 mr-1" /> Traduire
            </Button>
         </div>
      </div>

      {/* Removed FormControl wrapper */}
      <FormField
        control={formControl}
        name="lyrics"
        render={({ field }) => ( // field is not used here, but keep render prop structure
          <FormItem>
            {/* Removed FormControl wrapper */}
            <RichLyricsEditor
              value={lyricsContent}
              onChange={handleLyricsChange} // Updates parent state
              placeholder="Commencez à écrire les paroles ici..."
              className="min-h-[400px]"
              quillRef={quillRef}
              onSelectionChange={handleEditorSelectionChange} // Updates local state
            />
            <FormMessage />
          </FormItem>
        )}
      />
      {/* Display AI results/errors specific to this component */}
      {aiLoading && <p className="text-sm text-muted-foreground mt-2">IA en cours de traitement...</p>}
      {aiError && <p className="text-sm text-destructive mt-2">Erreur IA: {aiError}</p>}
      {aiLastResult && !aiLoading && <p className="text-sm text-muted-foreground mt-2">Dernier résultat IA: {aiLastResult}</p>}

      {/* Collapsible AI History Section (Display only) */}
      <Collapsible open={showAiHistory} onOpenChange={setShowAiHistory} className="mt-4">
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className="text-xs w-full justify-start text-muted-foreground hover:text-foreground">
            <History className="mr-2 h-4 w-4" />
            Historique IA ({Math.floor(aiHistory.length / 2)} interactions)
            {showAiHistory ? <ChevronUp className="ml-auto h-4 w-4" /> : <ChevronDown className="ml-auto h-4 w-4" />}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2">
          <ScrollArea className="h-[200px] border rounded-md p-2 bg-muted/20">
            {aiHistory.length === 0 ? (
              <p className="text-xs text-muted-foreground text-center py-4">Aucun historique pour le moment.</p>
            ) : (
              <div className="space-y-3">
                {aiHistory.map((item, index) => (
                  <div key={index} className={`text-xs p-2 rounded ${item.role === 'user' ? 'bg-blue-50 dark:bg-blue-900/30' : 'bg-gray-50 dark:bg-gray-800/30'}`}>
                    <p className="font-semibold mb-1 capitalize">{item.role}:</p>
                    <p className="whitespace-pre-wrap break-words">{item.content}</p>
                    {item.timestamp && <p className="text-muted-foreground text-[10px] mt-1">{item.timestamp.toLocaleTimeString()}</p>}
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </CollapsibleContent>
      </Collapsible>
    </>
  );
}
