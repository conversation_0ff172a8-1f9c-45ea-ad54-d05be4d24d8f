export interface Profile {
  id: string
  username: string
  display_name: string | null
  avatar_url: string | null
  bio: string | null
  created_at: string
  updated_at: string
}

export interface Song {
  id: string
  user_id: string
  title: string
  description: string | null
  genre: string | null
  duration: number | null
  cover_url: string | null
  audio_url: string | null
  waveform_url: string | null
  lyrics: string | null
  chords: string | null
  bpm: number | null
  key: string | null
  status: "draft" | "published" | "archived"
  is_ai_generated: boolean
  created_at: string
  updated_at: string
  plays: number | null // Allow null for plays
  release_date: string | null
  artist_name: string | null
  isrc: string | null
  language: string | null
  mood: string | null
  tempo: string | null
  lyrics_language: string | null
  recording_location: string | null
  recording_date: string | null
  writers: string[] | null // Assuming text array
  producers: string[] | null // Assuming text array
  featured_artists: string[] | null // Assuming text array
  visibility: string | null
  scheduled_for: string | null
  stems_available: boolean | null
  instrumental_version_url: string | null
  acapella_version_url: string | null
  original_song_id: string | null // uuid
  is_remix: boolean | null
  remix_of: string | null // uuid
  allow_downloads: boolean | null
  allow_comments: boolean | null
  is_explicit: boolean | null
  bloc_note: string | null
  time_signature: string | null
  capo: number | null
  tuning_frequency: number | null
  composer_name: string | null
  instrumentation: string[] | null // Added instrumentation
  album_id: string | null // Added album_id
}

export interface Album {
  id: string
  user_id: string
  title: string
  description: string | null
  cover_url: string | null
  release_date: string | null
  status: "draft" | "published" | "archived"
  created_at: string
  updated_at: string
}

export interface Playlist {
  id: string
  user_id: string
  title: string
  description: string | null
  cover_url: string | null
  is_public: boolean
  created_at: string
  updated_at: string
}

export interface Activity {
  id: string
  user_id: string
  activity_type:
    | "song_created"
    | "song_updated"
    | "album_created"
    | "album_updated"
    | "comment_added"
    | "playlist_created"
    | "playlist_updated"
  resource_type: "song" | "album" | "playlist" | "comment"
  resource_id: string
  content: string | null
  created_at: string
}

export interface Todo {
  id: string
  user_id: string
  title: string
  description: string | null
  due_date: string | null
  is_completed: boolean
  created_at: string
  updated_at: string
}

export interface Comment {
  id: string
  user_id: string
  resource_type: "song" | "album" | "playlist"
  resource_id: string
  content: string
  created_at: string
  updated_at: string
}

export interface ProjectStats {
  activeProjects: number
  totalListens: number
  followers: number
  revenue?: number
}

export interface ProjectInProgress {
  id: string
  title: string
  type: string
  status: "in_progress" | "completed" | "draft"
  progress: number
  lastUpdated: string
}

export interface RecentSession {
  id: string
  title: string
  lastModified: string
}
