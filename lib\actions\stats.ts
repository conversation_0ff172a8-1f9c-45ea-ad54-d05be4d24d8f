"use server"

// import { createServerActionClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { createSupabaseServerClient } from "@/lib/supabase/server"

export async function incrementPlayCount(songId: string) {
  const supabase = createSupabaseServerClient()

  // Vérifier si l'utilisateur est connecté
  const {
    data: { session },
  } = await supabase.auth.getSession()

  try {
    // Incrémenter le compteur de lecture dans la table plays
    await supabase.from("plays").insert({
      song_id: songId,
      user_id: session?.user?.id || null,
    })

    // Mettre à jour le compteur dans la table songs - RPC REMOVED
    // await supabase.rpc("increment_song_plays", { song_id: songId })

    return { success: true }
  } catch (error) {
    console.error("Erreur lors de l'incrémentation du compteur de lecture:", error)
    return { success: false, error }
  }
}

export async function incrementViewCount(resourceType: string, resourceId: string) {
  const supabase = createSupabaseServerClient()

  // Vérifier si l'utilisateur est connecté
  const {
    data: { session },
  } = await supabase.auth.getSession()

  try {
    // Incrémenter le compteur de vues
    await supabase.from("views").insert({
      resource_id: resourceId,
      user_id: session?.user?.id || null,
      resource_type: resourceType,
    })

    return { success: true }
  } catch (error) {
    console.error("Erreur lors de l'incrémentation du compteur de vues:", error)
    return { success: false, error }
  }
}

export async function incrementSongPlay(songId: string) {
  const supabase = createSupabaseServerClient()

  try {
    // Insérer une nouvelle entrée dans song_plays
    const { error } = await supabase.from("song_plays").insert({
      song_id: songId,
      played_at: new Date().toISOString(),
    })

    if (error) {
      console.error("Erreur lors de l'enregistrement de la lecture:", error)
      return { success: false, error }
    }

    // Mettre à jour le compteur de lectures dans la table songs - RPC REMOVED
    // const { error: updateError } = await supabase.rpc("increment_song_plays", { song_id: songId })

    // if (updateError) {
    //   console.error("Erreur lors de la mise à jour du compteur de lectures:", updateError)
    //   return { success: false, error: updateError }
    // }

    return { success: true }
  } catch (error) {
    console.error("Erreur lors de l'incrémentation des lectures:", error)
    return { success: false, error }
  }
}

export async function getResourceStats(resourceId: string, resourceType: "song" | "album" | "profile" | "band") {
  const supabase = createSupabaseServerClient()

  try {
    let views = 0
    let likes = 0
    let plays = 0

    // Obtenir le nombre de vues
    if (resourceType === "song") {
      const { count: viewCount, error: viewError } = await supabase
        .from("song_views")
        .select("id", { count: "exact", head: true })
        .eq("song_id", resourceId)

      if (!viewError) {
        views = viewCount || 0
      }

      // Obtenir le nombre de likes pour les morceaux
      const { count: likeCount, error: likeError } = await supabase
        .from("likes")
        .select("id", { count: "exact", head: true })
        .eq("resource_id", resourceId)
        .eq("resource_type", resourceType)

      if (!likeError) {
        likes = likeCount || 0
      }

      // Obtenir le nombre de lectures
      const { count: playCount, error: playError } = await supabase
        .from("song_plays")
        .select("id", { count: "exact", head: true })
        .eq("song_id", resourceId)

      if (!playError) {
        plays = playCount || 0
      }
    } else if (resourceType === "album") {
      // Obtenir le nombre de vues pour les albums
      const { count: viewCount, error: viewError } = await supabase
        .from("album_views")
        .select("id", { count: "exact", head: true })
        .eq("album_id", resourceId)

      if (!viewError) {
        views = viewCount || 0
      }

      // Obtenir le nombre de likes (si applicable)
      const { count: likeCount, error: likeError } = await supabase
        .from("likes")
        .select("id", { count: "exact", head: true })
        .eq("resource_id", resourceId)
        .eq("resource_type", resourceType)

      if (!likeError) {
        likes = likeCount || 0
      }
    }

    return { views, likes, plays }
  } catch (error) {
    console.error("Erreur lors de la récupération des statistiques:", error)
    return { views: 0, likes: 0, plays: 0 }
  }
}

export async function getArtistStats(artistId: string) {
  const supabase = createSupabaseServerClient()

  try {
    let playsCount = 0;
    let playsError = null;

    try {
      const { data: artistSongs, error: songsFetchError } = await supabase
        .from("songs")
        .select("id")
        .eq("creator_user_id", artistId);

      if (songsFetchError) {
        console.error("Error fetching artist's songs for play count:", songsFetchError);
        playsError = songsFetchError;
      } else if (artistSongs && artistSongs.length > 0) {
        const songIds = artistSongs.map(song => song.id);
        
        const { count, error: songPlaysError } = await supabase
          .from("song_plays")
          .select("id", { count: "exact", head: true })
          .in("song_id", songIds);

        if (songPlaysError) {
          console.error("Error fetching song plays for artist's songs:", songPlaysError);
          playsError = songPlaysError;
        } else {
          playsCount = count || 0;
        }
      }
    } catch (e) { 
        console.error("Unexpected error calculating artist plays:", e);
        // @ts-ignore
        playsError = e; 
    }

    const { count: followersCount, error: followersError } = await supabase
      .from("follows")
      .select("id", { count: "exact", head: true })
      .eq("following_id", artistId)

    const { count: albumsCount, error: albumsError } = await supabase
      .from("albums")
      .select("id", { count: "exact", head: true })
      .eq("creator_user_id", artistId) // Changed to creator_user_id

    const { count: songsCount, error: songsError } = await supabase
      .from("songs")
      .select("id", { count: "exact", head: true })
      .eq("creator_user_id", artistId)

    return {
      totalPlays: playsError ? 0 : playsCount,
      totalFollowers: followersError ? 0 : followersCount || 0,
      totalAlbums: albumsError ? 0 : albumsCount || 0,
      totalSongs: songsError ? 0 : songsCount || 0,
    }
  } catch (error) {
    console.error("Erreur lors de la récupération des statistiques de l'artiste:", error)
    return { totalPlays: 0, totalFollowers: 0, totalAlbums: 0, totalSongs: 0 }
  }
}

export async function getBandStats(bandId: string) {
  const supabase = createSupabaseServerClient()

  try {
    // Obtenir le nombre total d'abonnés
    const { count: followersCount, error: followersError } = await supabase
      .from("follows")
      .select("id", { count: "exact", head: true })
      .eq("following_id", bandId)

    // Obtenir le nombre total de projets
    const { count: projectsCount, error: projectsError } = await supabase
      .from("band_projects")
      .select("id", { count: "exact", head: true })
      .eq("band_id", bandId)

    // Obtenir le nombre total de membres
    const { count: membersCount, error: membersError } = await supabase
      .from("band_members")
      .select("id", { count: "exact", head: true })
      .eq("band_id", bandId)

    return {
      totalFollowers: followersError ? 0 : followersCount || 0,
      totalProjects: projectsError ? 0 : projectsCount || 0,
      totalMembers: membersError ? 0 : membersCount || 0,
    }
  } catch (error) {
    console.error("Erreur lors de la récupération des statistiques du groupe:", error)
    return { totalFollowers: 0, totalProjects: 0, totalMembers: 0 }
  }
}

export async function getTopSongsByPlays(userId: string, limit: number = 4) {
  const supabase = createSupabaseServerClient();
  try {
    const { data: songs, error: songsError } = await supabase
      .from("songs")
      .select("id, title, cover_art_url, genre") // Use correct column names
      .eq("creator_user_id", userId);

    if (songsError) throw songsError;
    if (!songs) return [];

    const songsWithPlayCounts = await Promise.all(
      songs.map(async (song) => {
        const { count, error: playCountError } = await supabase
          .from("song_plays")
          .select("id", { count: "exact", head: true })
          .eq("song_id", song.id);

        if (playCountError) {
          console.error(`Error fetching play count for song ${song.id}:`, playCountError);
          return { ...song, play_count: 0 };
        }
        return { ...song, play_count: count || 0 };
      })
    );

    songsWithPlayCounts.sort((a, b) => b.play_count - a.play_count);
    return songsWithPlayCounts.slice(0, limit);

  } catch (error) {
    console.error("Error fetching top songs by plays:", error);
    return [];
  }
}

export async function getTotalPlaysForUser(userId: string) {
  const supabase = createSupabaseServerClient();
  try {
    const { data: userSongs, error: songsError } = await supabase
      .from("songs")
      .select("id")
      .eq("creator_user_id", userId);

    if (songsError) throw songsError;
    if (!userSongs || userSongs.length === 0) return 0;

    const songIds = userSongs.map(song => song.id);

    const { count, error: playsError } = await supabase
      .from("song_plays")
      .select("id", { count: "exact", head: true })
      .in("song_id", songIds);

    if (playsError) throw playsError;

    return count || 0;

  } catch (error) {
    console.error("Error fetching total plays for user:", error);
    return 0;
  }
}
