import type React from "react"
import { redirect } from "next/navigation"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { AppSidebar } from "@/components/sidebar"
import { GlobalAudioPlayer } from "@/components/audio/global-audio-player"
import MobileMenuButton from "./mobile-menu-button";

interface AuthenticatedLayoutProps {
  children: React.ReactNode
}

export default async function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {
  const supabase = createSupabaseServerClient()

  // Authentification sécurisée via getUser
  const {
    data: { user },
    error: userError
  } = await supabase.auth.getUser()

  if (!user || userError) {
    redirect("/")
  }

  const { data: profile } = await supabase.from("profiles").select("*", { count: 'exact' }).eq("id", user.id).single()

  // Vérification de la validité de profile
  const userObj = (profile && !('code' in profile)) ? {
    id: user.id,
    email: user.email,
    name: profile.display_name || profile.username || user.email?.split('@')[0],
    avatar_url: profile.avatar_url,
  } : {
    id: user.id,
    email: user.email,
    name: user.email?.split('@')[0], // Fallback name
    avatar_url: undefined,
  }

  return (
    <div className="flex h-screen pb-20">
      {/* Bouton menu mobile sticky (Client Component) */}
      <MobileMenuButton />
      <AppSidebar user={userObj} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  )
}
