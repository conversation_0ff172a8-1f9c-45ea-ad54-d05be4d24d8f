"use client"
import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { BarChart3, Music, Disc, ListTodo, Activity, Users, Compass, Settings, LogOut, Plus, Radio } from "lucide-react"
import { getSupabaseClient } from "@/lib/supabase/client"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuAction,
  // SidebarGroupLabel, // Not used in the original, keep it out unless needed
  // SidebarGroup,      // Not used in the original for navItems, but used once below
  // SidebarGroupContent, // Not used in the original for navItems
  useSidebar, 
} from "@/components/ui/sidebar" // Adjusted imports based on actual usage in original
import { useToast } from "@/hooks/use-toast"
import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, Toolt<PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip"

// Updated interface to include username
export interface UserProfileForSidebar { // Added export
  id: string;
  name?: string | null;
  email?: string | null;
  avatar_url?: string | null;
  username?: string | null; 
}

interface SidebarProps {
  user: UserProfileForSidebar | null;
}

export function AppSidebar({ user }: SidebarProps) {
  const pathname = usePathname()
  const { toast } = useToast()
  const { state: sidebarState, toggleSidebar } = useSidebar(); 
  const isUiCollapsed = sidebarState === 'collapsed'; 

  const handleSignOut = async () => {
    const supabase = getSupabaseClient()
    const { error } = await supabase.auth.signOut()

    if (error) {
      toast({
        title: "Erreur",
        description: "Impossible de se déconnecter. Veuillez réessayer.",
        variant: "destructive",
      })
    } else {
      toast({
        title: "Déconnexion réussie",
        description: "Vous avez été déconnecté avec succès.",
      })
      window.location.href = "/"
    }
  }

  const navItems = [
    { title: "Vue d'ensemble", href: "/dashboard", icon: BarChart3 },
    { title: "Morceaux", href: "/songs", icon: Music, action: { href: "/songs/create", icon: Plus, tooltip: "Nouveau morceau" }},
    { title: "Albums", href: "/albums", icon: Disc, action: { href: "/albums/create", icon: Plus, tooltip: "Nouvel album" }},
    { title: "Groupes", href: "/bands", icon: Users, action: { href: "/bands/create", icon: Plus, tooltip: "Nouveau groupe" }},
    { title: "Découvrir", href: "/discover", icon: Compass },
    { title: "Communauté", href: "/community", icon: Radio },
    { title: "Todolist", href: "/todos", icon: ListTodo },
    { title: "Activité", href: "/activity", icon: Activity },
  ]

  return (
    <Sidebar className={`transition-all duration-300 bg-zinc-900 ${isUiCollapsed ? 'w-16' : 'w-64'} min-h-screen flex flex-col`}>
      <SidebarHeader className={`flex flex-col items-center justify-center gap-2 p-2 border-b border-zinc-800 transition-all duration-300 ${isUiCollapsed ? 'py-2' : 'py-4'}`}>
        <Link href="/">
          <img src="/LOGO_Mouvik.png" alt="Mouvik" className={`transition-all duration-300 ${isUiCollapsed ? 'h-7 mb-0' : 'h-12 mb-2'}`} />
        </Link>
        <button
          onClick={toggleSidebar} 
          className="mt-1 rounded-full p-0.5 hover:bg-primary/10 transition-all duration-200 border border-zinc-700 focus:border-primary shadow-sm"
          aria-label="Réduire la sidebar"
          style={{ alignSelf: 'flex-end' }} 
        >
          <svg width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-zinc-400 group-hover:text-primary transition-colors duration-200"><polyline points="15 18 9 12 15 6"></polyline></svg>
        </button>
      </SidebarHeader>
      <SidebarContent className="bg-zinc-900 transition-all duration-300 flex-1 overflow-y-auto">
        {/* Original structure used a direct SidebarMenu, not SidebarGroup for navItems */}
        <SidebarMenu>
          {navItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SidebarMenuButton asChild isActive={pathname === item.href} className="group transition-all duration-150 hover:bg-primary/10 hover:scale-[1.04] focus:bg-primary/20 text-slate-300 hover:text-slate-50 data-[active=true]:bg-primary/10 data-[active=true]:text-primary">
                      <Link href={item.href}>
                        <item.icon className={`h-5 w-5 ${isUiCollapsed ? 'mx-auto' : ''}`} />
                        {!isUiCollapsed && <span className="transition-all duration-200">{item.title}</span>}
                      </Link>
                    </SidebarMenuButton>
                  </TooltipTrigger>
                  {isUiCollapsed && <TooltipContent side="right">{item.title}</TooltipContent>}
                </Tooltip>
              </TooltipProvider>
              {!isUiCollapsed && item.action && (
                 <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SidebarMenuAction asChild showOnHover>
                          <Link href={item.action.href}>
                            <item.action.icon className="h-4 w-4" />
                            <span className="sr-only">{item.action.tooltip}</span>
                          </Link>
                        </SidebarMenuAction>
                      </TooltipTrigger>
                      <TooltipContent side="right" className="ml-2">{item.action.tooltip}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
              )}
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="border-t border-zinc-800 p-3"> {/* Removed mb-[70px] */}
        <div className="flex flex-col gap-2"> {/* Reduced gap */}
          {user ? (
            <div className={`${isUiCollapsed ? 'space-y-2' : 'space-y-2'}`}>
              <div className={`flex items-center ${isUiCollapsed ? 'justify-center' : 'gap-2'}`}>
                <Link href={user.username ? `/artists/${user.username}` : "/profile/edit"} passHref>
                  <Avatar className={`cursor-pointer border-2 border-primary transition-transform hover:scale-110 ${isUiCollapsed ? 'h-9 w-9' : 'h-10 w-10'}`}>
                    <AvatarImage src={user.avatar_url || "/placeholder.svg"} alt={user.name || user.email || 'User avatar'} />
                    <AvatarFallback className={`bg-slate-700 text-slate-300 ${isUiCollapsed ? 'text-sm' : 'text-base'}`}>
                      {user.name?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                </Link>
                {!isUiCollapsed && (
                  <div className="flex flex-col overflow-hidden flex-1">
                    <Link href={user.username ? `/artists/${user.username}` : "/profile/edit"} passHref className="hover:underline">
                      <span className="text-xs font-semibold text-slate-100 truncate" title={user.name || user.email || undefined}>
                        {user.name || user.email}
                      </span>
                    </Link>
                    {/* This "Artiste" badge might need to be conditional based on user role_primary */}
                    <span className="text-xs text-primary flex items-center gap-1">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3"><path fillRule="evenodd" d="M16.403 3.94A4.25 4.25 0 0013.5 2.25H6.5A4.25 4.25 0 003.94 8.597L2.25 13.5A4.25 4.25 0 006.5 17.75h7a4.25 4.25 0 004.25-4.25l-1.697-4.903A4.25 4.25 0 0013.5 6.5h-3A4.25 4.25 0 006.5 3.94m-.653.75l.005-.004a2.75 2.75 0 012.745-2.436h3.5a2.75 2.75 0 012.745 2.436l.005.004.004.005a2.75 2.75 0 01-.436 2.745h-6.5a2.75 2.75 0 01-.436-2.745l.004-.005zm-.004 5.005l.005.004a2.75 2.75 0 01.436 2.745h6.5a2.75 2.75 0 01.436-2.745l.005-.004.004-.005a2.75 2.75 0 01-.436-2.745h-3.5a2.75 2.75 0 01-2.745-2.436L6.5 6.5l-1.697 4.903a2.75 2.75 0 01-.436 2.745l.004.005z" clipRule="evenodd" /></svg>
                      Artiste {/* TODO: Make this dynamic based on role */}
                    </span>
                  </div>
                )}
              </div>
              <div className={`flex ${isUiCollapsed ? 'flex-col items-center space-y-1' : 'gap-1'}`}>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        href="/profile/edit"
                        className={`flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-xs hover:bg-slate-700/70 hover:text-slate-100 text-slate-300 ${isUiCollapsed ? 'justify-center' : ''}`}
                        aria-label="Modifier le profil"
                      >
                        <Settings className="h-3.5 w-3.5" />
                        {!isUiCollapsed && <span>Profil</span>}
                      </Link>
                    </TooltipTrigger>
                    {isUiCollapsed && <TooltipContent side="right">Profil</TooltipContent>}
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={handleSignOut}
                        className={`flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-xs hover:bg-slate-700/70 hover:text-slate-100 text-slate-300 ${isUiCollapsed ? 'justify-center' : ''}`}
                        aria-label="Déconnexion"
                      >
                        <LogOut className="h-3.5 w-3.5" />
                        {!isUiCollapsed && <span>Déconnexion</span>}
                      </button>
                    </TooltipTrigger>
                    {isUiCollapsed && <TooltipContent side="right">Déconnexion</TooltipContent>}
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          ) : (
            <div className={`flex ${isUiCollapsed ? 'justify-center' : ''}`}>
              <Link
                href="/login"
                className={`rounded-md bg-primary px-3 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 ${isUiCollapsed ? 'p-2' : ''}`}
              >
                {isUiCollapsed ? <LogOut className="h-5 w-5 transform rotate-180" /> : "Se connecter"}
              </Link>
            </div>
          )}
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
