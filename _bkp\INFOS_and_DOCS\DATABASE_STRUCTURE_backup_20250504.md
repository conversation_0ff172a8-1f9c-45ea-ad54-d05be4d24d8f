# Database Structure Summary

This document outlines the proposed database structure for key entities in the Mouvik v2 application.

## Table: Users (Profiles)

Represents user accounts and profiles.

| Field Name      | Data Type     | Constraints/Notes                      | Description                                  |
|-----------------|---------------|----------------------------------------|----------------------------------------------|
| `user_id`       | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the user               |
| `username`      | VARCHAR(50)   | UNIQUE, NOT NULL                       | User's chosen username                       |
| `email`         | VARCHAR(255)  | UNIQUE, NOT NULL                       | User's email address (for login/contact)     |
| `password_hash` | VARCHAR(255)  | NOT NULL                               | Hashed password                              |
| `display_name`  | VARCHAR(100)  |                                        | Public display name                          |
| `bio`           | TEXT          |                                        | User's biography/description                 |
| `profile_picture_url` | VARCHAR(255) |                                  | URL to the profile picture                   |
| `banner_url`    | VARCHAR(255)  |                                        | URL to the profile banner image              |
| `location`      | VARCHAR(100)  |                                        | User's location                              |
| `website_url`   | VARCHAR(255)  |                                        | Link to user's personal website              |
| `user_type`     | VARCHAR(20)   | DEFAULT 'listener'                     | Type of user (e.g., 'listener', 'artist')    |
| `created_at`    | TIMESTAMPZ    | DEFAULT NOW()                          | Timestamp of account creation                |
| `updated_at`    | TIMESTAMPZ    | DEFAULT NOW()                          | Timestamp of last profile update             |
| `last_login`    | TIMESTAMPZ    |                                        | Timestamp of last login                      |
| `is_verified`   | BOOLEAN       | DEFAULT FALSE                          | Indicates if the account is verified         |
| `settings`      | JSONB         |                                        | User-specific settings (privacy, prefs)    |

## Table: Artists

Stores information specifically about artists (can link to Users).

| Field Name      | Data Type     | Constraints/Notes        | Description                                  |
|-----------------|---------------|--------------------------|----------------------------------------------|
| `artist_id`     | UUID          | PRIMARY KEY              | Unique identifier for the artist             |
| `user_id`       | UUID          | FOREIGN KEY (Users)      | Link to the corresponding user account       |
| `artist_name`   | VARCHAR(100)  | NOT NULL                 | Official artist name                         |
| `genre`         | VARCHAR(100)  |                          | Primary music genre                          |
| `label`         | VARCHAR(100)  |                          | Record label affiliation                     |
| `origin_country`| VARCHAR(50)   |                          | Artist's country of origin                   |
| `members`       | TEXT[]        |                          | List of band members (if applicable)         |
| `management_contact` | VARCHAR(255) |                      | Contact info for management                  |
| `booking_contact`    | VARCHAR(255) |                      | Contact info for booking                     |
| `created_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of artist profile creation         |
| `updated_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of last artist profile update      |

*Note: An artist might be a specific type of user or a separate entity linked to a user.*

## Table: Albums

Represents music albums.

| Field Name       | Data Type     | Constraints/Notes        | Description                                  |
|------------------|---------------|--------------------------|----------------------------------------------|
| `album_id`       | UUID          | PRIMARY KEY              | Unique identifier for the album              |
| `artist_id`      | UUID          | FOREIGN KEY (Artists)    | The primary artist of the album              |
| `title`          | VARCHAR(255)  | NOT NULL                 | Album title                                  |
| `release_date`   | DATE          |                          | Date the album was released                  |
| `genre`          | VARCHAR(100)  |                          | Primary genre of the album                   |
| `album_art_url`  | VARCHAR(255)  |                          | URL to the album cover artwork               |
| `description`    | TEXT          |                          | Description or notes about the album         |
| `type`           | VARCHAR(20)   | DEFAULT 'album'          | Type (e.g., 'album', 'EP', 'single')         |
| `label`          | VARCHAR(100)  |                          | Record label                                 |
| `upc_code`       | VARCHAR(20)   | UNIQUE                   | Universal Product Code (optional)            |
| `created_at`     | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of record creation                 |
| `updated_at`     | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of last update                     |

## Table: Morceaux (Tracks/Songs)

Represents individual music tracks.

| Field Name       | Data Type     | Constraints/Notes        | Description                                  |
|------------------|---------------|--------------------------|----------------------------------------------|
| `track_id`       | UUID          | PRIMARY KEY              | Unique identifier for the track              |
| `album_id`       | UUID          | FOREIGN KEY (Albums)     | Album this track belongs to (can be NULL for singles) |
| `artist_id`      | UUID          | FOREIGN KEY (Artists)    | The primary artist of the track              |
| `title`          | VARCHAR(255)  | NOT NULL                 | Track title                                  |
| `duration_ms`    | INTEGER       | NOT NULL                 | Duration of the track in milliseconds        |
| `track_number`   | INTEGER       |                          | Track number on the album                    |
| `disc_number`    | INTEGER       | DEFAULT 1                | Disc number (for multi-disc albums)          |
| `audio_file_url` | VARCHAR(255)  | NOT NULL                 | URL to the audio file                        |
| `genre`          | VARCHAR(100)  |                          | Genre of the track                           |
| `release_date`   | DATE          |                          | Date the track was released (if single)      |
| `isrc_code`      | VARCHAR(20)   | UNIQUE                   | International Standard Recording Code (optional) |
| `lyrics`         | TEXT          |                          | Lyrics of the track                          |
| `play_count`     | BIGINT        | DEFAULT 0                | Number of times the track has been played    |
| `is_explicit`    | BOOLEAN       | DEFAULT FALSE            | Indicates explicit content                   |
| `composer`       | VARCHAR(255)  |                          | Composer(s)                                  |
| `producer`       | VARCHAR(255)  |                          | Producer(s)                                  |
| `created_at`     | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of record creation                 |
| `updated_at`     | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of last update                     |

## Table: User_Follows (Social Relationships - RS)

Represents the following relationship between users.

| Field Name      | Data Type | Constraints/Notes                               | Description                               |
|-----------------|-----------|-------------------------------------------------|-------------------------------------------|
| `follower_id`   | UUID      | PRIMARY KEY, FOREIGN KEY (Users)                | The user who is following                   |
| `following_id`  | UUID      | PRIMARY KEY, FOREIGN KEY (Users)                | The user who is being followed              |
| `followed_at`   | TIMESTAMPZ| DEFAULT NOW()                                   | Timestamp when the follow action occurred |

*Note: `PRIMARY KEY (follower_id, following_id)` ensures a user can only follow another user once.*

## Table: User_Likes_Tracks

Represents tracks liked by users.

| Field Name    | Data Type | Constraints/Notes                               | Description                               |
|---------------|-----------|-------------------------------------------------|-------------------------------------------|
| `user_id`     | UUID      | PRIMARY KEY, FOREIGN KEY (Users)                | The user who liked the track              |
| `track_id`    | UUID      | PRIMARY KEY, FOREIGN KEY (Morceaux)             | The track that was liked                  |
| `liked_at`    | TIMESTAMPZ| DEFAULT NOW()                                   | Timestamp when the like action occurred   |

*Note: `PRIMARY KEY (user_id, track_id)`.*

## Table: Playlists

Represents user-created or system-generated playlists.

| Field Name      | Data Type     | Constraints/Notes        | Description                                  |
|-----------------|---------------|--------------------------|----------------------------------------------|
| `playlist_id`   | UUID          | PRIMARY KEY              | Unique identifier for the playlist           |
| `user_id`       | UUID          | FOREIGN KEY (Users)      | The user who owns the playlist               |
| `name`          | VARCHAR(100)  | NOT NULL                 | Name of the playlist                         |
| `description`   | TEXT          |                          | Description of the playlist                  |
| `is_public`     | BOOLEAN       | DEFAULT TRUE             | Visibility of the playlist                   |
| `cover_image_url`| VARCHAR(255) |                          | URL for a custom playlist cover image        |
| `created_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of playlist creation               |
| `updated_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of last playlist update            |

## Table: Playlist_Tracks

Associates tracks with playlists (many-to-many).

| Field Name    | Data Type | Constraints/Notes                               | Description                                |
|---------------|-----------|-------------------------------------------------|--------------------------------------------|
| `playlist_id` | UUID      | PRIMARY KEY, FOREIGN KEY (Playlists)            | The playlist                               |
| `track_id`    | UUID      | PRIMARY KEY, FOREIGN KEY (Morceaux)             | The track in the playlist                  |
| `added_at`    | TIMESTAMPZ| DEFAULT NOW()                                   | Timestamp when the track was added         |
| `track_order` | INTEGER   | NOT NULL                                        | Order of the track within the playlist     |

*Note: `PRIMARY KEY (playlist_id, track_id)`. Consider `UNIQUE (playlist_id, track_order)`.*

## Other Important Considerations

*   **Indexing:** Add appropriate indexes (e.g., on `username`, `email`, `artist_id` in `Albums` and `Morceaux`, `user_id` in `Playlists`, etc.) for performance.
*   **Foreign Key Constraints:** Ensure `ON DELETE` and `ON UPDATE` actions are defined appropriately (e.g., `CASCADE`, `SET NULL`, `RESTRICT`).
*   **Data Types:** Choose the most suitable data types (e.g., `UUID` vs `BIGSERIAL` for primary keys, `TIMESTAMPZ` vs `TIMESTAMP`). `UUID` is generally preferred for distributed systems.
*   **Normalization:** This structure is reasonably normalized, but review specific use cases for potential denormalization for performance if needed (e.g., caching artist name on tracks).
*   **Authentication/Authorization:** User authentication details (`password_hash`) are included. Role-based access control might be needed.
*   **Social Features:** Beyond follows and likes, consider comments, shares, etc., which would require additional tables.
*   **Streaming/File Storage:** `audio_file_url` implies files are stored elsewhere (like S3). Need a strategy for managing these files.
*   **Search:** Consider implementing a dedicated search index (e.g., Elasticsearch) for complex searching across entities.
