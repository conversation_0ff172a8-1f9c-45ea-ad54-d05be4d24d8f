"use client"

import { useState, useEffect } from "react"
import { Heart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { createBrowserClient } from "@/lib/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

interface LikeButtonProps {
  resourceId: string
  resourceType: "song" | "album" | "artist" | "band" | "comment"
  initialLikes: number
  userId?: string
  size?: "sm" | "md" | "lg";
  onLikeToggle?: (newIsLiked: boolean) => void; // Callback to notify parent of change
  className?: string; // Add className to props
}

export function LikeButton({ resourceId, resourceType, initialLikes, userId, size = "md", onLikeToggle, className }: LikeButtonProps) {
  // Removed internal 'likes' state, rely on 'initialLikes' prop for display count (handled by parent)
  const [isLiked, setIsLiked] = useState(false); // Tracks if the CURRENT user liked it
  const [isLoading, setIsLoading] = useState(true); // Tracks loading state for the user-specific check
  const supabase = createBrowserClient();
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    if (!userId) {
      setIsLoading(false)
      return // Don't check like status if user is not logged in
    }

    const checkLikeStatus = async () => {
      setIsLoading(true)
      const { data: userLike, error } = await supabase
        .from("likes")
        .select("id")
        .eq("resource_id", resourceId)
        .eq("resource_type", resourceType)
        .eq("user_id", userId) // Filter by current user
        .maybeSingle()

      if (error) {
        console.error("Error checking like status:", error)
        toast({ variant: "destructive", title: "Erreur", description: "Impossible de vérifier le statut du like." })
      } else {
        setIsLiked(!!userLike)
      }
      setIsLoading(false);
    }

    checkLikeStatus();
  }, [resourceId, resourceType, userId, supabase, toast]); // Dependency array is correct

  const handleLike = async () => {
    if (!userId) {
      toast({ title: "Connexion requise", description: "Vous devez être connecté pour liker." })
      // Optionally redirect to login or open auth modal
      // router.push('/login');
      return
    }

    if (isLoading) return // Prevent multiple clicks while loading

    try {
      const {
        data: { session },
      } = await supabase.auth.getSession()

      if (!session) {
        toast({
          title: "Connexion requise",
          description: "Vous devez être connecté pour liker ce contenu",
          variant: "destructive",
        })
        return
      }

      if (isLiked) {
        // Supprimer le like
        await supabase
          .from("likes")
          .delete()
          .eq("resource_type", resourceType)
          .eq("resource_id", resourceId)
          .eq("user_id", userId)

        setIsLiked(false);
        // Call callback instead of updating internal count
        onLikeToggle?.(false); 
      } else {
        // Ajouter un like
        await supabase.from("likes").insert({
          resource_type: resourceType,
          resource_id: resourceId,
          user_id: userId,
        });

        setIsLiked(true);
         // Call callback instead of updating internal count
        onLikeToggle?.(true);
      }
    } catch (error) {
      console.error("Erreur lors de l'action like:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue. Veuillez réessayer.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10",
    lg: "h-12 w-12",
  }

  return (
    <Button
      variant={isLiked ? "default" : "outline"}
      size="icon"
      onClick={handleLike}
      disabled={isLoading || !userId} // Disable if loading or not logged in
      className={`transition-colors ${isLiked ? 'text-red-500 border-red-500 hover:bg-red-500/10' : ''} ${sizeClasses[size]} ${className || ''}`} // Merge with existing classes
    >
      <Heart className={`h-5 w-5 ${isLiked ? 'fill-current' : ''}`} />
      <span className="sr-only">{isLiked ? "Unlike" : "Like"}</span>
    </Button>
  )
}
