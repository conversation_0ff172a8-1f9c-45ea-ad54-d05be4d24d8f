import { createSupabaseServerClient } from "@/lib/supabase/server"
import { notFound } from "next/navigation"
import { formatDistanceToNow, format } from "date-fns"
import { fr } from "date-fns/locale"
import { Play, Heart, Share, MoreHorizontal, Clock, Disc, PlayCircle } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { LikeButton } from "@/components/social/like-button"
import { CommentSection } from "@/components/social/comment-section"
// import { genres } from "@/lib/constants/genres"; // Assuming this should be genreOptions
import { genreOptions, moodOptions, instrumentationOptions, albumTypeOptions } from "@/lib/constants/song-options";


// Add a type guard for profile objects
function isProfile(obj: any): obj is { display_name?: string; username?: string } {
  return obj && typeof obj === 'object' && (
    (typeof obj.display_name === 'string' || typeof obj.username === 'string')
  );
}

export default async function AlbumPage({ params }: { params: { id: string } }) {
  const supabase = await createSupabaseServerClient()

  // Récupérer l'utilisateur connecté (sera null si non connecté)
  const { data: { user } } = await supabase.auth.getUser()

  // Récupérer les données de l'album
  const { data: album, error } = await supabase
    .from("albums")
    .select(`
      *,
      profiles:user_id (id, username, display_name, avatar_url)
    `)
    .eq("id", params.id)
    .single()

  if (error || !album) {
    notFound()
  }

  // Récupérer les morceaux de l'album
  const { data: songs } = await supabase
    .from("songs")
    .select("*")
    .eq("album_id", params.id)
    .order("created_at", { ascending: true })

  // Récupérer les tags associés à l'album
  const { data: resourceTags } = await supabase
    .from("resource_tags")
    .select("tags(name)")
    .eq("resource_type", "album")
    .eq("resource_id", params.id)

  const tags = resourceTags?.map((rt) => (rt.tags as any).name) || []

  // Récupérer les commentaires
  const { data: comments } = await supabase
    .from("comments")
    .select(`
      *,
      profiles:user_id (username, display_name, avatar_url)
    `)
    .eq("resource_type", "album")
    .eq("resource_id", params.id)
    .order("created_at", { ascending: false })

  // Récupérer le nombre de likes
  const { count: likesCount } = await supabase
    .from("likes")
    .select("*", { count: "exact", head: true })
    .eq("resource_type", "album")
    .eq("resource_id", params.id)

  // Récupérer le nombre de vues
  const { count: viewsCount } = await supabase
    .from("views")
    .select("*", { count: "exact", head: true })
    .eq("resource_type", "album")
    .eq("resource_id", params.id)

  // Formater la date
  const formatDate = (date: string) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true, locale: fr })
  }

  // Formater la durée
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  // Formater les genres, moods, instrumentation
  const formatArrayField = (fieldValues: string[] | string | null, optionsArray: {value: string, label: string}[]) => {
    if (!fieldValues) return [];
    let values: string[];
    if (Array.isArray(fieldValues)) {
      values = fieldValues;
    } else if (typeof fieldValues === 'string') {
      values = fieldValues.split(",").map(s => s.trim()).filter(Boolean); // Handle old comma-separated string data
    } else {
      return [];
    }
    return values.map((value) => {
      const option = optionsArray.find((opt) => opt.value === value);
      return option ? option.label : value;
    });
  };

  const displayGenres = formatArrayField(album.genre, genreOptions);
  const displayMoods = formatArrayField(album.moods, moodOptions);
  const displayInstrumentation = formatArrayField(album.instrumentation, instrumentationOptions);


  // Calculer la durée totale de l'album
  const totalDuration = songs?.reduce((total, song) => total + (song.duration || 0), 0) || 0
  const formattedTotalDuration = () => {
    const minutes = Math.floor(totalDuration / 60)
    const seconds = totalDuration % 60
    return `${minutes} min ${seconds} sec`
  }

  const artistName = (() => {
    const profiles = album.profiles;
    if (Array.isArray(profiles)) {
      const first = profiles.find(isProfile);
      if (first) {
        return first.display_name || first.username || '';
      }
      return '';
    } else if (isProfile(profiles)) {
      return profiles.display_name || profiles.username || '';
    }
    return '';
  })()

  // Récupérer des albums similaires
  const { data: similarAlbums } = await supabase
    .from("albums")
    .select(`
      id,
      title,
      cover_url,
      profiles:user_id (username, display_name)
    `)
    .eq("genre", album.genre)
    .neq("id", params.id)
    .eq("status", "published")
    .limit(4)

  return (
    <div className="min-h-screen bg-gradient-to-b from-background/80 to-background">
      <div className="container py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            <div className="flex flex-col md:flex-row gap-6 mb-8">
              <div className="w-full md:w-64 aspect-square rounded-md overflow-hidden bg-muted flex-shrink-0">
                {album.cover_url ? (
                  <img
                    src={album.cover_url || "/placeholder.svg"}
                    alt={album.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-primary/10">
                    <Disc className="h-16 w-16 text-primary/40" />
                  </div>
                )}
              </div>

              <div className="flex-1">
              <div className="flex flex-wrap items-center gap-2 mb-2">
                {album.album_type && <Badge variant="default" className="text-xs">{albumTypeOptions.find(at => at.value === album.album_type)?.label || album.album_type}</Badge>}
                {displayGenres.length > 0 ? (
                  displayGenres.map((genre) => (
                    <Badge key={genre} variant="outline">{genre}</Badge>
                  ))
                ) : (
                  <Badge variant="outline">Non classé</Badge> 
                )}
              </div>

              <h1 className="text-3xl md:text-4xl font-bold mb-2">{album.title}</h1>
                <div className="flex items-center gap-2 mb-4">
                  <span className="text-sm text-muted-foreground">
                    Auteur : {album.author || (album.profiles?.username ?? "Artiste")}
                  </span>
                </div>

                <div className="flex items-center gap-2 mb-4">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={album.profiles?.avatar_url || "/placeholder.svg"} />
                    <AvatarFallback>{artistName.charAt(0).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <span className="font-medium">{artistName}</span>
                </div>

                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
                  <span>{formatDate(album.created_at)}</span>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{formattedTotalDuration()}</span>
                  </div>
                  <span>•</span>
                  <span>{songs?.length || 0} morceaux</span>
                </div>

                <div className="flex flex-wrap gap-2 mb-6">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center gap-2">
                  <Button size="lg" className="gap-2">
                    <Play className="h-4 w-4" />
                    Écouter
                  </Button>
                  <LikeButton resourceId={album.id} resourceType="album" initialLikes={likesCount || 0} userId={user?.id} />
                  <Button variant="outline" size="icon">
                    <Share className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <Tabs defaultValue="tracks" className="mt-8">
              <TabsList>
                <TabsTrigger value="tracks">Morceaux</TabsTrigger>
                <TabsTrigger value="about">À propos</TabsTrigger>
                <TabsTrigger value="comments">Commentaires ({comments?.length || 0})</TabsTrigger>
              </TabsList>

              <TabsContent value="tracks" className="mt-4">
                {songs && songs.length > 0 ? (
                  <div className="space-y-2">
                    {songs.map((song, index) => (
                      <div
                        key={song.id}
                        className="flex items-center justify-between p-3 rounded-md hover:bg-muted/50 group"
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-6 text-center text-muted-foreground">{index + 1}</div>
                          <div className="relative">
                            <div className="w-10 h-10 rounded bg-muted flex items-center justify-center">
                              {song.cover_url ? (
                                <img
                                  src={song.cover_url || "/placeholder.svg"}
                                  alt={song.title}
                                  className="w-full h-full object-cover rounded"
                                />
                              ) : (
                                <Disc className="h-5 w-5 text-muted-foreground" />
                              )}
                            </div>
                            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                              <PlayCircle className="h-6 w-6 text-primary cursor-pointer" />
                            </div>
                          </div>
                          <div>
                            <div className="font-medium">{song.title}</div>
                            <div className="text-xs text-muted-foreground">{artistName}</div>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">{formatDuration(song.duration || 0)}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">Aucun morceau disponible pour cet album.</div>
                )}
              </TabsContent>

              <TabsContent value="about" className="mt-4">
                {album.description ? (
                  <div className="prose prose-invert max-w-none">
                    <p>{album.description}</p>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucune description disponible pour cet album.
                  </div>
                )}

                <div className="mt-6 grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Informations</h3>
                    <dl className="space-y-2">
                      {displayGenres.length > 0 && (
                        <div className="flex justify-between">
                          <dt className="text-sm text-muted-foreground">Genres</dt>
                          <dd className="text-sm font-medium text-right">{displayGenres.join(', ')}</dd>
                        </div>
                      )}
                      {displayMoods.length > 0 && (
                        <div className="flex justify-between">
                          <dt className="text-sm text-muted-foreground">Ambiances</dt>
                          <dd className="text-sm font-medium text-right">{displayMoods.join(', ')}</dd>
                        </div>
                      )}
                      {displayInstrumentation.length > 0 && (
                        <div className="flex justify-between">
                          <dt className="text-sm text-muted-foreground">Instrumentation</dt>
                          <dd className="text-sm font-medium text-right">{displayInstrumentation.join(', ')}</dd>
                        </div>
                      )}
                      {album.album_type && (
                        <div className="flex justify-between">
                          <dt className="text-sm text-muted-foreground">Type</dt>
                          <dd className="text-sm font-medium">{albumTypeOptions.find(at => at.value === album.album_type)?.label || album.album_type}</dd>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <dt className="text-sm text-muted-foreground">Label</dt>
                        <dd className="text-sm font-medium">{album.label || "Non spécifié"}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-sm text-muted-foreground">Date de sortie</dt>
                        <dd className="text-sm font-medium">
                          {album.release_date
                            ? format(new Date(album.release_date), "dd MMMM yyyy", { locale: fr })
                            : formatDate(album.created_at)}
                        </dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-sm text-muted-foreground">UPC</dt>
                        <dd className="text-sm font-medium">{album.upc || "Non spécifié"}</dd>
                      </div>
                    </dl>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Artiste</h3>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={album.profiles?.avatar_url || "/placeholder.svg"} />
                        <AvatarFallback>{artistName.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{artistName}</div>
                        <Button variant="link" className="h-auto p-0 text-xs text-muted-foreground">
                          Voir le profil
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="comments" className="mt-4">
                <CommentSection resourceId={album.id} resourceType="album" userId={user?.id} />
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="font-medium mb-4">Albums similaires</h3>
                {similarAlbums && similarAlbums.length > 0 ? (
                  <div className="grid grid-cols-2 gap-4">
                    {similarAlbums.map((album) => (
                      <a key={album.id} href={`/albums/${album.id}`} className="block group">
                        <div className="aspect-square rounded-md overflow-hidden bg-muted mb-2">
                          {album.cover_url ? (
                            <img
                              src={album.cover_url || "/placeholder.svg"}
                              alt={album.title}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-primary/10">
                              <Disc className="h-8 w-8 text-primary/40" />
                            </div>
                          )}
                        </div>
                        <div className="text-sm font-medium truncate">{album.title}</div>
                        <div className="text-xs text-muted-foreground truncate">
                          {(() => {
                            const profiles = album.profiles;
                            if (Array.isArray(profiles)) {
                              const first = profiles.find(isProfile);
                              if (first) {
                                return (first as { display_name?: string; username?: string }).display_name || (first as { display_name?: string; username?: string }).username || '';
                              }
                              return '';
                            } else if (profiles && isProfile(profiles)) {
                              return (profiles as { display_name?: string; username?: string }).display_name || (profiles as { display_name?: string; username?: string }).username || '';
                            }
                            return '';
                          })()}
                        </div>
                      </a>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-muted-foreground">Aucun album similaire trouvé.</div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="font-medium mb-4">Statistiques</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Écoutes</span>
                    <span className="font-medium">0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Likes</span>
                    <span className="font-medium">{likesCount || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Vues</span>
                    <span className="font-medium">{viewsCount || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Commentaires</span>
                    <span className="font-medium">{comments?.length || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="font-medium mb-4">Actions</h3>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start">
                    <Share className="mr-2 h-4 w-4" />
                    Partager l'album
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Heart className="mr-2 h-4 w-4" />
                    Ajouter aux favoris
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
