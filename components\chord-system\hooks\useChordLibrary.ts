/**
 * 🎼 HOOK CHORD LIBRARY - Gestion Bibliothèque d'Accords
 * 
 * Hook spécialisé pour charger et gérer les bibliothèques d'accords
 * Optimisé avec cache intelligent et chargement progressif
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useChordSystem } from '../providers/ChordSystemProvider';
import { ChordDataManager } from '../utils/ChordDataManager';
import type {
  UnifiedChordPosition,
  ChordJsonDefinition,
  InstrumentType,
  ChordSearchFilters,
  ChordSearchResult
} from '../types/chord-system';

// ============================================================================
// TYPES POUR LE HOOK
// ============================================================================

interface UseChordLibraryOptions {
  /** Précharger les instruments populaires */
  preloadPopular?: boolean;
  /** Taille du cache (nombre d'instruments) */
  cacheSize?: number;
  /** Délai de debounce pour la recherche (ms) */
  searchDebounce?: number;
}

interface UseChordLibraryReturn {
  // Données
  chords: UnifiedChordPosition[];
  filteredChords: UnifiedChordPosition[];
  totalChords: number;
  
  // État
  loading: boolean;
  error: string | null;
  isInstrumentLoaded: (instrument: InstrumentType) => boolean;
  
  // Actions
  loadInstrument: (instrument: InstrumentType, tuning?: string) => Promise<void>;
  searchChords: (filters: ChordSearchFilters) => Promise<ChordSearchResult[]>;
  getChordsByKey: (key: string) => UnifiedChordPosition[];
  getChordsByDifficulty: (difficulty: 'beginner' | 'intermediate' | 'advanced') => UnifiedChordPosition[];
  
  // Cache
  clearCache: () => void;
  getCacheStats: () => { size: number; instruments: string[] };
}

// ============================================================================
// UTILITAIRES INTERNES
// ============================================================================

/**
 * Convertit les données JSON en accords unifiés
 */
function convertJsonToUnifiedChords(
  jsonData: ChordJsonDefinition,
  instrument: InstrumentType,
  tuning: string
): UnifiedChordPosition[] {
  const chords: UnifiedChordPosition[] = [];
  
  try {
    for (const [key, variations] of Object.entries(jsonData.chords || {})) {
      for (const variation of variations) {
        for (const position of variation.positions || []) {
          const unified: UnifiedChordPosition = {
            id: crypto.randomUUID(),
            chord: `${key}${variation.suffix}`,
            instrument,
            tuning,
            frets: position.frets,
            fingers: position.fingers,
            baseFret: position.baseFret || 1,
            barres: position.barres,
            midi: position.midi,
            difficulty: position.difficulty || 'intermediate',
            category: getCategoryFromSuffix(variation.suffix),
            tags: [key, variation.suffix, instrument, tuning],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          
          chords.push(unified);
        }
      }
    }
  } catch (error) {
    console.error('Erreur conversion JSON vers accords unifiés:', error);
  }
  
  return chords;
}

/**
 * Détermine la catégorie d'un accord à partir du suffixe
 */
function getCategoryFromSuffix(suffix: string): string {
  const lowerSuffix = suffix.toLowerCase();
  
  if (lowerSuffix.includes('major') || lowerSuffix === '') return 'major';
  if (lowerSuffix.includes('minor') || lowerSuffix.includes('m')) return 'minor';
  if (lowerSuffix.includes('7')) return '7th';
  if (lowerSuffix.includes('sus')) return 'suspended';
  if (lowerSuffix.includes('dim')) return 'diminished';
  if (lowerSuffix.includes('aug')) return 'augmented';
  if (lowerSuffix.includes('9') || lowerSuffix.includes('11') || lowerSuffix.includes('13')) return 'extended';
  
  return 'altered';
}

/**
 * Filtre les accords selon les critères
 */
function filterChords(
  chords: UnifiedChordPosition[],
  filters: ChordSearchFilters
): UnifiedChordPosition[] {
  return chords.filter(chord => {
    // Filtre par instrument
    if (filters.instrument && chord.instrument !== filters.instrument) {
      return false;
    }
    
    // Filtre par accordage
    if (filters.tuning && chord.tuning !== filters.tuning) {
      return false;
    }
    
    // Filtre par tonalité
    if (filters.key) {
      const chordKey = chord.chord.replace(/[^A-G#b]/g, '');
      if (chordKey !== filters.key) {
        return false;
      }
    }
    
    // Filtre par difficulté
    if (filters.difficulty && filters.difficulty !== 'all' && chord.difficulty !== filters.difficulty) {
      return false;
    }
    
    // Filtre par catégorie
    if (filters.category && chord.category !== filters.category) {
      return false;
    }
    
    // Filtre par audio
    if (filters.hasAudio && (!chord.midi || chord.midi.length === 0)) {
      return false;
    }
    
    // Filtre par terme de recherche
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      const searchableText = [
        chord.chord,
        chord.instrument,
        chord.tuning,
        chord.difficulty,
        chord.category,
        ...(chord.tags || [])
      ].join(' ').toLowerCase();
      
      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }
    
    // Filtre par tags
    if (filters.tags && filters.tags.length > 0) {
      const chordTags = chord.tags || [];
      const hasAllTags = filters.tags.every(tag => 
        chordTags.some(chordTag => chordTag.toLowerCase().includes(tag.toLowerCase()))
      );
      if (!hasAllTags) {
        return false;
      }
    }
    
    return true;
  });
}

/**
 * Calcule le score de pertinence pour la recherche
 */
function calculateRelevance(chord: UnifiedChordPosition, filters: ChordSearchFilters): number {
  let score = 0;
  
  // Score de base
  score += 10;
  
  // Bonus pour correspondance exacte du nom
  if (filters.searchTerm && chord.chord.toLowerCase() === filters.searchTerm.toLowerCase()) {
    score += 50;
  }
  
  // Bonus pour correspondance partielle du nom
  if (filters.searchTerm && chord.chord.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
    score += 25;
  }
  
  // Bonus pour instrument sélectionné
  if (chord.instrument === filters.instrument) {
    score += 20;
  }
  
  // Bonus pour accordage sélectionné
  if (chord.tuning === filters.tuning) {
    score += 15;
  }
  
  // Bonus pour difficulté appropriée
  if (chord.difficulty === 'beginner') score += 10;
  if (chord.difficulty === 'intermediate') score += 5;
  
  // Bonus pour présence de données MIDI
  if (chord.midi && chord.midi.length > 0) {
    score += 5;
  }
  
  return score;
}

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

export function useChordLibrary(options: UseChordLibraryOptions = {}): UseChordLibraryReturn {
  const {
    preloadPopular = true,
    cacheSize = 10,
    searchDebounce = 300
  } = options;
  
  const { state, actions } = useChordSystem();
  
  // État local
  const [chords, setChords] = useState<UnifiedChordPosition[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // ============================================================================
  // FONCTIONS PRINCIPALES
  // ============================================================================
  
  /**
   * Charge un instrument avec son accordage
   */
  const loadInstrument = useCallback(async (
    instrument: InstrumentType, 
    tuning?: string
  ): Promise<void> => {
    const targetTuning = tuning || state.selectedTuning;
    const cacheKey = `${instrument}-${targetTuning}`;
    
    // Vérifier le cache
    if (state.chordCache.has(cacheKey)) {
      const cachedChords = state.chordCache.get(cacheKey)!;
      setChords(prev => {
        const filtered = prev.filter(c => !(c.instrument === instrument && c.tuning === targetTuning));
        return [...filtered, ...cachedChords];
      });
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Charger les données JSON
      const jsonData = await ChordDataManager.loadInstrument(instrument, targetTuning);
      
      // Convertir en accords unifiés
      const unifiedChords = convertJsonToUnifiedChords(jsonData, instrument, targetTuning);
      
      // Mettre à jour le cache
      actions.setChordCache?.(cacheKey, unifiedChords);
      
      // Mettre à jour l'état
      setChords(prev => {
        const filtered = prev.filter(c => !(c.instrument === instrument && c.tuning === targetTuning));
        return [...filtered, ...unifiedChords];
      });
      
      // Marquer l'instrument comme chargé
      if (state.loadedInstruments) {
        state.loadedInstruments.add(cacheKey);
      }
      
    } catch (err) {
      const errorMessage = `Erreur chargement ${instrument}: ${err}`;
      setError(errorMessage);
      console.error(errorMessage, err);
    } finally {
      setLoading(false);
    }
  }, [state.selectedTuning, state.chordCache, state.loadedInstruments, actions]);
  
  /**
   * Recherche d'accords avec filtres
   */
  const searchChords = useCallback(async (
    filters: ChordSearchFilters
  ): Promise<ChordSearchResult[]> => {
    try {
      setLoading(true);
      
      // S'assurer que l'instrument est chargé
      if (filters.instrument && !isInstrumentLoaded(filters.instrument)) {
        await loadInstrument(filters.instrument, filters.tuning);
      }
      
      // Filtrer les accords
      const filtered = filterChords(chords, filters);
      
      // Calculer la pertinence et trier
      const results: ChordSearchResult[] = filtered.map(chord => ({
        chord,
        relevance: calculateRelevance(chord, filters),
        matchedFields: [] // TODO: Implémenter la détection des champs matchés
      })).sort((a, b) => b.relevance - a.relevance);
      
      return results;
      
    } catch (err) {
      setError(`Erreur recherche: ${err}`);
      return [];
    } finally {
      setLoading(false);
    }
  }, [chords, loadInstrument]);
  
  /**
   * Obtient les accords par tonalité
   */
  const getChordsByKey = useCallback((key: string): UnifiedChordPosition[] => {
    return chords.filter(chord => {
      const chordKey = chord.chord.replace(/[^A-G#b]/g, '');
      return chordKey === key;
    });
  }, [chords]);
  
  /**
   * Obtient les accords par difficulté
   */
  const getChordsByDifficulty = useCallback((
    difficulty: 'beginner' | 'intermediate' | 'advanced'
  ): UnifiedChordPosition[] => {
    return chords.filter(chord => chord.difficulty === difficulty);
  }, [chords]);
  
  /**
   * Vérifie si un instrument est chargé
   */
  const isInstrumentLoaded = useCallback((instrument: InstrumentType): boolean => {
    const key = `${instrument}-${state.selectedTuning}`;
    return state.loadedInstruments?.has(key) || false;
  }, [state.loadedInstruments, state.selectedTuning]);
  
  /**
   * Vide le cache
   */
  const clearCache = useCallback(() => {
    setChords([]);
    actions.clearChordCache?.();
  }, [actions]);
  
  /**
   * Statistiques du cache
   */
  const getCacheStats = useCallback(() => {
    const instruments = Array.from(state.loadedInstruments || []);
    return {
      size: state.chordCache?.size || 0,
      instruments
    };
  }, [state.loadedInstruments, state.chordCache]);
  
  // ============================================================================
  // EFFETS
  // ============================================================================
  
  // Chargement initial de l'instrument sélectionné
  useEffect(() => {
    if (state.selectedInstrument && !isInstrumentLoaded(state.selectedInstrument)) {
      loadInstrument(state.selectedInstrument, state.selectedTuning);
    }
  }, [state.selectedInstrument, state.selectedTuning, isInstrumentLoaded, loadInstrument]);
  
  // Préchargement des instruments populaires
  useEffect(() => {
    if (preloadPopular) {
      const popularInstruments: InstrumentType[] = ['guitar', 'piano', 'ukulele'];
      
      popularInstruments.forEach(instrument => {
        if (!isInstrumentLoaded(instrument)) {
          // Chargement en arrière-plan sans bloquer l'UI
          setTimeout(() => {
            loadInstrument(instrument);
          }, 1000);
        }
      });
    }
  }, [preloadPopular, isInstrumentLoaded, loadInstrument]);
  
  // ============================================================================
  // VALEURS CALCULÉES
  // ============================================================================
  
  const filteredChords = useMemo(() => {
    return filterChords(chords, state.searchFilters);
  }, [chords, state.searchFilters]);
  
  const totalChords = useMemo(() => {
    return chords.length;
  }, [chords]);
  
  // ============================================================================
  // RETOUR DU HOOK
  // ============================================================================
  
  return {
    // Données
    chords,
    filteredChords,
    totalChords,
    
    // État
    loading,
    error,
    isInstrumentLoaded,
    
    // Actions
    loadInstrument,
    searchChords,
    getChordsByKey,
    getChordsByDifficulty,
    
    // Cache
    clearCache,
    getCacheStats
  };
}
