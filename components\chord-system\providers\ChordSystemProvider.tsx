/**
 * 🎼 CHORD SYSTEM PROVIDER - État Centralisé
 * 
 * Provider principal pour le système d'accords unifié
 * Gestion d'état centralisée, performante et type-safe
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { 
  createContext, 
  useContext, 
  useReducer, 
  useCallback, 
  useMemo, 
  useEffect,
  ReactNode 
} from 'react';

import type {
  ChordSystemState,
  ChordSystemActions,
  UnifiedChordPosition,
  ChordProgression,
  ChordGridSection,
  ChordSearchFilters,
  InstrumentType,
  PlaybackOptions,
  ChordPlacement
} from '../types/chord-system';

import { SUPPORTED_INSTRUMENTS, DEFAULT_TUNINGS } from '../types/chord-system';

// ============================================================================
// TYPES POUR LE REDUCER
// ============================================================================

type ChordSystemAction =
  // Configuration
  | { type: 'SET_INSTRUMENT'; payload: InstrumentType }
  | { type: 'SET_TUNING'; payload: string }
  | { type: 'SET_AVAILABLE_INSTRUMENTS'; payload: typeof SUPPORTED_INSTRUMENTS }
  
  // Accords et sélection
  | { type: 'SELECT_CHORD'; payload: UnifiedChordPosition }
  | { type: 'ADD_TO_PROGRESSION'; payload: UnifiedChordPosition }
  | { type: 'REMOVE_FROM_PROGRESSION'; payload: string }
  | { type: 'REORDER_PROGRESSION'; payload: { fromIndex: number; toIndex: number } }
  | { type: 'CLEAR_SELECTED_CHORDS' }
  
  // Progressions
  | { type: 'SET_CURRENT_PROGRESSION'; payload: ChordProgression | null }
  | { type: 'SET_SAVED_PROGRESSIONS'; payload: ChordProgression[] }
  | { type: 'ADD_SAVED_PROGRESSION'; payload: ChordProgression }
  | { type: 'REMOVE_SAVED_PROGRESSION'; payload: string }
  
  // Interface utilisateur
  | { type: 'SET_PLAYING'; payload: boolean }
  | { type: 'SET_CURRENT_CHORD'; payload: UnifiedChordPosition | null }
  | { type: 'SET_SEARCH_FILTERS'; payload: Partial<ChordSearchFilters> }
  | { type: 'RESET_SEARCH_FILTERS' }
  
  // Grille de composition
  | { type: 'SET_SONG_SECTIONS'; payload: ChordGridSection[] }
  | { type: 'ADD_SONG_SECTION'; payload: ChordGridSection }
  | { type: 'UPDATE_SONG_SECTION'; payload: { id: string; updates: Partial<ChordGridSection> } }
  | { type: 'REMOVE_SONG_SECTION'; payload: string }
  | { type: 'SET_SELECTED_SECTION'; payload: string | null }
  | { type: 'ADD_CHORD_TO_MEASURE'; payload: { sectionId: string; measureId: string; chord: ChordPlacement } }
  
  // Audio
  | { type: 'SET_VOLUME'; payload: number }
  | { type: 'SET_MUTED'; payload: boolean }
  | { type: 'SET_PLAY_MODE'; payload: 'chord' | 'arpeggio' }
  
  // Cache et performance
  | { type: 'ADD_LOADED_INSTRUMENT'; payload: string }
  | { type: 'SET_CHORD_CACHE'; payload: { key: string; chords: UnifiedChordPosition[] } }
  | { type: 'CLEAR_CHORD_CACHE' }
  
  // État système
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' }
  | { type: 'RESET_STATE' };

// ============================================================================
// ÉTAT INITIAL
// ============================================================================

const createInitialState = (): ChordSystemState => ({
  // Configuration actuelle
  selectedInstrument: 'guitar',
  selectedTuning: 'standard',
  availableInstruments: Object.values(SUPPORTED_INSTRUMENTS),
  
  // Accords et progressions
  selectedChords: [],
  currentProgression: null,
  savedProgressions: [],
  
  // Interface utilisateur
  isPlaying: false,
  currentChord: null,
  searchFilters: {
    instrument: 'guitar',
    tuning: 'standard',
    difficulty: 'all',
    hasAudio: false,
    searchTerm: ''
  },
  
  // Grille de composition
  songSections: [],
  selectedSection: null,
  
  // Audio
  volume: 0.7,
  isMuted: false,
  playMode: 'chord',
  
  // Cache et performance
  loadedInstruments: new Set(['guitar']), // Guitare chargée par défaut
  chordCache: new Map(),
  
  // Erreurs et chargement
  loading: false,
  error: null
});

// ============================================================================
// REDUCER PRINCIPAL
// ============================================================================

function chordSystemReducer(state: ChordSystemState, action: ChordSystemAction): ChordSystemState {
  switch (action.type) {
    // Configuration
    case 'SET_INSTRUMENT':
      return {
        ...state,
        selectedInstrument: action.payload,
        selectedTuning: DEFAULT_TUNINGS[action.payload],
        searchFilters: {
          ...state.searchFilters,
          instrument: action.payload,
          tuning: DEFAULT_TUNINGS[action.payload]
        }
      };
      
    case 'SET_TUNING':
      return {
        ...state,
        selectedTuning: action.payload,
        searchFilters: {
          ...state.searchFilters,
          tuning: action.payload
        }
      };
      
    case 'SET_AVAILABLE_INSTRUMENTS':
      return {
        ...state,
        availableInstruments: Object.values(action.payload)
      };
    
    // Accords et sélection
    case 'SELECT_CHORD':
      return {
        ...state,
        currentChord: action.payload
      };
      
    case 'ADD_TO_PROGRESSION':
      return {
        ...state,
        selectedChords: [...state.selectedChords, action.payload],
        currentProgression: state.currentProgression ? {
          ...state.currentProgression,
          chords: [...state.currentProgression.chords, action.payload],
          updatedAt: new Date().toISOString()
        } : null
      };
      
    case 'REMOVE_FROM_PROGRESSION':
      return {
        ...state,
        selectedChords: state.selectedChords.filter(chord => chord.id !== action.payload),
        currentProgression: state.currentProgression ? {
          ...state.currentProgression,
          chords: state.currentProgression.chords.filter(chord => chord.id !== action.payload),
          updatedAt: new Date().toISOString()
        } : null
      };
      
    case 'REORDER_PROGRESSION':
      const { fromIndex, toIndex } = action.payload;
      const newSelectedChords = [...state.selectedChords];
      const [movedChord] = newSelectedChords.splice(fromIndex, 1);
      newSelectedChords.splice(toIndex, 0, movedChord);
      
      return {
        ...state,
        selectedChords: newSelectedChords,
        currentProgression: state.currentProgression ? {
          ...state.currentProgression,
          chords: newSelectedChords,
          updatedAt: new Date().toISOString()
        } : null
      };
      
    case 'CLEAR_SELECTED_CHORDS':
      return {
        ...state,
        selectedChords: [],
        currentProgression: state.currentProgression ? {
          ...state.currentProgression,
          chords: [],
          updatedAt: new Date().toISOString()
        } : null
      };
    
    // Progressions
    case 'SET_CURRENT_PROGRESSION':
      return {
        ...state,
        currentProgression: action.payload,
        selectedChords: action.payload?.chords || []
      };
      
    case 'SET_SAVED_PROGRESSIONS':
      return {
        ...state,
        savedProgressions: action.payload
      };
      
    case 'ADD_SAVED_PROGRESSION':
      return {
        ...state,
        savedProgressions: [...state.savedProgressions, action.payload]
      };
      
    case 'REMOVE_SAVED_PROGRESSION':
      return {
        ...state,
        savedProgressions: state.savedProgressions.filter(p => p.id !== action.payload)
      };
    
    // Interface utilisateur
    case 'SET_PLAYING':
      return {
        ...state,
        isPlaying: action.payload
      };
      
    case 'SET_CURRENT_CHORD':
      return {
        ...state,
        currentChord: action.payload
      };
      
    case 'SET_SEARCH_FILTERS':
      return {
        ...state,
        searchFilters: {
          ...state.searchFilters,
          ...action.payload
        }
      };
      
    case 'RESET_SEARCH_FILTERS':
      return {
        ...state,
        searchFilters: {
          instrument: state.selectedInstrument,
          tuning: state.selectedTuning,
          difficulty: 'all',
          hasAudio: false,
          searchTerm: ''
        }
      };
    
    // Grille de composition
    case 'SET_SONG_SECTIONS':
      return {
        ...state,
        songSections: action.payload
      };
      
    case 'ADD_SONG_SECTION':
      return {
        ...state,
        songSections: [...state.songSections, action.payload]
      };
      
    case 'UPDATE_SONG_SECTION':
      return {
        ...state,
        songSections: state.songSections.map(section =>
          section.id === action.payload.id
            ? { ...section, ...action.payload.updates }
            : section
        )
      };
      
    case 'REMOVE_SONG_SECTION':
      return {
        ...state,
        songSections: state.songSections.filter(section => section.id !== action.payload),
        selectedSection: state.selectedSection === action.payload ? null : state.selectedSection
      };
      
    case 'SET_SELECTED_SECTION':
      return {
        ...state,
        selectedSection: action.payload
      };
      
    case 'ADD_CHORD_TO_MEASURE':
      return {
        ...state,
        songSections: state.songSections.map(section => {
          if (section.id !== action.payload.sectionId) return section;
          
          return {
            ...section,
            measures: section.measures.map(measure => {
              if (measure.id !== action.payload.measureId) return measure;
              
              return {
                ...measure,
                chords: [...measure.chords, action.payload.chord]
              };
            })
          };
        })
      };
    
    // Audio
    case 'SET_VOLUME':
      return {
        ...state,
        volume: Math.max(0, Math.min(1, action.payload))
      };
      
    case 'SET_MUTED':
      return {
        ...state,
        isMuted: action.payload
      };
      
    case 'SET_PLAY_MODE':
      return {
        ...state,
        playMode: action.payload
      };
    
    // Cache et performance
    case 'ADD_LOADED_INSTRUMENT':
      return {
        ...state,
        loadedInstruments: new Set([...state.loadedInstruments, action.payload])
      };
      
    case 'SET_CHORD_CACHE':
      const newCache = new Map(state.chordCache);
      newCache.set(action.payload.key, action.payload.chords);
      return {
        ...state,
        chordCache: newCache
      };
      
    case 'CLEAR_CHORD_CACHE':
      return {
        ...state,
        chordCache: new Map()
      };
    
    // État système
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
      
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        loading: false
      };
      
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      };
      
    case 'RESET_STATE':
      return createInitialState();
    
    default:
      return state;
  }
}

// ============================================================================
// CONTEXT ET PROVIDER
// ============================================================================

interface ChordSystemContextValue {
  state: ChordSystemState;
  actions: ChordSystemActions;
}

const ChordSystemContext = createContext<ChordSystemContextValue | null>(null);

interface ChordSystemProviderProps {
  children: ReactNode;
  initialState?: Partial<ChordSystemState>;
}

export function ChordSystemProvider({ children, initialState }: ChordSystemProviderProps) {
  const [state, dispatch] = useReducer(
    chordSystemReducer,
    initialState ? { ...createInitialState(), ...initialState } : createInitialState()
  );

  // ============================================================================
  // ACTIONS MEMOIZED
  // ============================================================================

  const actions = useMemo<ChordSystemActions>(() => ({
    // Configuration
    selectInstrument: (instrument: InstrumentType) => {
      dispatch({ type: 'SET_INSTRUMENT', payload: instrument });
    },
    
    selectTuning: (tuning: string) => {
      dispatch({ type: 'SET_TUNING', payload: tuning });
    },
    
    // Gestion des accords
    selectChord: (chord: UnifiedChordPosition) => {
      dispatch({ type: 'SELECT_CHORD', payload: chord });
    },
    
    addToProgression: (chord: UnifiedChordPosition) => {
      dispatch({ type: 'ADD_TO_PROGRESSION', payload: chord });
    },
    
    removeFromProgression: (chordId: string) => {
      dispatch({ type: 'REMOVE_FROM_PROGRESSION', payload: chordId });
    },
    
    reorderProgression: (fromIndex: number, toIndex: number) => {
      dispatch({ type: 'REORDER_PROGRESSION', payload: { fromIndex, toIndex } });
    },
    
    // Progressions
    saveProgression: async (progression: ChordProgression) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        // TODO: Implémenter la sauvegarde Supabase
        dispatch({ type: 'ADD_SAVED_PROGRESSION', payload: progression });
        dispatch({ type: 'SET_CURRENT_PROGRESSION', payload: progression });
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: `Erreur sauvegarde: ${error}` });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },
    
    loadProgression: async (progressionId: string) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        // TODO: Implémenter le chargement Supabase
        const progression = state.savedProgressions.find(p => p.id === progressionId);
        if (progression) {
          dispatch({ type: 'SET_CURRENT_PROGRESSION', payload: progression });
        }
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: `Erreur chargement: ${error}` });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },
    
    deleteProgression: async (progressionId: string) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        // TODO: Implémenter la suppression Supabase
        dispatch({ type: 'REMOVE_SAVED_PROGRESSION', payload: progressionId });
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: `Erreur suppression: ${error}` });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },
    
    // Grille de composition
    addSection: (section: Omit<ChordGridSection, 'id'>) => {
      const newSection: ChordGridSection = {
        ...section,
        id: crypto.randomUUID()
      };
      dispatch({ type: 'ADD_SONG_SECTION', payload: newSection });
    },
    
    updateSection: (sectionId: string, updates: Partial<ChordGridSection>) => {
      dispatch({ type: 'UPDATE_SONG_SECTION', payload: { id: sectionId, updates } });
    },
    
    deleteSection: (sectionId: string) => {
      dispatch({ type: 'REMOVE_SONG_SECTION', payload: sectionId });
    },
    
    addChordToMeasure: (sectionId: string, measureId: string, chord: ChordPlacement) => {
      dispatch({ type: 'ADD_CHORD_TO_MEASURE', payload: { sectionId, measureId, chord } });
    },
    
    // Audio
    playChord: async (chord: UnifiedChordPosition, options?: PlaybackOptions) => {
      try {
        dispatch({ type: 'SET_PLAYING', payload: true });
        dispatch({ type: 'SET_CURRENT_CHORD', payload: chord });
        // TODO: Implémenter la lecture audio
        await new Promise(resolve => setTimeout(resolve, options?.duration || 2000));
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: `Erreur lecture: ${error}` });
      } finally {
        dispatch({ type: 'SET_PLAYING', payload: false });
      }
    },
    
    playProgression: async (progression: ChordProgression) => {
      try {
        dispatch({ type: 'SET_PLAYING', payload: true });
        // TODO: Implémenter la lecture de progression
        for (const chord of progression.chords) {
          dispatch({ type: 'SET_CURRENT_CHORD', payload: chord });
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: `Erreur lecture progression: ${error}` });
      } finally {
        dispatch({ type: 'SET_PLAYING', payload: false });
        dispatch({ type: 'SET_CURRENT_CHORD', payload: null });
      }
    },
    
    stopPlayback: () => {
      dispatch({ type: 'SET_PLAYING', payload: false });
      dispatch({ type: 'SET_CURRENT_CHORD', payload: null });
    },
    
    setVolume: (volume: number) => {
      dispatch({ type: 'SET_VOLUME', payload: volume });
    },
    
    toggleMute: () => {
      dispatch({ type: 'SET_MUTED', payload: !state.isMuted });
    },
    
    // Recherche
    searchChords: async (filters: ChordSearchFilters) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_SEARCH_FILTERS', payload: filters });
        // TODO: Implémenter la recherche
        return [];
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: `Erreur recherche: ${error}` });
        return [];
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },
    
    getSuggestions: async (context: ChordProgression) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        // TODO: Implémenter les suggestions IA
        return [];
      } catch (error) {
        dispatch({ type: 'SET_ERROR', payload: `Erreur suggestions: ${error}` });
        return [];
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },
    
    // Utilitaires
    clearError: () => {
      dispatch({ type: 'CLEAR_ERROR' });
    },
    
    resetState: () => {
      dispatch({ type: 'RESET_STATE' });
    }
  }), [state.savedProgressions, state.isMuted]);

  // ============================================================================
  // EFFETS
  // ============================================================================

  // Chargement initial des instruments
  useEffect(() => {
    dispatch({ type: 'SET_AVAILABLE_INSTRUMENTS', payload: SUPPORTED_INSTRUMENTS });
  }, []);

  // Auto-clear des erreurs après 5 secondes
  useEffect(() => {
    if (state.error) {
      const timer = setTimeout(() => {
        dispatch({ type: 'CLEAR_ERROR' });
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [state.error]);

  // ============================================================================
  // CONTEXT VALUE MEMOIZED
  // ============================================================================

  const contextValue = useMemo(() => ({
    state,
    actions
  }), [state, actions]);

  return (
    <ChordSystemContext.Provider value={contextValue}>
      {children}
    </ChordSystemContext.Provider>
  );
}

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

/**
 * Hook principal pour accéder au système d'accords
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { state, actions } = useChordSystem();
 *   
 *   return (
 *     <div>
 *       <p>Instrument: {state.selectedInstrument}</p>
 *       <button onClick={() => actions.selectInstrument('piano')}>
 *         Changer pour Piano
 *       </button>
 *     </div>
 *   );
 * }
 * ```
 */
export function useChordSystem(): ChordSystemContextValue {
  const context = useContext(ChordSystemContext);
  
  if (!context) {
    throw new Error('useChordSystem doit être utilisé dans un ChordSystemProvider');
  }
  
  return context;
}
