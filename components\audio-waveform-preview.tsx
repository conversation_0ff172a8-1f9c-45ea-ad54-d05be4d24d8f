"use client";

import React, { useEffect, useRef, useState, useCallback } from 'react';
import WaveSurfer from 'wavesurfer.js';
import { Loader2, Play, Pause, AlertCircle, Volume2, Download as DownloadIcon, VolumeX, Volume1 } from 'lucide-react';
import { useAudio } from '@/contexts/audio-context'; 
import type { Song } from '@/types';
import { Button } from '@/components/ui/button';
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Slider } from "@/components/ui/slider";

interface AudioWaveformPreviewProps {
  audioUrl: string | null | undefined;
  song?: Song; 
  height?: number;
  onReady?: (duration: number) => void;
  allowDownload?: boolean;
}

const AudioWaveformPreviewInternal: React.FC<AudioWaveformPreviewProps> = ({ 
  audioUrl,
  song, 
  height = 80,
  onReady,
  allowDownload = false
}) => {
  const waveformContainerRef = useRef<HTMLDivElement | null>(null);
  const waveformRef = useRef<HTMLDivElement | null>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isWaveSurferReady, setIsWaveSurferReady] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlayingState, setIsPlayingState] = useState(false);
  const [volume, setVolume] = useState(0.75);
  const [isMuted, setIsMuted] = useState(false);
  const [hoverTime, setHoverTime] = useState<string | null>(null);
  const [hoverPositionX, setHoverPositionX] = useState(0);

  const { playSong: globalPlaySong, pauseSong: globalPauseSong, resumeSong: globalResumeSong, currentSong: globalCurrentSong, isPlaying: isGlobalPlaying } = useAudio();

  const canSyncWithGlobalPlayer = !!song;
  const isCurrentGlobalSong = canSyncWithGlobalPlayer && globalCurrentSong?.id === song?.id;

  const formatTime = (seconds: number): string => {
    if (isNaN(seconds) || !isFinite(seconds)) return "0:00";
    const minutes = Math.floor(seconds / 60);
    const secondsRemainder = Math.floor(seconds) % 60;
    const paddedSeconds = `0${secondsRemainder}`.slice(-2);
    return `${minutes}:${paddedSeconds}`;
  };

  useEffect(() => {
    const isValidUrl = typeof audioUrl === 'string' && audioUrl.trim() !== '' && (audioUrl.startsWith('http://') || audioUrl.startsWith('https://') || audioUrl.startsWith('blob:http'));

    if (!waveformRef.current || !isValidUrl) {
      setIsLoading(false);
      setError(audioUrl && !isValidUrl ? 'Audio URL invalide.' : null);
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy();
        wavesurferRef.current = null;
      }
      setIsWaveSurferReady(false);
      setDuration(0); setCurrentTime(0);
      if (onReady) onReady(0);
      return;
    }

    setIsLoading(true); setError(null);
    if (wavesurferRef.current) wavesurferRef.current.destroy();

    const ws = WaveSurfer.create({
      container: waveformRef.current, url: audioUrl, height: height,
      waveColor: 'hsl(var(--muted-foreground) / 0.4)', // Lighter unplayed part
      progressColor: 'hsl(var(--primary))',           // Played part
      cursorColor: 'hsl(var(--primary))',             // Cursor matches played part
      barWidth: 2, 
      barGap: 1, 
      interact: true,
    });
    wavesurferRef.current = ws;
    ws.setVolume(isMuted ? 0 : volume);

    ws.on('ready', () => {
      setIsLoading(false); setIsWaveSurferReady(true);
      const dur = ws.getDuration(); setDuration(dur); setCurrentTime(0);
      if (onReady) onReady(dur);
    });
    ws.on('audioprocess', (time) => setCurrentTime(time));
    ws.on('play', () => setIsPlayingState(true));
    ws.on('pause', () => setIsPlayingState(false));
    ws.on('finish', () => { setIsPlayingState(false); setCurrentTime(0); ws.seekTo(0); });
    ws.on('error', (err) => {
      console.error("Wavesurfer error:", err);
      setError(`Erreur chargement audio: ${typeof err === 'string' ? err : (err as Error).message || 'Inconnue'}`);
      setIsLoading(false); setIsWaveSurferReady(false);
    });
    ws.on('click', (relativeX) => {
      if (wavesurferRef.current) {
        if (!canSyncWithGlobalPlayer || (isCurrentGlobalSong && !isGlobalPlaying)) {
            wavesurferRef.current.play(); 
        } else if (canSyncWithGlobalPlayer && isCurrentGlobalSong && isGlobalPlaying) {
            // Already playing via global, click just seeks.
        } else if (canSyncWithGlobalPlayer && !isCurrentGlobalSong && song) {
            globalPlaySong(song);
        }
      }
    });

    const waveformEl = waveformRef.current;
    const handleMouseMove = (event: MouseEvent) => {
      if (!ws.getDuration() || !waveformEl) return;
      const bbox = waveformEl.getBoundingClientRect();
      const progress = (event.clientX - bbox.left) / bbox.width;
      setHoverTime(formatTime(progress * ws.getDuration()));
      setHoverPositionX(event.clientX - bbox.left);
    };
    const handleMouseLeave = () => setHoverTime(null);
    waveformEl.addEventListener('mousemove', handleMouseMove);
    waveformEl.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      ws.destroy(); wavesurferRef.current = null; setIsWaveSurferReady(false);
      waveformEl?.removeEventListener('mousemove', handleMouseMove);
      waveformEl?.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [audioUrl, height, onReady]);

  useEffect(() => { 
    if (canSyncWithGlobalPlayer && isCurrentGlobalSong && wavesurferRef.current && isWaveSurferReady) {
      if (isGlobalPlaying && !wavesurferRef.current.isPlaying()) wavesurferRef.current.play();
      else if (!isGlobalPlaying && wavesurferRef.current.isPlaying()) wavesurferRef.current.pause();
    } else if (canSyncWithGlobalPlayer && !isCurrentGlobalSong && wavesurferRef.current && wavesurferRef.current.isPlaying()) {
      wavesurferRef.current.pause();
    }
  }, [isGlobalPlaying, isCurrentGlobalSong, canSyncWithGlobalPlayer, isWaveSurferReady, song]);

  const handlePlayPauseClick = useCallback(() => {
    if (!wavesurferRef.current || !isWaveSurferReady) return;
    if (canSyncWithGlobalPlayer && song) {
      if (isCurrentGlobalSong) {
        if (isGlobalPlaying) globalPauseSong(); else globalResumeSong();
      } else {
        globalPlaySong(song); 
      }
    } else {
      wavesurferRef.current.playPause();
    }
  }, [isWaveSurferReady, canSyncWithGlobalPlayer, song, isCurrentGlobalSong, isGlobalPlaying, globalPlaySong, globalPauseSong, globalResumeSong]);

  const handleVolumeChange = (newVolume: number[]) => {
    const vol = newVolume[0] / 100;
    setVolume(vol);
    setIsMuted(vol === 0);
    if (wavesurferRef.current) wavesurferRef.current.setVolume(vol);
  };

  const toggleMute = () => {
    if (!wavesurferRef.current) return;
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    wavesurferRef.current.setVolume(newMuted ? 0 : volume);
  };
  
  const handleDownload = () => {
    if (audioUrl && allowDownload) {
      const link = document.createElement('a');
      link.href = audioUrl;
      link.download = song?.title || 'audio-download'; 
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const displayPlaying = canSyncWithGlobalPlayer && isCurrentGlobalSong ? isGlobalPlaying : isPlayingState;

  return (
    <div className="space-y-2">
      <div 
        ref={waveformContainerRef} 
        className="waveform-container group relative overflow-hidden bg-muted/90 dark:bg-zinc-800/90 backdrop-blur-sm shadow-lg rounded-md" 
        style={{ minHeight: `${height}px` }}
      >
        <div ref={waveformRef} className="w-full h-full" />
        {hoverTime && (
          <div className="absolute pointer-events-none z-20 -top-6 transform -translate-x-1/2 px-1.5 py-0.5 bg-black/70 text-white text-xs rounded shadow" style={{ left: `${hoverPositionX}px`}}>
            {hoverTime}
          </div>
        )}
        {!isLoading && !error && isWaveSurferReady && (
          <div className="absolute top-1/2 left-3 -translate-y-1/2 z-20 flex items-center gap-1">
            <Button variant="ghost" size="icon" onClick={handlePlayPauseClick} className="h-9 w-9 bg-black/50 text-white hover:bg-black/70 rounded-full" aria-label={displayPlaying ? "Pause" : "Play"}>
              {displayPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
            </Button>
          </div>
        )}
        {!isLoading && !error && isWaveSurferReady && (
          <div className="absolute top-1/2 right-3 -translate-y-1/2 z-20 flex items-center gap-1">
            {allowDownload && audioUrl && (
              <Button variant="ghost" size="icon" onClick={handleDownload} className="h-8 w-8 bg-black/40 text-white hover:bg-black/60 rounded-full" title="Télécharger">
                <DownloadIcon className="h-4 w-4" />
              </Button>
            )}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 bg-black/40 text-white hover:bg-black/60 rounded-full" title="Volume">
                  {isMuted ? <VolumeX className="h-4 w-4" /> : volume > 0.5 ? <Volume2 className="h-4 w-4" /> : <Volume1 className="h-4 w-4" />}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-32 p-2">
                <Slider defaultValue={[volume * 100]} max={100} step={1} onValueChange={handleVolumeChange} />
              </PopoverContent>
            </Popover>
            <div className="waveform-time text-xs bg-black/50 text-white px-1.5 py-0.5 rounded tabular-nums">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>
        )}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 rounded-[inherit]">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        )}
        {error && !isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-destructive/80 p-2 rounded-[inherit]">
            <AlertCircle className="h-5 w-5 text-destructive-foreground mr-2"/>
            <p className="text-destructive-foreground text-xs text-center font-medium">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(AudioWaveformPreviewInternal);
