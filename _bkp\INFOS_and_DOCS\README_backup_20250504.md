# MOUVIK - Plateforme Musicale Complète

## Aperçu du Projet

MOUVIK est une plateforme musicale complète qui permet aux artistes de créer, partager et monétiser leur musique. La plateforme offre des fonctionnalités avancées comme la composition assistée par IA, le partage social, et des outils d'édition audio professionnels.

## Fonctionnalités Principales

- **Création Musicale** : Studio d'enregistrement et d'édition intégré
- **Découvrir & Playlists** : Hub d'exploration musicale multi-plateforme (import URL, playlists mixtes, provenance, filtres avancés, gestion intelligente des tags)
- **Communauté** : Mur d'activité, groupes/bands, hashtags, discussions, commentaires, likes, notifications, modération
- **Assistant IA** : Suggestions de composition et d'arrangement par intelligence artificielle
- **Partage Social** : Partage, commentaires et collaboration entre artistes
- **Monétisation** : Abonnements premium et vente directe de musique
- **Analytics** : Statistiques détaillées sur l'audience et les performances
- **Multi-plateforme** : Expérience cohérente sur desktop et mobile

## Structure du Projet

Le projet est construit avec Next.js (App Router) et utilise Supabase (PostgreSQL) pour la base de données, l'authentification et le stockage. L'architecture est modulaire et extensible.

```bash
/app                    # Routes et pages de l'application
  /(dashboard)          # Layout pour les pages du dashboard
  /admin                # Pages d'administration
  /album                # Pages d'albums
  /api                  # Routes API
    /discover           # API pour Découvrir & Playlists (import, preview, gestion playlists, tags)
    /community          # API pour Communauté (posts, commentaires, likes, hashtags, groupes)
  /auth                 # Pages d'authentification
  /playlist             # Pages de playlists
  /pro                  # Pages pour utilisateurs premium
  /profile              # Pages de profil utilisateur
  /track                # Pages de pistes individuelles
  /discover             # Pages pour Découvrir & Playlists (import, feed, détail)
  /community            # Pages pour Communauté (mur, groupes, hashtags)
/components             # Composants réutilisables
  /audio                # Composants audio (player, waveform, import)
  /auth                 # Composants d'authentification
  /comments             # Système de commentaires
  /community            # Composants Communauté (feed, posts, ActivityCard, LikeButton, etc.)
  /discover             # Composants Découvrir (ImportForm, ResourceList, ResourceCard, TagInput, PlaylistManager)
  /music-player         # Lecteur de musique
  /pro                  # Composants premium
  /vault                # Stockage sécurisé de fichiers
/lib                    # Utilitaires et fonctions partagées (extraction métadonnées, gestion tags, création activité)
/public                 # Ressources statiques
/scripts                # Scripts de base de données et d'initialisation
```

## Workflows Clés

- **Import multi-plateforme** : Saisie d'URL → détection plateforme → extraction métadonnées → preview UI (suggestion tags, badges provenance) → validation utilisateur → création ressource (music_resources, resource_tags, activité).
- **Gestion Playlists** : Création/édition, ajout/retrait de ressources internes/externes, gestion droits et ordre, partage.
- **Activité Communauté** : Création posts, commentaires, likes, détection hashtags, feed filtrable, notifications, modération.
- **Gestion avancée des tags** : Centralisation, normalisation, suggestion intelligente, auto-complétion, modération des tags custom.
- **Modération & Sécurité** : Signalement, rôles, historique, soft delete, validation stricte, respect RGPD.

## Roadmap & Documentation

- [ARCHITECTURE.md](./ARCHITECTURE.md) : Architecture technique détaillée (flux, API, tables, relations, UI)
- [GUIDELINES.md](./GUIDELINES.md) : Directives de développement (conventions, sécurité, UI/UX, workflows spécifiques)
- [TASKS.md](./TASKS.md) : Liste priorisée des tâches pour chaque module
- [ROADMAP.md](./ROADMAP.md) : Phases de développement et jalons

## Technologies Utilisées

- **Frontend** : Next.js 14, React, Tailwind CSS
- **Backend** : Next.js API Routes, Server Actions
- **Base de données** : Supabase (PostgreSQL)
- **Authentification** : Supabase Auth
- **Stockage** : Supabase Storage
- **Déploiement** : Vercel

## Configuration

Voir [GUIDELINES.md](./GUIDELINES.md) et [ARCHITECTURE.md](./ARCHITECTURE.md) pour la configuration des variables d'environnement (Supabase, OAuth, etc.) et les bonnes pratiques de sécurité.

### Prérequis

- Node.js 18+
- Compte Supabase
- Variables d'environnement configurées

### Variables d'Environnement

Créez un fichier `.env.local` à la racine du projet avec les variables suivantes :

```bash
NEXT_PUBLIC_SUPABASE_URL=votre_url_supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=votre_clé_anon_supabase
SUPABASE_SERVICE_ROLE_KEY=votre_clé_service_supabase
NEXT_PUBLIC_SITE_URL=url_de_votre_site
```

### Installation

1. Clonez le dépôt
2. Installez les dépendances : `npm install`
3. Initialisez la base de données : `npm run init-db`
4. Lancez le serveur de développement : `npm run dev`

## Déploiement

Le projet est configuré pour être déployé sur Vercel. Connectez votre dépôt GitHub à Vercel et configurez les variables d'environnement nécessaires.

## Contribution

Pour contribuer au projet :

1. Créez une branche pour votre fonctionnalité
2. Suivez les conventions de code établies
3. Testez vos modifications
4. Soumettez une pull request

## Licence

Tous droits réservés MOUVIK 2025
