"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { createBrowserClient } from '@/lib/supabase/client';
import type { Song } from '@/types';
import { But<PERSON> } from "@/components/ui/button";
import { PlayButton } from '@/components/audio/play-button';
import { LikeButton } from '@/components/social/like-button';
import AudioWaveformPreview from '@/components/audio-waveform-preview';
import { Eye, Heart, PlayCircle, Share2, Download } from 'lucide-react'; // Added Download icon

// Define props including the song data and server-fetched counts
interface SongViewClientWrapperProps {
  song: Song & { albums: { title: string } | null };
  serverLikesCount: number;
  serverPlaysCount: number;
  serverViewsCount: number;
}

export default function SongViewClientWrapper({ 
  song, 
  serverLikesCount,
  serverPlaysCount,
  serverViewsCount 
}: SongViewClientWrapperProps) {
  const supabase = createBrowserClient();
  const [userId, setUserId] = useState<string | undefined>(undefined);
  const [displayLikes, setDisplayLikes] = useState(serverLikesCount);
  // No need for isLoadingUserId as LikeButton handles its own loading/disabled state based on userId

  useEffect(() => {
    const fetchUserId = async () => {
      const { data: sessionData } = await supabase.auth.getSession();
      setUserId(sessionData?.session?.user?.id);
    };
    fetchUserId();
  }, [supabase]);

  const handleLikeToggle = (newIsLiked: boolean) => {
    setDisplayLikes(prev => newIsLiked ? prev + 1 : prev - 1);
  };

  return (
    <div className="flex flex-col h-full justify-between text-white"> {/* Ensure text is white for dark bg */}
      {/* Top section: Title, Artist, Stats */}
      <div>
        <h1 className="text-3xl md:text-4xl font-bold tracking-tight truncate" title={song.title || 'Morceau sans titre'}>
          {song.title || 'Morceau sans titre'}
        </h1>
        {song.user_id && song.artist_name ? (
          <Link href={`/profile/${song.user_id}`} className="mt-1 text-lg text-slate-300 hover:text-slate-100 hover:underline truncate" title={song.artist_name}>
            Par {song.artist_name}
          </Link>
        ) : (
          <p className="mt-1 text-lg text-slate-300 truncate" title={song.artist_name || 'Artiste inconnu'}>
            Par {song.artist_name || 'Artiste inconnu'}
          </p>
        )}
        
        {/* Stats Display - Integrated into header */}
        <div className="flex items-center gap-4 text-sm text-slate-400 mt-3 mb-1">
          <div className="flex items-center gap-1" title="Lectures">
            <PlayCircle className="h-4 w-4"/> 
            <span>{serverPlaysCount}</span>
          </div>
          <div className="flex items-center gap-1" title="Likes">
            <Heart className="h-4 w-4"/> 
            <span>{displayLikes}</span>
          </div>
          <div className="flex items-center gap-1" title="Vues">
            <Eye className="h-4 w-4"/> 
            <span>{serverViewsCount}</span>
          </div>
        </div>
      </div>

      {/* Middle section: Waveform Player - Removed wrapper styling for more breathing room */}
      <div className="my-4"> 
        {song.audio_url ? (
          <AudioWaveformPreview audioUrl={song.audio_url} song={song} height={80} />
        ) : (
          <div className="h-20 md:h-24 flex items-center justify-center text-slate-400 bg-slate-700/50 rounded-lg border border-slate-600">
            <p>Lecteur Waveform (Audio manquant)</p>
          </div>
        )}
      </div>

      {/* Bottom section: Action Buttons */}
      <div className="flex items-center gap-2 md:gap-3">
        <PlayButton song={song} size="lg" className="bg-orange-500 hover:bg-orange-600 text-white flex-grow md:flex-none md:px-6" /> 
        <LikeButton 
          resourceId={song.id} 
          resourceType="song" 
          initialLikes={displayLikes}
          userId={userId} 
          onLikeToggle={handleLikeToggle}
          size="lg"
          className="border-slate-600 hover:bg-slate-700 text-slate-300 hover:text-white"
        />
        <Button variant="outline" size="icon" title="Partager (Bientôt)" className="border-slate-600 hover:bg-slate-700 text-slate-300 hover:text-white">
          <Share2 className="h-5 w-5" />
        </Button>
        {song.audio_url && song.allow_downloads ? (
          <a href={song.audio_url} download target="_blank" rel="noopener noreferrer">
            <Button variant="outline" size="icon" title="Télécharger" className="border-slate-600 hover:bg-slate-700 text-slate-300 hover:text-white">
              <Download className="h-5 w-5" />
            </Button>
          </a>
        ) : (
          <Button variant="outline" size="icon" title="Téléchargement non autorisé" disabled className="border-slate-600 text-slate-500">
            <Download className="h-5 w-5" />
          </Button>
        )}
        {/* Edit button has been moved to the page level */}
      </div>
    </div>
  );
}
