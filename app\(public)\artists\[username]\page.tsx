import { createSupabaseServerClient } from "@/lib/supabase/server";
import { notFound } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Globe, CalendarDays, Music, Users, Edit, Heart, MessageCircle, Share2, Play, 
  ListMusic, DiscAlbum, UserCheck, Sparkles, BarChart3, LinkIcon as LucideLinkIcon, 
  PlayCircle, Film, Twitter, Instagram, Facebook, Youtube, Linkedin, Github, Twitch 
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { WaveformPlayer } from "@/components/audio/waveform-player";
// import { FollowButton } from "@/components/social/follow-button"; // À créer ou adapter
// import { LikeButton } from "@/components/social/like-button"; // À créer ou adapter

import { 
  rolePrimaryOptions as globalRolePrimaryOptions,
  countryOptions // Added import for countryOptions
  // Import other option lists if needed for displaying labels instead of raw values
} from '@/lib/constants/song-options';

// Types ENUM (align with EditProfilePage types if possible, or use string for flexibility)
export type RolePrimaryEnum = string; // Align with EditProfilePage
export type UserRoleEnum = 'user' | 'moderator' | 'admin';
export type SubscriptionEnum = 'free' | 'pro' | 'studio';
export type AiUsageEnum = 'none' | 'light' | 'moderate' | 'heavy';
export type MonetizationEnum = 'hobby' | 'streaming' | 'sync' | 'teaching';
export type DawEnum = string; // Align with EditProfilePage

// Interface for instruments_played, align with EditProfilePage
export interface InstrumentPlayed {
  name: string;
  experience_years: number;
}

interface Profile {
  id: string;
  username: string;
  display_name: string | null;
  avatar_url: string | null;
  header_url: string | null;
  bio: string | null;
  // location: string | null; // Old field
  location_city: string | null;
  location_country: string | null;
  website: string | null;
  genres: string[] | null;
  influences: string[] | null;
  tags: string[] | null; // Added
  social_links: { platform: string; url: string }[] | null;
  created_at: string;
  role_primary: RolePrimaryEnum | null;
  roles_secondary: string[] | null;
  // years_active: number | null; // Old field
  instruments_played: InstrumentPlayed[] | null; // New field
  operating_systems: string[] | null; // New field
  subscription_tier: SubscriptionEnum;
  user_role: UserRoleEnum | null;
  status_badges: string[] | null;
  main_instruments: string[] | null;
  // monetization_goals: MonetizationEnum | null; // To be hidden
  open_to_collab: boolean;
  primary_daw: DawEnum | null;
  other_daws: string[] | null;
  ai_usage_level: AiUsageEnum;
  ai_usage_percent: number | null;
  ai_tools: string[] | null;
  is_public?: boolean; 
  record_label?: string | null; 
  equipment?: string | null; 
  spoken_languages?: string[] | null; 
  // Removed unused/internal flags like notify_followers, add_to_discovery, initials, name
}

export default async function ArtistUsernameProfilePage({ params }: { params: { username: string } }) {
  const supabase = createSupabaseServerClient();
  const { data: { user: currentUser } } = await supabase.auth.getUser();

  const { data: profileData, error: profileError } = await supabase
    .from("profiles")
    .select("*")
    .ilike("username", params.username) 
    .single();

  if (profileError) {
    console.error(`Error fetching profile for username "${params.username}":`, profileError);
    notFound();
  }
  if (!profileData) {
    console.error(`No profile found for username "${params.username}".`);
    notFound();
  }
  const profile = profileData as Profile;

  const { data: totalPlaysData, error: totalPlaysError } = await supabase.rpc('get_total_plays_for_artist', { artist_id_param: profile.id });
  const totalPlays = typeof totalPlaysData === 'number' ? totalPlaysData : 0;
  if (totalPlaysError) console.error("Error fetching total plays:", totalPlaysError.message);

  const { data: totalSongsData, error: totalSongsError } = await supabase.rpc('get_total_songs_for_artist', { artist_id_param: profile.id });
  const totalSongsCount = typeof totalSongsData === 'number' ? totalSongsData : 0;
  if (totalSongsError) console.error("Error fetching total songs count:", totalSongsError.message);
  
  const { count: followersCount, error: followersError } = await supabase.from("follows").select("id", { count: "exact", head: true }).eq("following_id", profile.id);
  if (followersError) console.error("Error fetching followers count:", followersError.message);

  const { count: followingCount, error: followingError } = await supabase.from("follows").select("id", { count: "exact", head: true }).eq("follower_id", profile.id);
  if (followingError) console.error("Error fetching following count:", followingError.message);

  const { data: albumsData, error: albumsError } = await supabase.from("albums").select("id, title, cover_url, release_date, songs(count)").eq("artist_id", profile.id).order("release_date", { ascending: false });
  const albums = albumsData || [];
  if (albumsError) console.error("Error fetching albums:", albumsError.message);
  
  const { data: popularSongsData, error: popularSongsError } = await supabase.from("songs").select("id, title, plays, duration, albums(title, release_date), cover_url, audio_url, waveform_url, status, user_id, created_at, updated_at").eq("user_id", profile.id).eq("status", "published").order("plays", { ascending: false }).limit(5);
  const popularSongs = popularSongsData || [];
  if (popularSongsError) console.error("Error fetching popular songs:", popularSongsError.message);
  
  const { data: allSongsData, error: allSongsError } = await supabase.from("songs").select("id, title, plays, duration, albums(title, release_date), cover_url, audio_url, waveform_url, status, user_id, created_at, updated_at").eq("user_id", profile.id).eq("status", "published").order("created_at", { ascending: false });
  const allSongs = allSongsData || [];
  if (allSongsError) console.error("Error fetching all songs:", allSongsError.message);

  const getInitials = (name: string | null | undefined) => {
    if (!name) return "?";
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <div className="min-h-screen bg-slate-900 text-slate-100">
      <div className="h-60 md:h-80 bg-cover bg-center relative" style={{ backgroundImage: `url(${profile.header_url || '/placeholder-header.jpg'})` }}>
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>
        {profile.subscription_tier && profile.subscription_tier !== 'free' && (
          <Badge variant="secondary" className="absolute top-4 right-4 text-sm font-semibold py-1.5 px-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full shadow-xl">
            {profile.subscription_tier.toUpperCase()}
          </Badge>
        )}
      </div>

      <div className="container mx-auto max-w-screen-xl p-4 relative">
        <div className="absolute -top-20 md:-top-24 left-1/2 md:left-10 transform -translate-x-1/2 md:translate-x-0 z-10">
          <Avatar className="h-32 w-32 md:h-40 md:w-40 border-4 border-slate-900 ring-2 ring-primary shadow-lg">
            <AvatarImage src={profile.avatar_url || undefined} alt={profile.display_name || profile.username} />
            <AvatarFallback className="text-4xl bg-slate-700 text-slate-300">{getInitials(profile.display_name || profile.username)}</AvatarFallback>
          </Avatar>
        </div>

        <div className="flex flex-col md:flex-row gap-6 lg:gap-8 pt-16 md:pt-20">
          <aside className="w-full md:w-1/3 lg:w-1/4 xl:w-1/5 space-y-6 md:pt-20">
            <div className="space-y-2">
              <Button variant="outline" className="w-full bg-primary/10 border-primary text-primary hover:bg-primary/20">Suivre (Placeholder)</Button>
              <Button variant="outline" className="w-full bg-slate-700/50 border-slate-600 hover:bg-slate-600/50"><MessageCircle className="h-4 w-4 mr-2" /> Message</Button>
              {currentUser?.id === profile.id && (
                <Link href="/profile/edit" passHref className="block">
                  <Button variant="outline" size="sm" className="w-full bg-slate-700/50 border-slate-600 hover:bg-slate-600/50"><Edit className="h-4 w-4 mr-2" /> Modifier le profil</Button>
                </Link>
              )}
            </div>
            <Card className="bg-slate-800/70 border-slate-700 shadow-md">
              <CardHeader className="pb-2"><CardTitle className="text-lg font-semibold text-slate-200">Statistiques</CardTitle></CardHeader>
              <CardContent className="grid grid-cols-2 gap-4 text-center">
                <div><div className="text-2xl font-bold text-primary">{totalPlays.toLocaleString()}</div><div className="text-xs text-slate-400">ÉCOUTES</div></div>
                <div><div className="text-2xl font-bold text-primary">{(followersCount || 0).toLocaleString()}</div><div className="text-xs text-slate-400">ABONNÉS</div></div>
                <div><div className="text-2xl font-bold text-primary">{(followingCount || 0).toLocaleString()}</div><div className="text-xs text-slate-400">ABONNEMENTS</div></div>
                <div><div className="text-2xl font-bold text-primary">{totalSongsCount.toLocaleString()}</div><div className="text-xs text-slate-400">MORCEAUX</div></div>
              </CardContent>
            </Card>
            {profile.genres && profile.genres.length > 0 && (
              <Card className="bg-slate-800/70 border-slate-700 shadow-md">
                <CardHeader className="pb-2"><CardTitle className="text-lg font-semibold text-slate-200">Genres Musicaux</CardTitle></CardHeader>
                <CardContent className="flex flex-wrap gap-2">{profile.genres.map((genre: string) => (<Badge key={genre} variant="secondary" className="bg-slate-700 hover:bg-slate-600 text-slate-300 text-xs">{genre}</Badge>))}</CardContent>
              </Card>
            )}
            <Card className="bg-slate-800/70 border-slate-700 shadow-md">
              <CardHeader className="pb-3 pt-4"><CardTitle className="text-lg font-semibold text-slate-200">Activité Récente</CardTitle></CardHeader>
              <CardContent className="space-y-4 text-sm">
                <div className="flex items-start gap-3"><div className="bg-primary/10 text-primary p-2 rounded-full mt-1"><Music className="h-4 w-4" /></div><div><p className="text-slate-300">A publié un nouveau morceau <Link href="#" className="font-semibold text-primary hover:underline">"Cosmic Drift"</Link></p><p className="text-xs text-slate-400">il y a 2 jours</p></div></div>
                <div className="flex items-start gap-3"><div className="bg-primary/10 text-primary p-2 rounded-full mt-1"><DiscAlbum className="h-4 w-4" /></div><div><p className="text-slate-300">A sorti un nouvel album <Link href="#" className="font-semibold text-primary hover:underline">"Nebula Echoes"</Link></p><p className="text-xs text-slate-400">il y a 1 semaine</p></div></div>
                <div className="flex items-start gap-3"><div className="bg-primary/10 text-primary p-2 rounded-full mt-1"><Users className="h-4 w-4" /></div><div><p className="text-slate-300">A collaboré avec <Link href="#" className="font-semibold text-primary hover:underline">DJ Stardust</Link> sur "Galactic Groove"</p><p className="text-xs text-slate-400">il y a 2 semaines</p></div></div>
              </CardContent>
            </Card>
          </aside>

          <main className="w-full md:w-2/3 lg:w-3/4 xl:w-4/5 space-y-8">
            <div className="bg-slate-800/70 border border-slate-700 p-6 rounded-lg shadow-md">
              <div className="flex items-center gap-3">
                <h1 className="text-3xl md:text-4xl font-bold text-slate-100">{profile.display_name || profile.username}</h1>
                <Badge variant={ profile.subscription_tier === 'pro' || profile.subscription_tier === 'studio' ? 'default' : 'secondary'} className="capitalize whitespace-nowrap text-xs ml-2 py-0.5 px-2">
                  {profile.subscription_tier}
                  {profile.user_role === 'admin' && ' Admin'}
                </Badge>
                {/* <UserCheck className="h-6 w-6 text-blue-400 ml-2 opacity-50" /> Placeholder for verified badge */}
              </div>
              <p className="text-base text-slate-400 mt-1">
                @{profile.username} 
                {(profile.location_city || profile.location_country) && ` • `}
                {profile.location_city}
                {profile.location_city && profile.location_country ? ", " : ""}
                {profile.location_country && countryOptions.find(c => c.value === profile.location_country)?.label || profile.location_country}
              </p>
              {profile.bio && (<p className="mt-4 text-slate-300 whitespace-pre-line">{profile.bio}</p>)}
              <div className="mt-4 flex flex-wrap gap-x-6 gap-y-2 text-sm text-slate-400">
                {profile.website && (<Link href={profile.website} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1.5 hover:text-primary transition-colors"><LucideLinkIcon className="h-4 w-4" /> {profile.website.replace(/^https?:\/\//, '')}</Link>)}
                {profile.created_at && (<span className="flex items-center gap-1.5"><CalendarDays className="h-4 w-4" /> Membre depuis {formatDistanceToNow(new Date(profile.created_at), { addSuffix: true, locale: fr }).replace('environ ', '')}</span>)}
                {profile.record_label && (<span className="flex items-center gap-1.5"><DiscAlbum className="h-4 w-4" /> Label: {profile.record_label}</span>)}
              </div>
              <div className="mt-4 flex flex-wrap gap-3">
                {profile.social_links && profile.social_links.length > 0 ? (
                  profile.social_links.map(link => {
                    let IconComponent;
                    const platform = link.platform.toLowerCase();
                    if (platform.includes('twitter') || platform === 'x') IconComponent = Twitter;
                    else if (platform.includes('youtube')) IconComponent = Youtube;
                    else if (platform.includes('instagram')) IconComponent = Instagram;
                    else if (platform.includes('facebook')) IconComponent = Facebook;
                    else if (platform.includes('linkedin')) IconComponent = Linkedin;
                    else if (platform.includes('github')) IconComponent = Github;
                    else if (platform.includes('twitch')) IconComponent = Twitch;
                    else if (platform.includes('spotify')) IconComponent = Music; 
                    else if (platform.includes('soundcloud')) IconComponent = PlayCircle;
                    else if (platform.includes('bandcamp')) IconComponent = DiscAlbum;
                    else if (platform.includes('tiktok')) IconComponent = Film;
                    else if (platform.includes('udio')) IconComponent = Sparkles;
                    else if (platform.includes('suno')) IconComponent = Sparkles;
                    else IconComponent = LucideLinkIcon;
                    return (<Link href={link.url} key={link.platform} target="_blank" rel="noopener noreferrer" title={link.platform} className="text-slate-400 hover:text-primary transition-colors"><IconComponent className="h-5 w-5" /><span className="sr-only">{link.platform}</span></Link>);
                  })
                ) : (<p className="text-sm text-slate-500">Aucun lien social ajouté.</p>)}
              </div>
            </div>
            
            <Tabs defaultValue="overview" className="mt-8">
              <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-1 p-1 bg-slate-800 rounded-lg border border-slate-700">
                <TabsTrigger value="overview" className="rounded-md data-[state=active]:bg-primary data-[state=active]:text-slate-900 data-[state=inactive]:text-slate-300 data-[state=inactive]:hover:bg-slate-700/50">Populaires</TabsTrigger>
                <TabsTrigger value="albums" className="rounded-md data-[state=active]:bg-primary data-[state=active]:text-slate-900 data-[state=inactive]:text-slate-300 data-[state=inactive]:hover:bg-slate-700/50">Albums</TabsTrigger>
                <TabsTrigger value="tracks" className="rounded-md data-[state=active]:bg-primary data-[state=active]:text-slate-900 data-[state=inactive]:text-slate-300 data-[state=inactive]:hover:bg-slate-700/50">Morceaux</TabsTrigger>
                <TabsTrigger value="playlists" className="rounded-md data-[state=active]:bg-primary data-[state=active]:text-slate-900 data-[state=inactive]:text-slate-300 data-[state=inactive]:hover:bg-slate-700/50">Playlists</TabsTrigger>
                <TabsTrigger value="about" className="rounded-md data-[state=active]:bg-primary data-[state=active]:text-slate-900 data-[state=inactive]:text-slate-300 data-[state=inactive]:hover:bg-slate-700/50">À Propos</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-6 p-4 bg-slate-800/70 border border-slate-700 rounded-lg space-y-8">
                {/* Overview content remains largely the same, ensure data like popularSongs[0].albums.title is handled if albums is null */}
                <div className="bg-slate-800 border border-slate-700 p-4 sm:p-6 rounded-lg shadow-lg">
                  { popularSongs && popularSongs.length > 0 && popularSongs[0] ? (
                    <>
                      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3">
                        <h3 className="text-xl sm:text-2xl font-semibold text-slate-100">À la une : {popularSongs[0].title}</h3>
                        <div className="text-xs text-slate-400 mt-1 sm:mt-0">
                          {/* @ts-ignore popularSongs[0].albums might be null */}
                          {popularSongs[0].albums?.title ? `${popularSongs[0].albums.title} • ` : ''}
                          {/* @ts-ignore popularSongs[0].albums might be null */}
                          {popularSongs[0].albums?.release_date ? new Date(popularSongs[0].albums.release_date).getFullYear() : new Date(popularSongs[0].created_at).getFullYear()}
                        </div>
                      </div>
                      {popularSongs[0].audio_url ? (
                        <WaveformPlayer audioUrl={popularSongs[0].audio_url} height={100} waveColor="#475569" progressColor="#0ea5e9" />
                      ) : (
                        <div className="bg-slate-700 h-24 sm:h-28 flex items-center justify-center rounded-md my-4"><Music className="h-10 w-10 text-slate-500" /><p className="ml-4 text-slate-500 text-sm">Audio non disponible</p></div>
                      )}
                      <div className="flex items-center justify-end gap-2 mt-2">
                          <Button variant="outline" size="icon" className="bg-slate-700 border-slate-600 hover:bg-slate-600 h-8 w-8"><Heart className="h-4 w-4 text-slate-300" /></Button>
                          <Button variant="outline" size="icon" className="bg-slate-700 border-slate-600 hover:bg-slate-600 h-8 w-8"><Share2 className="h-4 w-4 text-slate-300" /></Button>
                      </div>
                    </>
                  ) : ( <div className="bg-slate-700/50 p-6 rounded-lg"><h3 className="text-2xl font-semibold mb-3 text-slate-100">Morceau à la Une</h3><div className="bg-slate-600/50 h-32 flex items-center justify-center rounded"><p className="text-slate-400">Aucun morceau populaire.</p></div></div> )}
                </div>
                
                {albums && albums.length > 0 && (
                  <div>
                    <div className="flex justify-between items-center mb-4"><h3 className="text-2xl font-semibold text-slate-100">Albums</h3><span className="text-sm text-primary cursor-pointer hover:underline">Voir tout</span></div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {albums.slice(0,3).map((album: any) => ( <Link href={`/albums/${album.id}`} key={album.id} className="group"><Card className="overflow-hidden transition-all hover:shadow-xl bg-slate-700/80 border-slate-600 hover:border-primary"><div className="aspect-square bg-slate-600 overflow-hidden"><Image src={album.cover_url || '/placeholder-cover.png'} alt={album.title || 'Album cover'} width={300} height={300} className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" /></div><CardContent className="p-3"><h4 className="font-semibold truncate text-slate-200 group-hover:text-primary">{album.title || 'Titre inconnu'}</h4><p className="text-xs text-slate-400">{album.release_date ? new Date(album.release_date).getFullYear() : 'N/A'}{/* @ts-ignore */}{album.songs && (album.songs as any[]).length > 0 && (album.songs as any[])[0].count ? ` • ${(album.songs as any[])[0].count} morceaux` : ''}</p></CardContent></Card></Link> ))}
                    </div>
                  </div>
                )}

                {popularSongs && popularSongs.length > 0 && (
                  <div>
                    <div className="flex justify-between items-center mb-4"><h3 className="text-2xl font-semibold text-slate-100">Morceaux Populaires</h3><span className="text-sm text-primary cursor-pointer hover:underline">Voir tout</span></div>
                    <div className="space-y-2">
                      {popularSongs.map((song: any, index: number) => ( <Card key={song.id} className="flex items-center p-3 gap-4 bg-slate-700/80 border-slate-600 hover:bg-slate-700/100 transition-colors"><span className="text-base text-slate-400 w-6 text-center font-medium">{index + 1}</span><Button variant="ghost" size="icon" className="text-primary hover:bg-primary/10 h-8 w-8"><Play className="h-5 w-5" /></Button><div className="flex-1 truncate"><p className="font-medium truncate text-slate-200 hover:text-primary">{song.title || 'Titre inconnu'}</p><p className="text-xs text-slate-400 truncate">
                        {/* @ts-ignore */}
                        {song.albums?.title || 'Single'}
                        </p></div><span className="text-sm text-slate-400 hidden md:inline">{(song.plays || 0).toLocaleString()}</span><span className="text-sm text-slate-400">{song.duration ? `${Math.floor(song.duration / 60)}:${(song.duration % 60).toString().padStart(2, '0')}` : '-:--'}</span></Card> ))}
                    </div>
                  </div>
                )}
                
                <div>
                    <div className="flex justify-between items-center mb-4"><h3 className="text-2xl font-semibold text-slate-100">Playlists</h3><span className="text-sm text-primary cursor-pointer hover:underline">Voir tout</span></div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {[1,2,3].map(p => ( <Card key={p} className="overflow-hidden bg-slate-700/80 border-slate-600"><div className="aspect-video bg-slate-600 flex items-center justify-center"><ListMusic className="h-12 w-12 text-slate-500"/></div><CardContent className="p-3"><h4 className="font-semibold truncate text-slate-200">Nom de la Playlist {p}</h4><p className="text-xs text-slate-400">X morceaux • Yh Zm</p><p className="text-xs text-slate-500 mt-1">Mis à jour il y a ...</p></CardContent></Card> ))}
                    </div>
                </div>
              </TabsContent>
              <TabsContent value="albums" className="mt-6 p-4 bg-slate-800/70 border border-slate-700 rounded-lg">
                {albums && albums.length > 0 ? ( <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">{albums.map((album: any) => ( <Link href={`/albums/${album.id}`} key={album.id} className="group"><Card className="overflow-hidden transition-all hover:shadow-xl bg-slate-700/80 border-slate-600 hover:border-primary"><div className="aspect-square bg-slate-600 overflow-hidden"><Image src={album.cover_url || '/placeholder-cover.png'} alt={album.title || 'Album cover'} width={300} height={300} className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" /></div><CardContent className="p-3"><h4 className="font-semibold truncate text-slate-200 group-hover:text-primary">{album.title || 'Titre inconnu'}</h4><p className="text-xs text-slate-400">{album.release_date ? new Date(album.release_date).getFullYear() : 'N/A'}{/* @ts-ignore */}{album.songs && (album.songs as any[]).length > 0 && (album.songs as any[])[0].count ? ` • ${(album.songs as any[])[0].count} morceaux` : ''}</p></CardContent></Card></Link> ))}</div> ) : ( <p className="text-slate-400 text-center py-8">Cet artiste n'a pas encore d'albums.</p> )}
              </TabsContent>
              <TabsContent value="tracks" className="mt-6 p-4 bg-slate-800/70 border border-slate-700 rounded-lg">
                {/* @ts-ignore allSongs should be in scope and is defined above */}
                {allSongs && allSongs.length > 0 ? ( <div className="space-y-2"><h3 className="text-2xl font-semibold text-slate-100 mb-4">Tous les Morceaux</h3>
                {/* @ts-ignore allSongs should be in scope and is defined above */}
                {allSongs.map((song: any, index: number) => ( <Card key={song.id} className="flex items-center p-3 gap-4 bg-slate-700/80 border-slate-600 hover:bg-slate-700/100 transition-colors"><Button variant="ghost" size="icon" className="text-primary hover:bg-primary/10 h-10 w-10"><Play className="h-5 w-5" /></Button>{song.cover_url && ( <Image src={song.cover_url} alt={song.title || 'Song cover'} width={40} height={40} className="rounded aspect-square object-cover"/> )}<div className="flex-1 truncate"><p className="font-medium truncate text-slate-200 hover:text-primary">{song.title || 'Titre inconnu'}</p><p className="text-xs text-slate-400 truncate">
                  {/* @ts-ignore */}
                  {song.albums?.title || 'Single'}
                  {/* @ts-ignore */}
                  {song.albums?.release_date ? ` • ${new Date(song.albums.release_date).getFullYear()}` : ` • ${new Date(song.created_at).getFullYear()}`}
                </p></div><span className="text-sm text-slate-400 hidden sm:inline">{(song.plays || 0).toLocaleString()} écoutes</span><span className="text-sm text-slate-400">{song.duration ? `${Math.floor(song.duration / 60)}:${(song.duration % 60).toString().padStart(2, '0')}` : '-:--'}</span><Button variant="ghost" size="icon" className="text-slate-400 hover:text-primary hover:bg-primary/10"><Heart className="h-4 w-4" /></Button></Card> ))}</div> ) : ( <p className="text-slate-400 text-center py-8">Cet artiste n'a pas encore publié de morceaux.</p> )}
              </TabsContent>
              <TabsContent value="playlists" className="mt-6 p-4 bg-slate-800/70 border border-slate-700 rounded-lg">
                <div className="text-center py-8"><ListMusic className="h-16 w-16 text-slate-500 mx-auto mb-4" /><h3 className="text-xl font-semibold text-slate-200 mb-2">Playlists en préparation !</h3><p className="text-slate-400">Cette section affichera bientôt les playlists de l'artiste.</p></div>
              </TabsContent>
              <TabsContent value="about" className="mt-6 p-6 bg-slate-800/70 border border-slate-700 rounded-lg space-y-8">
                <div className="pb-6 border-b border-slate-700">
                  <h3 className="text-2xl font-semibold text-slate-100 mb-4">Rôles et Compétences</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    {profile.role_primary && (<div><p className="text-slate-400 font-medium mb-1">Rôle Principal</p><Badge variant="default" className="bg-primary/80 text-primary-foreground text-base px-3 py-1">{globalRolePrimaryOptions.find(r => r.value === profile.role_primary)?.label || profile.role_primary}</Badge></div>)}
                    {profile.roles_secondary && profile.roles_secondary.length > 0 && (<div><p className="text-slate-400 font-medium mb-1">Rôles Secondaires</p><div className="flex flex-wrap gap-2">{profile.roles_secondary.map((role: string) => <Badge key={role} variant="secondary" className="bg-slate-700 text-slate-300">{role}</Badge>)}</div></div>)}
                    {profile.main_instruments && profile.main_instruments.length > 0 && (<div><p className="text-slate-400 font-medium mb-1">Instruments Principaux (Tags)</p><div className="flex flex-wrap gap-2">{profile.main_instruments.map((inst: string) => <Badge key={inst} variant="outline" className="border-slate-600 text-slate-300">{inst}</Badge>)}</div></div>)}
                    {profile.instruments_played && profile.instruments_played.length > 0 && (
                      <div className="md:col-span-2"><p className="text-slate-400 font-medium mb-1">Instruments Joués (Détails)</p>
                        <ul className="list-disc list-inside text-slate-300 space-y-1">
                          {profile.instruments_played.map((item, index) => (
                            <li key={index}>{item.name} ({item.experience_years} an{item.experience_years > 1 ? 's' : ''} d'expérience)</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="pb-6 border-b border-slate-700">
                  <h3 className="text-2xl font-semibold text-slate-100 mb-4">Parcours Musical</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    {/* Years active removed */}
                    {profile.influences && profile.influences.length > 0 && (<div><p className="text-slate-400 font-medium mb-1">Influences</p><div className="flex flex-wrap gap-2">{profile.influences.map((inf: string) => <Badge key={inf} variant="outline" className="border-slate-600 text-slate-300">{inf}</Badge>)}</div></div>)}
                  </div>
                </div>

                {(profile.primary_daw || (profile.other_daws && profile.other_daws.length > 0) || profile.equipment || (profile.operating_systems && profile.operating_systems.length > 0)) && (
                  <div className="pb-6 border-b border-slate-700">
                    <h3 className="text-2xl font-semibold text-slate-100 mb-4">Équipement et Logiciels</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                      {profile.primary_daw && (<div><p className="text-slate-400 font-medium mb-1">DAW Principal</p><Badge variant="default" className="bg-slate-600 text-slate-200">{profile.primary_daw}</Badge></div>)}
                      {profile.other_daws && profile.other_daws.length > 0 && (<div><p className="text-slate-400 font-medium mb-1">Autres DAWs</p><div className="flex flex-wrap gap-2">{profile.other_daws.map((daw: string) => <Badge key={daw} variant="outline" className="border-slate-600 text-slate-300">{daw}</Badge>)}</div></div>)}
                      {profile.operating_systems && profile.operating_systems.length > 0 && (<div><p className="text-slate-400 font-medium mb-1">Systèmes d'Exploitation</p><div className="flex flex-wrap gap-2">{profile.operating_systems.map((os: string) => <Badge key={os} variant="outline" className="border-slate-600 text-slate-300">{os}</Badge>)}</div></div>)}
                      {profile.equipment && (<div className="md:col-span-2"><p className="text-slate-400 font-medium mb-1">Équipement notable</p><p className="text-slate-300 whitespace-pre-line">{profile.equipment}</p></div>)}
                    </div>
                  </div>
                )}

                <div className="pb-6 border-b border-slate-700">
                  <h3 className="text-2xl font-semibold text-slate-100 mb-4">Approche avec l'Intelligence Artificielle</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    <div><p className="text-slate-400 font-medium mb-1">Niveau d'utilisation de l'IA</p><Badge variant={profile.ai_usage_level === 'none' ? 'outline' : 'default'} className={`capitalize ${ profile.ai_usage_level === 'light' ? 'bg-sky-500/20 text-sky-400 border-sky-500/30' : profile.ai_usage_level === 'moderate' ? 'bg-amber-500/20 text-amber-400 border-amber-500/30' : profile.ai_usage_level === 'heavy' ? 'bg-red-500/20 text-red-400 border-red-500/30' : 'border-slate-600 text-slate-300' }`}><Sparkles className="h-3 w-3 mr-1.5" />{profile.ai_usage_level || 'Non spécifié'}</Badge></div>
                    {profile.ai_usage_level !== 'none' && profile.ai_usage_percent !== null && (<div><p className="text-slate-400 font-medium mb-1">Pourcentage d'intégration IA</p><p className="text-slate-200 text-lg">{profile.ai_usage_percent}%</p></div>)}
                    {profile.ai_usage_level !== 'none' && profile.ai_tools && profile.ai_tools.length > 0 && (<div className="md:col-span-2"><p className="text-slate-400 font-medium mb-1">Outils IA Utilisés</p><div className="flex flex-wrap gap-2">{profile.ai_tools.map((tool: string) => <Badge key={tool} variant="outline" className="border-sky-700 text-sky-400">{tool}</Badge>)}</div></div>)}
                  </div>
                </div>

                <div className="pb-6">
                  <h3 className="text-2xl font-semibold text-slate-100 mb-4">Collaborations et Langues</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    <div><p className="text-slate-400 font-medium mb-1">Ouverture aux collaborations</p><Badge variant={profile.open_to_collab ? 'default' : 'outline'} className={profile.open_to_collab ? 'bg-green-500/80 text-white' : 'border-slate-600 text-slate-300'}>{profile.open_to_collab ? 'Ouvert(e)' : 'Fermé(e) actuellement'}</Badge></div>
                    {/* Monetization goals hidden */}
                    {profile.spoken_languages && profile.spoken_languages.length > 0 && (<div><p className="text-slate-400 font-medium mb-1">Langues parlées</p><p className="text-slate-300">{profile.spoken_languages.join(', ')}</p></div>)}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </main>
        </div>
      </div>
    </div>
  );
}
