import { createServerClient, type CookieOptions } from '@supabase/ssr'

export { createServerClient };
import { cookies } from 'next/headers'

// Factory function to create Supabase client for Server Components/Actions/Routes
export function createSupabaseServerClient() { 
  // Get the cookie store instance *inside* the factory
  // Explicitly assert the type to bypass faulty inference
  const cookieStore = cookies() as ReturnType<typeof cookies>

  // Return the configured client
  return createServerClient<any>( 
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
             // Optional: cookieStore.set({ name, value, ...options })
          } catch (error) {
            // Server Components cannot set cookies, ignore error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
             // Optional: cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // Server Components cannot set cookies, ignore error
          }
        },
      },
    }
  )
}
