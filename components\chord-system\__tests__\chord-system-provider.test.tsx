/**
 * 🎼 TESTS CHORD SYSTEM PROVIDER
 * 
 * Tests unitaires pour le provider principal et les hooks
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

import React from 'react';
import { renderHook, act, render, screen } from '@testing-library/react';
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ChordSystemProvider, useChordSystem } from '../providers/ChordSystemProvider';
import { useChordLibrary } from '../hooks/useChordLibrary';
import type { UnifiedChordPosition, ChordProgression } from '../types/chord-system';

// ============================================================================
// MOCKS ET UTILITAIRES
// ============================================================================

// Mock du ChordDataManager
jest.mock('../utils/ChordDataManager', () => ({
  ChordDataManager: {
    loadInstrument: jest.fn().mockResolvedValue({
      instrument: 'guitar',
      tuning: ['E', 'A', 'D', 'G', 'B', 'E'],
      strings: 6,
      keys: ['C', 'D', 'E'],
      suffixes: ['major', 'minor'],
      chords: {
        'C': [{
          suffix: 'major',
          name: 'Cmajor',
          positions: [{
            frets: ['x', 3, 2, 0, 1, 0],
            fingers: [0, 3, 2, 0, 1, 0],
            baseFret: 1,
            barres: [],
            difficulty: 'beginner'
          }]
        }]
      }
    }),
    clearCache: jest.fn(),
    getCacheStats: jest.fn().mockReturnValue({ size: 0, keys: [], memoryUsage: 0 })
  }
}));

// Wrapper pour les tests avec Provider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ChordSystemProvider>
    {children}
  </ChordSystemProvider>
);

// Données de test
const mockChord: UnifiedChordPosition = {
  id: 'test-chord-1',
  chord: 'Cmajor',
  instrument: 'guitar',
  tuning: 'standard',
  frets: ['x', 3, 2, 0, 1, 0],
  fingers: [0, 3, 2, 0, 1, 0],
  baseFret: 1,
  difficulty: 'beginner',
  createdAt: '2025-06-11T00:00:00.000Z',
  updatedAt: '2025-06-11T00:00:00.000Z'
};

const mockProgression: ChordProgression = {
  id: 'test-progression-1',
  name: 'Test Progression',
  chords: [mockChord],
  key: 'C',
  tempo: 120,
  timeSignature: '4/4',
  createdAt: '2025-06-11T00:00:00.000Z',
  updatedAt: '2025-06-11T00:00:00.000Z'
};

// ============================================================================
// TESTS DU PROVIDER
// ============================================================================

describe('ChordSystemProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('devrait fournir l\'état initial correct', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    expect(result.current.state).toMatchObject({
      selectedInstrument: 'guitar',
      selectedTuning: 'standard',
      selectedChords: [],
      currentProgression: null,
      isPlaying: false,
      currentChord: null,
      volume: 0.7,
      isMuted: false,
      playMode: 'chord',
      loading: false,
      error: null
    });
  });

  it('devrait permettre de changer d\'instrument', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    act(() => {
      result.current.actions.selectInstrument('piano');
    });

    expect(result.current.state.selectedInstrument).toBe('piano');
    expect(result.current.state.selectedTuning).toBe('standard');
  });

  it('devrait permettre de changer d\'accordage', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    act(() => {
      result.current.actions.selectTuning('drop_d');
    });

    expect(result.current.state.selectedTuning).toBe('drop_d');
  });

  it('devrait gérer la sélection d\'accords', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    act(() => {
      result.current.actions.selectChord(mockChord);
    });

    expect(result.current.state.currentChord).toEqual(mockChord);
  });

  it('devrait gérer l\'ajout d\'accords à la progression', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    act(() => {
      result.current.actions.addToProgression(mockChord);
    });

    expect(result.current.state.selectedChords).toContain(mockChord);
  });

  it('devrait gérer la suppression d\'accords de la progression', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    // Ajouter d'abord un accord
    act(() => {
      result.current.actions.addToProgression(mockChord);
    });

    expect(result.current.state.selectedChords).toHaveLength(1);

    // Puis le supprimer
    act(() => {
      result.current.actions.removeFromProgression(mockChord.id);
    });

    expect(result.current.state.selectedChords).toHaveLength(0);
  });

  it('devrait gérer la réorganisation des accords', () => {
    const chord2: UnifiedChordPosition = {
      ...mockChord,
      id: 'test-chord-2',
      chord: 'Dmajor'
    };

    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    // Ajouter deux accords
    act(() => {
      result.current.actions.addToProgression(mockChord);
      result.current.actions.addToProgression(chord2);
    });

    expect(result.current.state.selectedChords[0].chord).toBe('Cmajor');
    expect(result.current.state.selectedChords[1].chord).toBe('Dmajor');

    // Réorganiser
    act(() => {
      result.current.actions.reorderProgression(0, 1);
    });

    expect(result.current.state.selectedChords[0].chord).toBe('Dmajor');
    expect(result.current.state.selectedChords[1].chord).toBe('Cmajor');
  });

  it('devrait gérer les contrôles audio', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    // Test volume
    act(() => {
      result.current.actions.setVolume(0.5);
    });
    expect(result.current.state.volume).toBe(0.5);

    // Test mute
    act(() => {
      result.current.actions.toggleMute();
    });
    expect(result.current.state.isMuted).toBe(true);

    act(() => {
      result.current.actions.toggleMute();
    });
    expect(result.current.state.isMuted).toBe(false);
  });

  it('devrait gérer les erreurs', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    // Simuler une erreur via l'état interne
    act(() => {
      // Note: En production, les erreurs seraient gérées par les actions async
      result.current.state.error = 'Test error';
    });

    // Effacer l'erreur
    act(() => {
      result.current.actions.clearError();
    });

    expect(result.current.state.error).toBeNull();
  });

  it('devrait permettre de réinitialiser l\'état', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    // Modifier l'état
    act(() => {
      result.current.actions.selectInstrument('piano');
      result.current.actions.addToProgression(mockChord);
      result.current.actions.setVolume(0.3);
    });

    expect(result.current.state.selectedInstrument).toBe('piano');
    expect(result.current.state.selectedChords).toHaveLength(1);
    expect(result.current.state.volume).toBe(0.3);

    // Réinitialiser
    act(() => {
      result.current.actions.resetState();
    });

    expect(result.current.state.selectedInstrument).toBe('guitar');
    expect(result.current.state.selectedChords).toHaveLength(0);
    expect(result.current.state.volume).toBe(0.7);
  });
});

// ============================================================================
// TESTS DU HOOK useChordLibrary
// ============================================================================

describe('useChordLibrary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('devrait charger les accords pour l\'instrument sélectionné', async () => {
    const { result } = renderHook(() => useChordLibrary(), {
      wrapper: TestWrapper
    });

    // Attendre le chargement initial
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('devrait permettre de charger un instrument spécifique', async () => {
    const { result } = renderHook(() => useChordLibrary(), {
      wrapper: TestWrapper
    });

    await act(async () => {
      await result.current.loadInstrument('piano');
    });

    expect(result.current.isInstrumentLoaded('piano')).toBe(true);
  });

  it('devrait filtrer les accords selon les critères', () => {
    const { result } = renderHook(() => useChordLibrary(), {
      wrapper: TestWrapper
    });

    // Les accords filtrés devraient être un tableau
    expect(Array.isArray(result.current.filteredChords)).toBe(true);
  });

  it('devrait fournir des statistiques de cache', () => {
    const { result } = renderHook(() => useChordLibrary(), {
      wrapper: TestWrapper
    });

    const stats = result.current.getCacheStats();
    
    expect(stats).toHaveProperty('size');
    expect(stats).toHaveProperty('instruments');
    expect(Array.isArray(stats.instruments)).toBe(true);
  });

  it('devrait permettre de vider le cache', () => {
    const { result } = renderHook(() => useChordLibrary(), {
      wrapper: TestWrapper
    });

    act(() => {
      result.current.clearCache();
    });

    // Le cache devrait être vidé
    const stats = result.current.getCacheStats();
    expect(stats.size).toBe(0);
  });
});

// ============================================================================
// TESTS D'INTÉGRATION
// ============================================================================

describe('Intégration Provider + Hooks', () => {
  it('devrait synchroniser l\'état entre le provider et les hooks', async () => {
    const TestComponent = () => {
      const { state, actions } = useChordSystem();
      const { totalChords, isInstrumentLoaded } = useChordLibrary();

      return (
        <div>
          <div data-testid="instrument">{state.selectedInstrument}</div>
          <div data-testid="total-chords">{totalChords}</div>
          <div data-testid="is-loaded">{isInstrumentLoaded(state.selectedInstrument).toString()}</div>
          <button 
            data-testid="change-instrument"
            onClick={() => actions.selectInstrument('piano')}
          >
            Changer pour Piano
          </button>
        </div>
      );
    };

    render(
      <ChordSystemProvider>
        <TestComponent />
      </ChordSystemProvider>
    );

    // Vérifier l'état initial
    expect(screen.getByTestId('instrument')).toHaveTextContent('guitar');

    // Changer d'instrument
    act(() => {
      screen.getByTestId('change-instrument').click();
    });

    expect(screen.getByTestId('instrument')).toHaveTextContent('piano');
  });

  it('devrait gérer les erreurs de manière cohérente', async () => {
    // Mock d'une erreur de chargement
    const mockError = new Error('Erreur de test');
    jest.mocked(require('../utils/ChordDataManager').ChordDataManager.loadInstrument)
      .mockRejectedValueOnce(mockError);

    const { result } = renderHook(() => useChordLibrary(), {
      wrapper: TestWrapper
    });

    await act(async () => {
      await result.current.loadInstrument('guitar');
    });

    expect(result.current.error).toContain('Erreur chargement guitar');
  });
});

// ============================================================================
// TESTS DE PERFORMANCE
// ============================================================================

describe('Performance', () => {
  it('devrait gérer un grand nombre d\'accords sans ralentissement', () => {
    const { result } = renderHook(() => useChordSystem(), {
      wrapper: TestWrapper
    });

    const startTime = performance.now();

    // Ajouter 1000 accords
    act(() => {
      for (let i = 0; i < 1000; i++) {
        const chord: UnifiedChordPosition = {
          ...mockChord,
          id: `chord-${i}`,
          chord: `C${i}`
        };
        result.current.actions.addToProgression(chord);
      }
    });

    const endTime = performance.now();
    const duration = endTime - startTime;

    expect(result.current.state.selectedChords).toHaveLength(1000);
    expect(duration).toBeLessThan(1000); // Moins d'1 seconde
  });

  it('devrait optimiser les re-renders avec useMemo', () => {
    let renderCount = 0;

    const TestComponent = () => {
      renderCount++;
      const { state } = useChordSystem();
      return <div>{state.selectedInstrument}</div>;
    };

    const { rerender } = render(
      <ChordSystemProvider>
        <TestComponent />
      </ChordSystemProvider>
    );

    const initialRenderCount = renderCount;

    // Re-render sans changement d'état
    rerender(
      <ChordSystemProvider>
        <TestComponent />
      </ChordSystemProvider>
    );

    // Le nombre de renders ne devrait pas augmenter significativement
    expect(renderCount - initialRenderCount).toBeLessThanOrEqual(2);
  });
});
